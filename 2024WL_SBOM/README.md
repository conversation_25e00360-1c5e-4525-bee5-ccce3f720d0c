# 自动化漏洞监控和预警系统使用指南

## 系统概述

自动化漏洞监控和预警系统是一个用于收集、分析和管理开源软件漏洞信息的综合平台。该系统整合了多个数据源，支持漏洞数据采集、POC收集/生成、补丁跟踪等功能，并将数据以图数据库形式组织，实现漏洞数据的关联分析和可视化。

## 主要功能模块

### 1. 漏洞数据收集

本系统支持多源漏洞数据采集，包括NVD（美国国家漏洞数据库）、CNVD（中国国家信息安全漏洞共享平台）和CNNVD（中国国家漏洞数据库）等多个漏洞信息源。通过整合不同来源的漏洞数据，系统可以提供更全面的漏洞信息，并支持多维度的漏洞分析。

#### 1.1 CNVD漏洞数据爬虫

CNVD爬虫用于从中国国家信息安全漏洞共享平台（https://www.cnvd.org.cn/）采集漏洞信息，是系统中重要的数据来源之一。

**核心功能**：
- **异步编程实现高效爬取**：采用异步编程技术提高数据爬取效率
- **基于Playwright的自动化浏览器爬取**：使用Playwright自动化浏览器技术，有效绕过网站的反爬机制
- **自动处理521防护和cookie验证**：针对CNVD网站的特殊防护措施实现自动化处理
- **强大的错误处理和重试机制**：包含多级错误处理和自动重试，确保数据采集的稳定性
- **自动解析漏洞详情页内容**：智能提取漏洞标题、描述、解决方案等关键信息
- **结构化存储漏洞信息**：将采集的数据按标准结构保存到SQLite数据库

**数据库字段**：
- `cnvd_id`：CNVD漏洞编号，如"CNVD-2023-12345"
- `title`：漏洞标题
- `cve_id`：对应的CVE编号（如有）
- `level`：危害级别（高、中、低）
- `vulType`：漏洞类型
- `vendor`：厂商信息
- `product`：影响产品
- `disclosure_date`：公开日期
- `update_date`：更新时间
- `description`：漏洞详细描述
- `solution`：漏洞解决方案
- `reference_links`：参考链接

**使用示例**：
```bash
# 爬取CNVD第1页到第3页数据
python src/data_collection/main.py cnvd --start-page 1 --end-page 3 --sleep-time 5

# 使用自定义数据库路径
python src/data_collection/main.py cnvd --start-page 1 --end-page 3 --db-path /path/to/custom_db.db
```

**注意事项**：
- CNVD网站有严格的反爬措施，请设置合理的睡眠时间（建议至少4-5秒）
- 避免过快或过多的并发请求，以免IP被封禁
- 爬虫建议在有GUI环境的系统上运行，或在无头服务器上确保安装了所需的浏览器依赖

#### 1.2 CNNVD漏洞数据爬虫

CNNVD爬虫用于从中国国家漏洞数据库（https://www.cnnvd.org.cn/）采集漏洞信息，支持更灵活的采集策略和数据管理。

**核心功能**：
- **多线程采集模式**：支持单线程和多线程采集，可根据需求调整采集效率
- **自动处理网站的反爬机制**：内置多种反爬措施应对策略
- **断点续传和增量更新**：支持采集中断后的继续采集，避免重复工作
- **灵活的数据查询和导出**：支持按关键词搜索、按危害等级筛选，以及导出为JSON或CSV格式
- **完整的命令行接口**：提供丰富的子命令和参数，方便集成到自动化流程

**数据库字段**：
- `cnnvd_id`：CNNVD漏洞编号（主键）
- `vulName`：漏洞名称
- `cve_id`：对应的CVE编号
- `vulType`：漏洞类型
- `hazardLevel`：危害等级（1:严重; 2:高危; 3:中危; 4:低危）
- `vulDesc`：漏洞描述
- `referUrl`：参考链接
- `patch`：补丁信息
- `updateTime`：更新时间

**使用示例**：
```bash
# 爬取CNNVD第1页到第3页数据（多线程模式）
python src/data_collection/main.py cnnvd crawl --start-page 1 --end-page 3 --threads 5

# 搜索漏洞数据
python src/data_collection/main.py cnnvd search --keyword "Windows" --hazard-level 2

# 导出漏洞数据
python src/data_collection/main.py cnnvd export --output /path/to/output.json --format json
```

**参数说明**：
- `crawl`子命令：
  - `--start-page, -s`：起始页码，默认为1
  - `--end-page, -e`：结束页码，默认为3
  - `--threads, -t`：线程数量，默认为5
  - `--delay`：请求延迟(秒)，默认为1.0
- `search`子命令：
  - `--keyword, -k`：搜索关键词
  - `--hazard-level`：危害等级过滤
- `export`子命令：
  - `--output, -o`：导出文件路径
  - `--format, -f`：导出格式（json或csv）

**注意事项**：
- 默认线程数为5，过多的线程可能触发网站的防护机制
- 当前版本支持简单的断点续传，如果采集过程中断，再次启动时会自动跳过已采集的记录

### 2. POC收集和生成

漏洞验证代码(Proof of Concept, POC)是评估漏洞危害程度和可利用性的重要依据。本系统支持从多个来源收集POC，并在现有POC不可用时，利用大型语言模型(LLM)自动生成POC代码。

**核心功能**：
- **多源POC收集**：从ExploitDB、自定义数据库获取已公开的POC代码
- **自动化LLM生成**：当从公开渠道未找到POC时，调用LLM API自动生成可用的POC代码
- **多种存储方式**：支持将POC保存到SQLite数据库、JSON文件和Neo4j图数据库
- **完整元数据**：保存POC的相关信息，包括来源、作者、发布日期、利用难度等
- **自动关联**：建立漏洞和POC之间的关联关系，便于后续分析

**ExploitDB爬虫优化**：
- 从Selenium迁移到Requests，提高Docker环境兼容性
- 改进HTML解析机制，增加多层次的解析方法
- 实现更可靠的直接搜索方法，支持多URL尝试和高级解析
- 优化代理配置，解决Docker环境中的网络访问问题

**数据库结构**：
```sql
CREATE TABLE poc (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    poc_id TEXT UNIQUE,
    exploit_db_id TEXT,
    cve_id TEXT,
    description TEXT,
    code_snippet TEXT,
    complexity TEXT,
    exploit_difficulty TEXT,
    publication_date TEXT,
    author TEXT,
    platform TEXT,
    url TEXT,
    source TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

**Neo4j图谱结构**：
- **节点类型**：
  - `Vulnerability`：代表CVE漏洞
  - `Exploit`：代表漏洞利用代码(POC)
  - `Reference`：代表参考链接
  - `Package`：代表受影响的软件包
- **关系**：
  - `(Vulnerability)-[:HAS_POC]->(Exploit)`：漏洞有POC
  - `(Vulnerability)-[:HAS_REFERENCE]->(Reference)`：漏洞有参考链接
  - `(Vulnerability)-[:AFFECTS]->(Package)`：漏洞影响软件包

**使用示例**：
```bash
# 测试单个CVE的POC收集
python src/data_collection/main.py test-poc --cve-ids CVE-2021-44228

# 测试多个CVE的POC收集
python src/data_collection/main.py test-poc --cve-ids CVE-2021-44228,CVE-2021-45046

# 收集POC并保存到JSON文件
python src/data_collection/main.py test-poc --cve-ids CVE-2021-44228 --save-poc-to-json

# 使用LLM生成POC并保存到Neo4j图数据库
python src/data_collection/main.py test-poc --cve-ids CVE-2021-44228 --use-llm-for-poc --save-poc-to-neo4j
```

**可用参数**：
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--cve-ids` | 要收集POC的CVE ID列表，用逗号分隔 | 无 |
| `--use-llm-for-poc` | 当从其他来源未找到POC时，使用LLM生成 | False |
| `--save-poc-to-json` | 将收集到的POC保存为JSON文件 | False |
| `--save-poc-to-neo4j` | 将收集到的POC保存到Neo4j图数据库 | False |
| `--force-poc-fetch` | 强制从网络获取POC，忽略缓存 | False |
| `--poc-timeout` | 网络请求超时时间(秒) | 60 |
| `--poc-db-path` | SQLite数据库路径 | /home/<USER>/data/poc/exploitDetailed.db |
| `--poc-json-output-dir` | JSON输出目录 | /home/<USER>/data/poc/json |

**代理配置**：
在Docker环境中使用代理时，需要特别注意：
- Windows/macOS中使用`host.docker.internal`访问宿主机代理
- Linux中使用Docker网桥IP（通常是`**********`）

### 3. 补丁跟踪功能

补丁跟踪器是一个专门用于自动化发现CVE漏洞相关补丁的工具。通过分析CVE的引用链接，构建引用网络，并使用智能算法识别和评估潜在的补丁，该工具能够帮助安全研究人员和软件维护者快速定位漏洞修复代码。

**核心功能**：
- **自动获取多源引用**：从NVD、GitHub、红帽等多个来源自动获取CVE引用链接
- **引用网络构建**：解析引用之间的关系，构建完整的引用网络图
- **智能补丁识别算法**：基于多种特征识别潜在补丁，计算置信度评分
- **官方仓库映射**：将非GitHub URL（如git.openssl.org等）智能映射到对应的GitHub官方仓库
- **等价补丁搜索增强**：通过仓库映射提高等价补丁查找的准确性，优先使用官方仓库而非fork
- **批量处理能力**：支持时间范围内的CVE批量补丁跟踪
- **优先级排序机制**：对引用进行优先级排序，优先处理可能包含补丁的引用
- **结果可视化**：生成引用网络图和详细分析报告

**输出结果**：
补丁跟踪结果将保存在`/home/<USER>/data/patch`目录中（除非指定了其他路径），包括：
- `trace_result.json`：完整的跟踪结果，包括所有发现的补丁、置信度、网络统计等
- `reference_network.json`：引用网络的完整数据，包括节点、边和相关元数据
- `reference_network.png`：引用网络的可视化图，展示节点之间的关系
- `high_confidence_patches.json`：仅包含高置信度补丁的列表
- `timerange_summary.json`：时间范围处理的汇总报告（批量模式）

**参数详解**：
- **必选参数**（二选一）：
  - `--cve, -c`：单个CVE ID（例如：CVE-2021-44228）
  - `--start-date`：批量模式起始日期（YYYY-MM-DD）
- **时间范围参数**：
  - `--end-date`：结束日期，默认为今天
  - `--date-type`：日期类型，可选值：published(默认)、modified
  - `--max-cves`：最多处理的CVE数量
  - `--newest-first`：优先处理最新的CVE
- **补丁筛选参数**：
  - `--confidence`：补丁置信度阈值，默认0.7（范围0-1）
  - `--no-priority`：禁用引用优先级排序
  - `--min-priority`：最低包含的优先级，可选值：critical、high、medium(默认)、low、very_low
- **基本配置参数**：
  - `--data-dir, -d`：数据保存目录
  - `--depth`：最大引用深度，默认为3
  - `--delay`：请求延迟(秒)，默认为0.5秒
  - `--github-token`：GitHub API令牌（同时用于仓库映射模块和API搜索）
  - `--no-verify-ssl`：禁用SSL证书验证
  - `--retries`：请求重试次数，默认3次

**使用示例**：
```bash
# 单个CVE补丁跟踪
python src/data_collection/main.py patch-trace --cve CVE-2021-44228

# 时间范围补丁跟踪
python src/data_collection/main.py patch-trace --start-date 2023-01-01 --end-date 2023-01-31

# 高级使用 - 调整跟踪深度和置信度
python src/data_collection/main.py patch-trace --cve CVE-2021-44228 --depth 4 --confidence 0.6

# 限制处理数量并优先处理最新的CVE
python src/data_collection/main.py patch-trace --start-date 2023-01-01 --max-cves 10 --newest-first

# 禁用优先级排序，使用更宽松的优先级筛选
python src/data_collection/main.py patch-trace --cve CVE-2021-44228 --no-priority --min-priority low
```

**仓库映射功能**：
系统内置了一个强大的仓库映射模块，能够将非GitHub URL（如git.openssl.org、git.kernel.org等）智能映射到对应的GitHub官方仓库：

- **多维度映射策略**：结合域名映射、URL模式匹配和项目名称识别
- **优先非fork仓库**：在排序算法中对非fork仓库施加高权重，优先选择官方仓库
- **commit验证机制**：验证commit在官方仓库中是否存在，确保映射准确性
- **可扩展映射表**：内置200+常见开源项目的官方仓库映射，支持动态更新

此功能显著提升了等价补丁搜索的准确性和全面性，尤其对Linux内核、OpenSSL等项目的补丁跟踪更加精准。

**注意事项**：
- GitHub API存在速率限制，建议提供GitHub令牌以提高限制（从每小时60次提高到5000次）
- 处理单个CVE通常需要几分钟，跟踪深度越大耗时越长
- 首次运行时会创建缓存，后续使用`--use-cache`可加快处理速度
- 对于较冷门或未映射的开源项目，系统会退化为使用GitHub API搜索查找等价补丁

### 4. 漏洞风险评估功能

漏洞风险评估功能是自动化漏洞监控和预警系统的重要组成部分，用于全面评估特定CVE漏洞的风险程度。该功能基于多维数据分析，综合考虑CVSS评分、EPSS利用概率评分、POC可获得性、影响范围等因素，生成综合风险等级和风险评分，并提供相应的处置建议。

**核心特点**：
- **多维度风险评估**：综合考虑技术影响、利用难度、POC可获得性等多个维度
- **量化风险指标**：提供风险评分和风险等级的量化表示
- **利用Neo4j图数据库**：通过图数据分析评估漏洞在供应链中的传播风险
- **自动生成处置建议**：根据风险评估结果，自动生成针对性的处置建议
- **详细的评估报告**：生成结构化的评估报告，便于进一步分析和决策

**参数说明**：
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--cve` | 要评估的CVE ID，例如：CVE-2021-44228 | 无（必需参数） |
| `--output` | 风险评估结果输出目录 | /home/<USER>/data/risk_assessment |
| `--graph-url` | 图数据库连接URL | bolt://localhost:7687 |
| `--graph-user` | 图数据库用户名 | neo4j |
| `--graph-password` | 图数据库密码 | password |
| `--poc-db-path` | POC数据库路径 | /home/<USER>/data/poc/exploitDetailed.db |
| `--no-verify-ssl` | 禁用SSL证书验证 | False |

**使用示例**：
```bash
# 基本用法 - 评估指定CVE的风险
python src/data_collection/main.py assess-risk --cve CVE-2021-44228

# 自定义输出目录
python src/data_collection/main.py assess-risk --cve CVE-2021-44228 --output /path/to/custom/output

# 自定义图数据库连接
python src/data_collection/main.py assess-risk --cve CVE-2021-44228 --graph-url bolt://neo4j-server:7687 --graph-user neo4j --graph-password mypassword

# 跳过SSL验证（适用于内部测试环境）
python src/data_collection/main.py assess-risk --cve CVE-2021-44228 --no-verify-ssl
```

**输出结果说明**：
风险评估结果将以JSON格式保存到指定输出目录，文件名格式为`{CVE_ID}_risk_assessment_{timestamp}.json`。主要字段包括：

- `cve_id`：评估的CVE ID
- `risk_level`：综合风险等级（"严重"、"高危"、"中危"、"低危"）
- `risk_score`：风险评分（0-10之间的浮点数）
- `cvss_score`：CVSS基础评分
- `epss_score`：EPSS利用概率评分
- `has_poc`：是否存在已知POC
- `poc_details`：POC详细信息
- `affected_packages`：受影响的软件包列表
- `direct_affected_count`：直接受影响的组件数量
- `indirect_affected_count`：间接受影响的组件数量
- `recommendation`：处置建议

**风险等级说明**：
| 风险等级 | 风险评分范围 | 说明 |
|---------|------------|------|
| 严重 | 9.0 - 10.0 | 漏洞可被远程攻击，利用简单，影响范围广，存在公开POC，应立即处置 |
| 高危 | 7.0 - 8.9 | 漏洞可被远程攻击，利用较简单，影响较大，可能存在POC，应优先处置 |
| 中危 | 4.0 - 6.9 | 漏洞利用难度中等或影响有限，建议在常规维护周期内处置 |
| 低危 | 0.1 - 3.9 | 漏洞利用难度高或影响小，可在低优先级维护周期内处置 |
| 无风险 | 0 | 漏洞不适用或无实际风险 |

**注意事项**：
1. 使用该功能前请确保已部署Neo4j图数据库，并已导入相关漏洞数据
2. 确保POC数据库文件存在，否则POC相关风险评估将无法进行
3. 对于较新的CVE，可能因缺乏EPSS评分而使用估算值
4. 风险评估结果仅供参考，具体处置方案应结合实际业务场景制定
5. 评估过程需要访问外部API（如EPSS API），请确保网络连接正常，必要时配置代理

## 安装配置

### 基本环境要求
- Python 3.8+
- Docker（如果使用容器运行）
- Neo4j数据库（用于存储关系数据）

### 安装步骤
1. 克隆代码仓库
2. 创建虚拟环境并安装依赖：
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   pip install -r requirements.txt
   ```
3. 配置环境变量（复制`.env.example`到`.env`并填写必要配置）
4. 安装Neo4j数据库（可选）

### 代理配置
在网络受限环境中，可以通过代理访问外部资源：

```bash
# 直接运行时
python3 src/data_collection/scripts/collect_poc.py --cve CVE-2021-44228 --proxy http://127.0.0.1:10809

# Docker容器中运行
python3 src/data_collection/scripts/collect_poc.py --cve CVE-2021-44228 --proxy http://host.docker.internal:10809
```

## 系统架构

### 主要模块
- **数据采集模块**：负责从各数据源收集漏洞信息
- **POC处理模块**：收集和生成漏洞利用代码
- **补丁跟踪模块**：
  - **引用网络构建器**：构建漏洞引用关系图
  - **补丁识别器**：从引用网络中识别补丁
  - **仓库映射模块**：将非GitHub URL映射到官方GitHub仓库
  - **等价补丁查找器**：基于映射和GitHub API发现等价补丁
- **风险评估模块**：综合分析漏洞风险并提供处置建议

## 数据存储说明

### SQLite数据库
- CNVD数据：`/home/<USER>/data/cnvd_vulnerabilities.db`
- CNNVD数据：`/home/<USER>/data/cnnvd_data.db`
- POC数据：`/home/<USER>/data/poc/exploitDetailed.db`

### Neo4j图数据库
系统使用Neo4j存储实体和关系数据，主要实体包括：
- Vulnerability（漏洞）
- Exploit（POC）
- Reference（引用）
- Package（软件包）

### 文件输出
- POC JSON文件：`/home/<USER>/data/poc/json/`
- 补丁跟踪结果：`/home/<USER>/data/patch/`
- 风险评估结果：`/home/<USER>/data/risk_assessment/`

## 常见问题处理

### 1. 网络连接问题
- 使用`--no-verify-ssl`选项禁用SSL验证
- 增加重试次数和请求延迟
- 配置合适的代理服务器

### 2. 数据采集问题
- CNVD/CNNVD爬虫触发反爬机制：增加请求间隔，减少并发数
- POC采集失败：检查网络连接，尝试使用LLM生成

### 3. 数据库访问错误
- 检查数据库路径和权限
- 确保Neo4j服务正在运行
- 验证连接配置（URI、用户名、密码）

## 技术支持

如需更多帮助，请参考源代码文档注释或联系项目维护人员。 