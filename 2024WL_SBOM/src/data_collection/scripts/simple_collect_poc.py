#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版POC收集器：从ExploitDB获取POC，使用模拟数据进行测试
使用方法:
    python simple_collect_poc.py --cve CVE-2021-44228 [--proxy http://host:port] [--mock]
"""

import os
import sys
import argparse
import logging
import json
import time
import random
from typing import Dict, Optional

# 添加项目根目录到sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..')))

# 使用完全限定的导入路径
from src.data_collection.services.exploit_db_collector import ExploitDBCollector

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('simple_collect_poc')

def setup_arg_parser() -> argparse.ArgumentParser:
    """设置参数解析器"""
    parser = argparse.ArgumentParser(description='简化版POC收集器')
    
    parser.add_argument('--cve', required=True, help='CVE ID，例如CVE-2021-44228')
    parser.add_argument('--proxy', help='HTTP代理，例如 http://127.0.0.1:8080')
    parser.add_argument('--socks-proxy', help='SOCKS代理，例如 socks5://127.0.0.1:1080')
    parser.add_argument('--mock', action='store_true', help='使用模拟数据，用于测试')
    parser.add_argument('--verbose', action='store_true', help='显示详细日志')
    parser.add_argument('--timeout', type=int, default=30, help='请求超时时间（秒）')
    parser.add_argument('--output', help='输出JSON文件路径')
    
    return parser

def configure_proxies(args) -> Dict:
    """配置代理设置"""
    proxies = {}
    
    if args.proxy:
        logger.info(f"使用HTTP代理: {args.proxy}")
        proxies['http'] = args.proxy
        proxies['https'] = args.proxy
        
    if args.socks_proxy:
        logger.info(f"使用SOCKS代理: {args.socks_proxy}")
        proxies['socks'] = args.socks_proxy
        
    return proxies

def save_to_json(poc_data: Dict, output_path: str) -> None:
    """保存POC数据到JSON文件"""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(poc_data, f, ensure_ascii=False, indent=2)
        logger.info(f"已将POC数据保存到 {output_path}")
    except Exception as e:
        logger.error(f"保存JSON时出错: {str(e)}")

def process_cve(cve_id: str, args) -> Optional[Dict]:
    """处理单个CVE ID"""
    logger.info(f"处理CVE ID: {cve_id}")
    
    # 配置代理
    proxies = configure_proxies(args)
    
    try:
        # 初始化ExploitDB收集器
        collector = ExploitDBCollector(
            proxies=proxies,
            timeout=args.timeout,
            mock_mode=args.mock,
            verbose=args.verbose
        )
        
        # 获取POC
        poc_data = collector.fetch_poc_by_cve(cve_id)
        
        if poc_data:
            logger.info(f"成功获取到 {cve_id} 的POC")
            
            # 保存到JSON文件
            if args.output:
                save_to_json(poc_data, args.output)
                
            return poc_data
        else:
            logger.warning(f"未找到 {cve_id} 的POC")
            return None
            
    except Exception as e:
        logger.error(f"处理 {cve_id} 时出错: {str(e)}")
        return None

def main():
    """主函数"""
    parser = setup_arg_parser()
    args = parser.parse_args()
    
    # 设置详细日志模式
    if args.verbose:
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            handler.setLevel(logging.DEBUG)
        logger.debug("详细模式已启用")
    
    # 处理CVE
    cve_id = args.cve
    poc_data = process_cve(cve_id, args)
    
    if poc_data:
        print(f"\n成功获取 {cve_id} 的POC信息:")
        print(f"EDB-ID: {poc_data.get('edb_id', 'N/A')}")
        print(f"标题: {poc_data.get('title', 'N/A')}")
        print(f"作者: {poc_data.get('author', 'N/A')}")
        print(f"日期: {poc_data.get('date', 'N/A')}")
        print(f"平台: {poc_data.get('platform', 'N/A')}")
        print(f"类型: {poc_data.get('type', 'N/A')}")
        poc_code = poc_data.get('poc_code', '')
        print(f"POC代码长度: {len(poc_code)} 字符")
        print(f"POC代码片段: {poc_code[:150]}..." if len(poc_code) > 150 else poc_code)
    else:
        print(f"\n未能获取 {cve_id} 的POC信息")

if __name__ == "__main__":
    main() 