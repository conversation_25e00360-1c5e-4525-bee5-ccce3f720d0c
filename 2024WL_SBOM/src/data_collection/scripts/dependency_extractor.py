#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
组件依赖关系提取脚本

从package.json、requirements.txt、Gemfile等文件中提取组件之间的依赖关系，
并在Neo4j中建立DEPENDS_ON关系。
"""

import os
import sys
import json
import re
from typing import Dict, List, Any, Tuple, Set, Optional
import glob
import subprocess

# 修复导入路径问题
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

# 导入需要的模块
from src.services.graph_store import GraphStore
from src.models.graph import GraphNode, NodeType, RelationType, GraphRelation
from src.utils.logger import setup_logger, logger

def extract_npm_dependencies(repo_path: str) -> List[Dict[str, Any]]:
    """
    从package.json文件中提取NPM依赖关系
    
    Args:
        repo_path: 仓库路径
        
    Returns:
        依赖关系列表
    """
    deps = []
    
    # 查找所有package.json文件
    package_files = glob.glob(os.path.join(repo_path, '**/package.json'), recursive=True)
    
    for pkg_file in package_files:
        try:
            with open(pkg_file, 'r', encoding='utf-8') as f:
                pkg_data = json.load(f)
                
            # 获取包名
            pkg_name = pkg_data.get('name')
            if not pkg_name:
                continue
                
            # 依赖类型及其对应的是否为开发依赖
            dep_types = {
                'dependencies': False,
                'devDependencies': True,
                'peerDependencies': False,
                'optionalDependencies': False
            }
            
            # 处理各种依赖类型
            for dep_type, is_dev in dep_types.items():
                deps_dict = pkg_data.get(dep_type, {})
                
                for dep_name, version in deps_dict.items():
                    deps.append({
                        'source': pkg_name,
                        'target': dep_name,
                        'version_constraint': version,
                        'is_dev_dependency': is_dev,
                        'ecosystem': 'npm'
                    })
                    
        except Exception as e:
            logger.error(f"处理{pkg_file}时出错: {str(e)}")
            
    return deps

def extract_pip_dependencies(repo_path: str) -> List[Dict[str, Any]]:
    """
    从requirements.txt文件中提取pip依赖关系
    
    Args:
        repo_path: 仓库路径
        
    Returns:
        依赖关系列表
    """
    deps = []
    
    # 查找所有requirements.txt文件
    req_files = glob.glob(os.path.join(repo_path, '**/requirements*.txt'), recursive=True)
    
    # 提取依赖版本的正则表达式
    version_re = re.compile(r'^([a-zA-Z0-9_.-]+)(.*?)$')
    
    for req_file in req_files:
        try:
            # 确定项目名称，可以使用目录名称
            project_dir = os.path.dirname(req_file)
            project_name = os.path.basename(project_dir)
            
            # 读取requirements.txt
            with open(req_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                        
                    # 移除注释
                    line = line.split('#')[0].strip()
                    
                    # 解析依赖名称和版本
                    match = version_re.match(line)
                    if match:
                        dep_name = match.group(1)
                        version = match.group(2).strip()
                        
                        deps.append({
                            'source': project_name,
                            'target': dep_name,
                            'version_constraint': version,
                            'is_dev_dependency': 'dev' in req_file or 'test' in req_file,
                            'ecosystem': 'pypi'
                        })
                        
        except Exception as e:
            logger.error(f"处理{req_file}时出错: {str(e)}")
            
    return deps

def extract_maven_dependencies(repo_path: str) -> List[Dict[str, Any]]:
    """
    从pom.xml文件中提取Maven依赖关系
    
    Args:
        repo_path: 仓库路径
        
    Returns:
        依赖关系列表
    """
    deps = []
    
    # 查找所有pom.xml文件
    pom_files = glob.glob(os.path.join(repo_path, '**/pom.xml'), recursive=True)
    
    for pom_file in pom_files:
        try:
            # 使用Maven命令解析依赖（需要安装Maven）
            # 如果无法使用Maven命令，可以使用XML解析库解析pom.xml
            project_dir = os.path.dirname(pom_file)
            
            # 尝试获取groupId和artifactId
            project_name = None
            try:
                import xml.etree.ElementTree as ET
                tree = ET.parse(pom_file)
                root = tree.getroot()
                
                # 获取命名空间
                ns = {'m': 'http://maven.apache.org/POM/4.0.0'}
                
                # 获取groupId和artifactId
                group_id = root.find('m:groupId', ns)
                artifact_id = root.find('m:artifactId', ns)
                
                if group_id is not None and artifact_id is not None:
                    project_name = f"{group_id.text}:{artifact_id.text}"
                else:
                    # 使用目录名作为备选
                    project_name = os.path.basename(project_dir)
                    
                # 解析依赖
                dependencies = root.findall('.//m:dependency', ns)
                for dep in dependencies:
                    dep_group_id = dep.find('m:groupId', ns)
                    dep_artifact_id = dep.find('m:artifactId', ns)
                    dep_version = dep.find('m:version', ns)
                    dep_scope = dep.find('m:scope', ns)
                    
                    if dep_group_id is not None and dep_artifact_id is not None:
                        deps.append({
                            'source': project_name,
                            'target': f"{dep_group_id.text}:{dep_artifact_id.text}",
                            'version_constraint': dep_version.text if dep_version is not None else '',
                            'is_dev_dependency': dep_scope is not None and dep_scope.text in ['test', 'provided'],
                            'ecosystem': 'maven'
                        })
                        
            except Exception as e:
                logger.error(f"解析pom.xml失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"处理{pom_file}时出错: {str(e)}")
            
    return deps

def create_depends_on_relations(graph: GraphStore, dependencies: List[Dict[str, Any]]) -> int:
    """
    在Neo4j中创建DEPENDS_ON关系
    
    Args:
        graph: 图数据库存储服务实例
        dependencies: 依赖关系列表
        
    Returns:
        成功创建的关系数量
    """
    if not dependencies:
        return 0
        
    if graph.driver is None:
        logger.error("未连接到图数据库")
        return 0
        
    # 先检查Component节点是否存在，没有的话创建
    component_ids = set()
    for dep in dependencies:
        component_ids.add(dep['source'])
        component_ids.add(dep['target'])
        
    # 检查并创建缺失的Component节点
    for comp_id in component_ids:
        node_props = {
            'name': comp_id,
            'ecosystem': next((d['ecosystem'] for d in dependencies if d['source'] == comp_id or d['target'] == comp_id), 'unknown'),
            'description': f"Auto-generated component: {comp_id}"
        }
        
        node = graph.get_node(NodeType.COMPONENT, comp_id)
        if not node:
            # 创建节点
            node = GraphNode(
                id=comp_id,
                node_type=NodeType.COMPONENT,
                properties=node_props
            )
            graph.create_node(node)
            logger.debug(f"创建组件节点: {comp_id}")
    
    # 创建DEPENDS_ON关系
    relations = []
    for dep in dependencies:
        relation = GraphRelation(
            source_id=dep['source'],
            target_id=dep['target'],
            relation_type=RelationType.DEPENDS_ON,
            properties={
                'version_constraint': dep['version_constraint'],
                'is_dev_dependency': dep['is_dev_dependency'],
                'ecosystem': dep['ecosystem']
            }
        )
        relations.append(relation)
    
    # 批量创建关系
    if relations:
        success_count = graph.batch_create_relations(relations)
        logger.info(f"批量创建DEPENDS_ON关系: 成功 {success_count}/{len(relations)}")
        return success_count
    
    return 0

def main():
    """主函数"""
    # 设置日志
    setup_logger()
    logger.info("开始提取组件依赖关系...")
    
    # 添加命令行参数解析
    import argparse
    parser = argparse.ArgumentParser(description='提取组件依赖关系并建立DEPENDS_ON关系')
    parser.add_argument('--repo-path', required=True, help='仓库路径')
    parser.add_argument('--neo4j-uri', default="bolt://localhost:7687", help='Neo4j URI')
    parser.add_argument('--neo4j-user', default="neo4j", help='Neo4j用户名')
    parser.add_argument('--neo4j-password', default="password", help='Neo4j密码')
    
    args = parser.parse_args()
    
    repo_path = args.repo_path
    neo4j_uri = args.neo4j_uri
    neo4j_user = args.neo4j_user
    neo4j_password = args.neo4j_password
    
    if not os.path.exists(repo_path):
        logger.error(f"仓库路径不存在: {repo_path}")
        return
    
    # 连接图数据库
    logger.info(f"连接图数据库: {neo4j_uri}")
    
    try:
        graph = GraphStore(neo4j_uri, neo4j_user, neo4j_password)
        
        # 验证连接
        if not graph.is_connected():
            logger.error("无法连接到Neo4j图数据库，请检查配置")
            return
            
        logger.info("成功连接到Neo4j图数据库")
    except Exception as e:
        logger.error(f"连接Neo4j图数据库失败: {str(e)}")
        return
    
    try:
        # 提取依赖关系
        dependencies = []
        
        # NPM依赖
        npm_deps = extract_npm_dependencies(repo_path)
        if npm_deps:
            logger.info(f"提取到 {len(npm_deps)} 个NPM依赖关系")
            dependencies.extend(npm_deps)
        
        # Pip依赖
        pip_deps = extract_pip_dependencies(repo_path)
        if pip_deps:
            logger.info(f"提取到 {len(pip_deps)} 个Pip依赖关系")
            dependencies.extend(pip_deps)
        
        # Maven依赖
        maven_deps = extract_maven_dependencies(repo_path)
        if maven_deps:
            logger.info(f"提取到 {len(maven_deps)} 个Maven依赖关系")
            dependencies.extend(maven_deps)
        
        # 创建DEPENDS_ON关系
        if dependencies:
            logger.info(f"共提取到 {len(dependencies)} 个依赖关系")
            rel_count = create_depends_on_relations(graph, dependencies)
            logger.info(f"成功创建 {rel_count} 个DEPENDS_ON关系")
        else:
            logger.warning("未提取到任何依赖关系")
        
    except Exception as e:
        logger.error(f"处理依赖关系时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
    finally:
        # 关闭数据库连接
        if graph and graph.driver:
            graph.close()
            
        logger.info("脚本执行结束")

if __name__ == "__main__":
    main() 