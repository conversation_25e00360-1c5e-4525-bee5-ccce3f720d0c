#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
漏洞数据分析脚本

分析数据库中的漏洞数据，生成统计信息和报告。
"""

import os
import sys
import sqlite3
import datetime
import logging
from pathlib import Path
from collections import Counter
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 检查是否安装了可选依赖
try:
    import matplotlib.pyplot as plt
    import pandas as pd
    import numpy as np
    HAS_PLOTTING = True
except ImportError:
    logger.warning("未安装matplotlib或pandas库，将跳过图表生成和CSV导出")
    HAS_PLOTTING = False

def get_db_path():
    """获取数据库路径"""
    data_dir = os.path.join(project_root, 'data')
    # 确保数据目录存在
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    return os.path.join(data_dir, 'vulnerabilities.db')

def get_overall_stats():
    """获取总体统计信息"""
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    stats = {}
    try:
        # 总漏洞数量
        cursor.execute("SELECT COUNT(*) as count FROM vulnerabilities")
        stats['total_vulnerabilities'] = cursor.fetchone()['count']
        
        # 按数据源统计
        cursor.execute("SELECT source, COUNT(*) as count FROM vulnerabilities GROUP BY source")
        stats['by_source'] = {row['source']: row['count'] for row in cursor.fetchall()}
        
        # 按严重程度统计
        cursor.execute("SELECT severity, COUNT(*) as count FROM vulnerabilities GROUP BY severity")
        stats['by_severity'] = {row['severity']: row['count'] for row in cursor.fetchall()}
        
        # 按状态统计
        cursor.execute("SELECT status, COUNT(*) as count FROM vulnerabilities GROUP BY status")
        stats['by_status'] = {row['status']: row['count'] for row in cursor.fetchall()}
        
        # 按年份统计
        cursor.execute("""
            SELECT strftime('%Y', published_date) as year, COUNT(*) as count 
            FROM vulnerabilities 
            WHERE published_date IS NOT NULL
            GROUP BY year
            ORDER BY year
        """)
        stats['by_year'] = {row['year']: row['count'] for row in cursor.fetchall()}
        
        # 受影响软件包统计
        cursor.execute("""
            SELECT p.name, COUNT(vp.vulnerability_id) as vuln_count
            FROM packages p
            JOIN vulnerability_package vp ON p.id = vp.package_id
            GROUP BY p.name
            ORDER BY vuln_count DESC
            LIMIT 20
        """)
        stats['top_affected_packages'] = {row['name']: row['vuln_count'] for row in cursor.fetchall()}
        
        # 参考链接来源统计
        cursor.execute("""
            SELECT r.source, COUNT(*) as count
            FROM [references] r
            GROUP BY r.source
            ORDER BY count DESC
            LIMIT 20
        """)
        stats['top_reference_sources'] = {row['source']: row['count'] for row in cursor.fetchall()}
        
        return stats
    
    finally:
        conn.close()

def get_recent_vulnerabilities(days=30):
    """获取最近的漏洞信息"""
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # 计算日期范围
        end_date = datetime.datetime.now().isoformat()
        start_date = (datetime.datetime.now() - datetime.timedelta(days=days)).isoformat()
        
        # 查询最近的漏洞
        cursor.execute("""
            SELECT v.vuln_id, v.title, v.source, v.severity, v.published_date, v.status
            FROM vulnerabilities v
            WHERE v.published_date BETWEEN ? AND ?
            ORDER BY v.published_date DESC
            LIMIT 50
        """, (start_date, end_date))
        
        return [dict(row) for row in cursor.fetchall()]
    
    finally:
        conn.close()

def get_high_severity_stats():
    """获取高危漏洞统计信息"""
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    stats = {}
    try:
        # 高危漏洞总数
        cursor.execute("""
            SELECT COUNT(*) as count 
            FROM vulnerabilities 
            WHERE severity IN ('CRITICAL', 'HIGH', 'critical', 'high')
        """)
        stats['total_high_severity'] = cursor.fetchone()['count']
        
        # 按数据源统计高危漏洞
        cursor.execute("""
            SELECT source, COUNT(*) as count 
            FROM vulnerabilities 
            WHERE severity IN ('CRITICAL', 'HIGH', 'critical', 'high')
            GROUP BY source
        """)
        stats['by_source'] = {row['source']: row['count'] for row in cursor.fetchall()}
        
        # 高危漏洞最多的软件包
        cursor.execute("""
            SELECT p.name, COUNT(vp.vulnerability_id) as vuln_count
            FROM packages p
            JOIN vulnerability_package vp ON p.id = vp.package_id
            JOIN vulnerabilities v ON vp.vulnerability_id = v.id
            WHERE v.severity IN ('CRITICAL', 'HIGH', 'critical', 'high')
            GROUP BY p.name
            ORDER BY vuln_count DESC
            LIMIT 10
        """)
        stats['top_high_severity_packages'] = {row['name']: row['vuln_count'] for row in cursor.fetchall()}
        
        return stats
    
    finally:
        conn.close()

def print_stats(stats):
    """打印统计信息"""
    print("\n========== 漏洞数据统计 ==========")
    
    print(f"\n总漏洞数量: {stats['total_vulnerabilities']}")
    
    print("\n按数据源统计:")
    for source, count in stats['by_source'].items():
        print(f"  {source}: {count}条")
    
    print("\n按严重程度统计:")
    for severity, count in stats['by_severity'].items():
        if severity:  # 过滤None值
            print(f"  {severity}: {count}条")
    
    print("\n按状态统计:")
    for status, count in stats['by_status'].items():
        if status:  # 过滤None值
            print(f"  {status}: {count}条")
    
    print("\n按年份统计:")
    for year, count in stats['by_year'].items():
        print(f"  {year}: {count}条")
    
    print("\n受影响软件包Top 10:")
    for i, (pkg, count) in enumerate(list(stats['top_affected_packages'].items())[:10]):
        print(f"  {i+1}. {pkg}: {count}条漏洞")
    
    print("\n参考链接来源Top 10:")
    for i, (source, count) in enumerate(list(stats['top_reference_sources'].items())[:10]):
        if source:  # 过滤None值
            print(f"  {i+1}. {source}: {count}条")

def export_to_json(data, filename):
    """导出数据到JSON文件"""
    output_dir = os.path.join(project_root, 'reports')
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    filepath = os.path.join(output_dir, filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"数据已导出至: {filepath}")
    return filepath

def export_to_csv(data, filename):
    """导出数据到CSV文件"""
    if not HAS_PLOTTING:
        logger.warning("未安装pandas，无法导出CSV")
        return None
        
    output_dir = os.path.join(project_root, 'reports')
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    filepath = os.path.join(output_dir, filename)
    
    # 如果是字典列表，直接转换为DataFrame
    if isinstance(data, list) and all(isinstance(item, dict) for item in data):
        df = pd.DataFrame(data)
    # 如果是嵌套字典，进行转换
    elif isinstance(data, dict):
        # 将嵌套字典转换为扁平结构
        flat_data = []
        for key, value in data.items():
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    flat_data.append({
                        'category': key,
                        'name': sub_key,
                        'count': sub_value
                    })
            else:
                flat_data.append({
                    'category': 'overall',
                    'name': key,
                    'count': value
                })
        df = pd.DataFrame(flat_data)
    else:
        logger.error(f"不支持的数据类型: {type(data)}")
        return None
    
    df.to_csv(filepath, index=False, encoding='utf-8')
    logger.info(f"数据已导出至: {filepath}")
    return filepath

def generate_plots(stats):
    """生成图表"""
    if not HAS_PLOTTING:
        logger.warning("未安装matplotlib，无法生成图表")
        return []
        
    output_dir = os.path.join(project_root, 'reports', 'plots')
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 1. 按数据源统计的饼图
    plt.figure(figsize=(10, 6))
    plt.pie(
        stats['by_source'].values(), 
        labels=stats['by_source'].keys(),
        autopct='%1.1f%%',
        startangle=90
    )
    plt.title('按数据源统计的漏洞分布')
    plt.axis('equal')
    pie_chart_path = os.path.join(output_dir, 'by_source_pie.png')
    plt.savefig(pie_chart_path)
    plt.close()
    
    # 2. 按年份统计的柱状图
    plt.figure(figsize=(12, 6))
    years = list(stats['by_year'].keys())
    counts = list(stats['by_year'].values())
    plt.bar(years, counts)
    plt.title('按年份统计的漏洞数量')
    plt.xlabel('年份')
    plt.ylabel('漏洞数量')
    plt.xticks(rotation=45)
    bar_chart_path = os.path.join(output_dir, 'by_year_bar.png')
    plt.savefig(bar_chart_path)
    plt.close()
    
    # 3. 受影响软件包Top 10的横向柱状图
    plt.figure(figsize=(12, 8))
    top_pkgs = dict(list(stats['top_affected_packages'].items())[:10])
    packages = list(top_pkgs.keys())
    counts = list(top_pkgs.values())
    
    # 反转列表使最大值在顶部
    packages.reverse()
    counts.reverse()
    
    plt.barh(packages, counts)
    plt.title('受影响软件包Top 10')
    plt.xlabel('漏洞数量')
    plt.ylabel('软件包名称')
    plt.tight_layout()
    pkg_chart_path = os.path.join(output_dir, 'top_packages_bar.png')
    plt.savefig(pkg_chart_path)
    plt.close()
    
    logger.info(f"图表已生成至: {output_dir}")
    return [pie_chart_path, bar_chart_path, pkg_chart_path]

def main():
    """主函数"""
    logger.info("开始漏洞数据分析...")
    
    # 1. 获取总体统计信息
    stats = get_overall_stats()
    print_stats(stats)
    
    # 2. 导出数据到文件
    export_to_json(stats, 'vulnerability_stats.json')
    if HAS_PLOTTING:
        export_to_csv(stats, 'vulnerability_stats.csv')
    
    # 3. 获取最近的漏洞信息
    recent_vulns = get_recent_vulnerabilities(days=90)
    export_to_json(recent_vulns, 'recent_vulnerabilities.json')
    if HAS_PLOTTING:
        export_to_csv(recent_vulns, 'recent_vulnerabilities.csv')
    
    # 4. 获取高危漏洞统计
    high_severity_stats = get_high_severity_stats()
    export_to_json(high_severity_stats, 'high_severity_stats.json')
    
    # 5. 生成图表
    if HAS_PLOTTING:
        generate_plots(stats)
    
    logger.info("漏洞数据分析完成")

if __name__ == "__main__":
    main() 