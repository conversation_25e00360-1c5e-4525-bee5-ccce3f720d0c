#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
POC收集器：从ExploitDB获取POC，如果失败则使用LLM生成，并存储到Neo4j
使用方法:
    python collect_poc.py --cve CVE-2021-44228 [--db-path exploitDetailed.db] [--proxy http://host:port]
"""

import os
import sys
import argparse
import logging
import sqlite3
import random
import time
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径以导入模块
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

# 直接导入模块（绝对导入）
from src.data_collection.services.playwright_exploit_db_collector import PlaywrightExploitDBCollector
from src.data_collection.services.generetors.poc_llmgenerator import PocGenerator

# 检查是否安装了neo4j模块
try:
    from neo4j import GraphDatabase
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    print("警告: Neo4j模块未安装，无法使用Neo4j功能")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', 'poc_collection.log'), 'a')
    ]
)
logger = logging.getLogger('collect_poc')

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# ExploitDB URL常量
EXPLOITDB_BASE_URL = "https://www.exploit-db.com"
EXPLOITDB_SEARCH_URL = f"{EXPLOITDB_BASE_URL}/"

# SQLite 数据库函数
def ensure_sqlite_db(db_path: str) -> None:
    """确保 SQLite 数据库和表存在"""
    try:
        # 确保目录存在
        db_dir = os.path.dirname(db_path)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)
            logger.debug(f"确保数据库目录存在: {db_dir}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建 exploitDetail 表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS exploitDetail (
                EDB_ID int PRIMARY KEY,
                Date TEXT,
                V INT,
                Title TEXT,
                Type TEXT,
                Platform TEXT,
                Author TEXT,
                CVE TEXT,
                POC TEXT
            )
        ''')
        
        # 创建 llm_poc 表（存储LLM生成的POC，没有EDB-ID）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS llm_poc (
                ID INTEGER PRIMARY KEY AUTOINCREMENT,
                CVE_ID TEXT UNIQUE,
                Date TEXT,
                Title TEXT,
                Type TEXT,
                Platform TEXT,
                Author TEXT,
                POC TEXT,
                Severity TEXT,
                Score REAL
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.debug(f"已确保数据库 {db_path} 和相关表存在")
    except sqlite3.Error as e:
        logger.error(f"初始化 SQLite 数据库出错: {str(e)}")
        raise

def save_poc_to_sqlite(poc_data: Dict, db_path: str) -> bool:
    """
    将POC数据保存到SQLite数据库
    
    参数:
        poc_data: POC数据
        db_path: SQLite数据库路径
        
    返回:
        是否成功保存
    """
    logger.info(f"将 POC 数据保存到 SQLite ({db_path})")
    
    try:
        # 确保数据库和表存在
        ensure_sqlite_db(db_path)
        
        # 插入数据
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已存在
        cursor.execute("SELECT EDB_ID FROM exploitDetail WHERE EDB_ID = ?", (poc_data['edb_id'],))
        if cursor.fetchone():
            logger.info(f"EDB-ID {poc_data['edb_id']} 已存在于数据库中，更新")
            cursor.execute("""
                UPDATE exploitDetail SET 
                Date = ?, Title = ?, Type = ?, Platform = ?, Author = ?, CVE = ?, POC = ?
                WHERE EDB_ID = ?
            """, (
                poc_data.get('date', ''),
                poc_data.get('title', ''),
                poc_data.get('type', ''),
                poc_data.get('platform', ''),
                poc_data.get('author', ''),
                poc_data.get('cve_id', ''),
                poc_data.get('poc_code', ''),
                poc_data['edb_id']
            ))
        else:
            logger.info(f"插入新的 EDB-ID {poc_data['edb_id']}")
            cursor.execute("""
                INSERT INTO exploitDetail (EDB_ID, Date, V, Title, Type, Platform, Author, CVE, POC)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                poc_data['edb_id'],
                poc_data.get('date', ''),
                1,  # Version 默认为1
                poc_data.get('title', ''),
                poc_data.get('type', ''),
                poc_data.get('platform', ''),
                poc_data.get('author', ''),
                poc_data.get('cve_id', ''),
                poc_data.get('poc_code', '')
            ))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"保存POC到SQLite时出错: {str(e)}")
        return False

def try_get_poc_from_sqlite(cve_id: str, db_path: str) -> Optional[Dict]:
    """
    尝试从SQLite获取POC
    
    参数:
        cve_id: CVE ID
        db_path: SQLite数据库路径
        
    返回:
        格式化的POC数据或None
    """
    logger.info(f"尝试从 SQLite ({db_path}) 获取 {cve_id} 的 POC")
    
    # 确保数据库和表存在
    ensure_sqlite_db(db_path)
    
    # 查询数据库
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
        cursor = conn.cursor()
        
        # 查询匹配CVE_ID的记录
        cursor.execute("SELECT * FROM exploitDetail WHERE CVE = ?", (cve_id,))
        rows = cursor.fetchall()
        conn.close()
        
        if not rows:
            logger.info(f"在数据库中未找到 {cve_id} 的POC")
            return None
            
        # 转换为字典格式
        result = {}
        for row in rows:
            row_dict = dict(row)
            
            result = {
                'cve_id': row_dict['CVE'],
                'edb_id': row_dict['EDB_ID'],
                'date': row_dict['Date'],
                'title': row_dict['Title'],
                'type': row_dict['Type'],
                'platform': row_dict['Platform'],
                'author': row_dict['Author'],
                'poc_code': row_dict['POC'],
                'source': 'exploitdb'
            }
            break  # 只使用第一条记录
            
        logger.info(f"成功从数据库中获取到 {cve_id} 的POC")
        return result
    except Exception as e:
        logger.error(f"从SQLite获取POC时出错: {str(e)}")
        return None

def generate_poc_with_llm(cve_id: str, use_nvd_api: bool = True, proxies: Dict = None) -> Optional[Dict]:
    """
    使用LLM生成POC
    
    参数:
        cve_id: CVE ID
        use_nvd_api: 是否使用NVD API获取CVE详情
        proxies: 代理设置
        
    返回:
        生成的POC数据或None
    """
    logger.info(f"尝试为 {cve_id} 生成POC")
    
    # 保存原始环境变量
    original_http_proxy = os.environ.get('HTTP_PROXY')
    original_https_proxy = os.environ.get('HTTPS_PROXY')
    
    try:
        # 设置代理环境变量
        if proxies:
            if 'http' in proxies and proxies['http']:
                logger.debug(f"设置HTTP_PROXY环境变量: {proxies['http']}")
                os.environ['HTTP_PROXY'] = proxies['http']
                
            if 'https' in proxies and proxies['https']:
                logger.debug(f"设置HTTPS_PROXY环境变量: {proxies['https']}")
                os.environ['HTTPS_PROXY'] = proxies['https']
        
        # 初始化POC生成器
        generator = PocGenerator()
        
        # 获取CVE详情
        cve_details = None
        if use_nvd_api:
            logger.info(f"使用NVD API获取 {cve_id} 详情")
            cve_details = generator.get_cve_details(cve_id)
        
        if not cve_details:
            # 如果无法获取详情，创建最小详情集
            logger.warning(f"无法获取 {cve_id} 的详情，将使用最小信息集")
            cve_details = {
                'id': cve_id,
                'description': f"Vulnerability details for {cve_id}",
                'severity': "UNKNOWN",
                'base_score': 0.0,
                'references': [],
                'published': datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                'last_modified': datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                'weaknesses': []
            }
        
        # 生成POC代码
        logger.info(f"开始为 {cve_id} 生成POC代码")
        poc_code = generator.generate_poc(cve_details)
        
        if not poc_code:
            logger.error(f"为 {cve_id} 生成POC代码失败")
            return None
        
        # 构建结果数据
        result = {
            'cve_id': cve_id,
            'edb_id': None,  # LLM生成的POC没有EDB-ID
            'date': datetime.now().strftime("%Y-%m-%d"),
            'title': f"LLM Generated POC for {cve_id}",
            'type': "LLM Generated",
            'platform': "Multiple",
            'author': "LLM",
            'poc_code': poc_code,
            'source': 'llm',
            'severity': cve_details.get('severity', 'UNKNOWN'),
            'base_score': cve_details.get('base_score', 0.0)
        }
        
        logger.info(f"为 {cve_id} 生成POC成功")
        
        return result
    
    except Exception as e:
        logger.error(f"生成POC时出错: {str(e)}")
        return None
    
    finally:
        # 恢复原始环境变量
        if original_http_proxy:
            os.environ['HTTP_PROXY'] = original_http_proxy
        elif 'HTTP_PROXY' in os.environ:
            del os.environ['HTTP_PROXY']
            
        if original_https_proxy:
            os.environ['HTTPS_PROXY'] = original_https_proxy
        elif 'HTTPS_PROXY' in os.environ:
            del os.environ['HTTPS_PROXY']

def save_llm_poc_to_sqlite(poc_data: Dict, db_path: str) -> bool:
    """
    将LLM生成的POC保存到SQLite
    
    参数:
        poc_data: POC数据
        db_path: SQLite数据库路径
        
    返回:
        是否成功保存
    """
    logger.info(f"将LLM生成的POC保存到SQLite ({db_path})")
    
    try:
        # 确保数据库和表存在
        ensure_sqlite_db(db_path)
        
        # 插入数据
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已存在
        cursor.execute("SELECT ID FROM llm_poc WHERE CVE_ID = ?", (poc_data['cve_id'],))
        result = cursor.fetchone()
        
        if result:
            logger.info(f"CVE {poc_data['cve_id']} 已存在于LLM POC表中，更新")
            cursor.execute("""
                UPDATE llm_poc SET 
                Date = ?, Title = ?, Type = ?, Platform = ?, Author = ?, POC = ?, Severity = ?, Score = ?
                WHERE CVE_ID = ?
            """, (
                poc_data.get('date', ''),
                poc_data.get('title', ''),
                poc_data.get('type', ''),
                poc_data.get('platform', ''),
                poc_data.get('author', ''),
                poc_data.get('poc_code', ''),
                poc_data.get('severity', 'UNKNOWN'),
                poc_data.get('base_score', 0.0),
                poc_data['cve_id']
            ))
        else:
            logger.info(f"插入新的CVE {poc_data['cve_id']}到LLM POC表")
            cursor.execute("""
                INSERT INTO llm_poc (CVE_ID, Date, Title, Type, Platform, Author, POC, Severity, Score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                poc_data['cve_id'],
                poc_data.get('date', ''),
                poc_data.get('title', ''),
                poc_data.get('type', ''),
                poc_data.get('platform', ''),
                poc_data.get('author', ''),
                poc_data.get('poc_code', ''),
                poc_data.get('severity', 'UNKNOWN'),
                poc_data.get('base_score', 0.0)
            ))
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        logger.error(f"保存LLM POC到SQLite时出错: {str(e)}")
        return False

def save_exploitdb_poc_list_to_neo4j(exploitdb_poc_list: List[Dict]) -> bool:
    """
    将一批 ExploitDB POC 数据批量保存到 Neo4j
    
    参数:
        exploitdb_poc_list: 包含多个 ExploitDB POC 数据字典的列表
        
    返回:
        是否成功保存 (至少部分成功)
    """
    if not exploitdb_poc_list:
        logger.info("没有 ExploitDB POC 数据需要保存到 Neo4j")
        return True # 没有数据也算成功完成
        
    # --- 修改: 日志反映批量操作 --- 
    cve_id_example = exploitdb_poc_list[0].get('cve_id', '未知CVE')
    logger.info(f"批量将 {len(exploitdb_poc_list)} 个 {cve_id_example} 的 ExploitDB POC 保存到 Neo4j")
    
    if not NEO4J_AVAILABLE:
        logger.error("Neo4j模块未安装，无法保存")
        return False
    
    # --- 修改: 只建立一次连接 --- 
    driver = None
    try:
        uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        username = os.getenv("NEO4J_USER", "neo4j")
        password = os.getenv("NEO4J_PASSWORD", "password")
        driver = GraphDatabase.driver(uri, auth=(username, password))
        
        with driver.session() as session:
            # --- 修改: 使用 UNWIND 进行批量处理 --- 
            query = """
            UNWIND $poc_batch AS poc // 将列表展开为单独的行，每行是一个 poc 字典
            
            // 合并 Vulnerability 节点 (基于 CVE)
            MERGE (v:Vulnerability {cve_id: poc.cve_id})
            ON CREATE SET v.title = poc.title // 仅在创建时设置标题 (避免覆盖)
            
            // 合并 Exploit 节点 (基于 EDB ID)
            MERGE (e:Exploit {source: 'exploitdb', edb_id: poc.edb_id})
            ON CREATE SET // 仅在创建时设置属性
                e.code = poc.poc_code,
                e.author = poc.author,
                e.date = poc.date,
                e.title = poc.title,
                e.type = poc.type,
                e.platform = poc.platform,
                e.cve_id = poc.cve_id // 在 Exploit 节点上也存储 cve_id
            ON MATCH SET // 如果节点已存在，可以选择性更新某些属性
                e.code = poc.poc_code, // 总是更新代码?
                e.title = poc.title, 
                e.date = poc.date 
                
            // 合并关系
            MERGE (v)-[r:HAS_POC]->(e)
            
            RETURN count(*) AS processed_count // 返回处理的记录数
            """
            
            # 准备参数列表，确保 edb_id 是字符串 (或根据需要是数字)
            poc_batch_params = []
            for poc_data in exploitdb_poc_list:
                 # 确保核心字段存在
                if not poc_data.get('cve_id') or not poc_data.get('edb_id'):
                     logger.warning(f"跳过缺少 cve_id 或 edb_id 的 POC 数据: {poc_data.get('edb_id')}")
                     continue
                     
                poc_batch_params.append({
                    'cve_id': poc_data['cve_id'],
                    'edb_id': str(poc_data['edb_id']), # 保持字符串类型?
                    'title': poc_data.get('title', ''),
                    'poc_code': poc_data.get('poc_code', ''),
                    'author': poc_data.get('author', ''),
                    'date': poc_data.get('date', ''),
                    'type': poc_data.get('type', ''),
                    'platform': poc_data.get('platform', '')
                })
            
            if not poc_batch_params: # 如果过滤后列表为空
                logger.warning(f"没有有效的 ExploitDB POC 数据可以发送到 Neo4j for {cve_id_example}")
                return False

            result = session.run(query, poc_batch=poc_batch_params)
            summary = result.consume() # 获取查询摘要
            
            # --- 修改: 通过摘要检查结果 --- 
            nodes_created = summary.counters.nodes_created
            rels_created = summary.counters.relationships_created
            props_set = summary.counters.properties_set
            
            logger.info(
                f"Neo4j 批量保存完成 ({cve_id_example}): "
                f"创建节点={nodes_created}, 创建关系={rels_created}, 设置属性={props_set}"
            )
            
            # 只要没有异常，就认为操作是成功的 (即使没有创建新节点/关系，因为 MERGE)
            success = True

        # --- 修改: 将关闭连接移到 try 块外部的 finally 中 --- 
        # driver.close() 
        return success
    
    except Exception as e:
        # --- 修改: 提供更详细的错误日志 --- 
        import traceback
        logger.error(f"批量保存到 Neo4j 时出错 ({cve_id_example}): {str(e)}")
        logger.debug(f"异常详情: {traceback.format_exc()}")
        return False
    finally:
        # --- 修改: 确保驱动程序总是被关闭 --- 
        if driver:
             driver.close()
             logger.debug("Neo4j driver 已关闭")

def setup_arg_parser() -> argparse.ArgumentParser:
    """设置参数解析器"""
    parser = argparse.ArgumentParser(description='从ExploitDB获取POC，如果失败则使用LLM生成，并存储到Neo4j')
    
    # 必要参数组
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--cve', help='CVE ID，例如CVE-2021-44228')
    group.add_argument('--file', help='包含CVE ID的文件，每行一个')
    group.add_argument('--db-query', help='从数据库查询CVE ID')
    
    # 可选参数
    parser.add_argument('--db-path', default='/home/<USER>/data/poc/exploitDetailed.db', help='SQLite数据库路径')
    parser.add_argument('--proxy', help='HTTP代理，例如 http://127.0.0.1:8080')
    parser.add_argument('--socks-proxy', help='SOCKS代理，例如 socks5://127.0.0.1:1080')
    parser.add_argument('--save-to-json', action='store_true', help='将结果保存为JSON文件到默认或指定目录')
    parser.add_argument('--json-output-dir', default='/home/<USER>/data/poc/json', help='JSON文件输出目录')
    parser.add_argument('--save-to-neo4j', action='store_true', help='将结果保存到Neo4j')
    parser.add_argument('--mock', action='store_true', help='使用模拟数据，用于测试')
    parser.add_argument('--verbose', action='store_true', help='显示详细日志')
    parser.add_argument('--timeout', type=int, default=60, help='Playwright/请求超时时间（秒）')
    parser.add_argument('--delay', type=float, default=0, help='在处理每个CVE后添加随机延迟（秒），例如1.5')
    parser.add_argument('--use-llm', action='store_true', help='如果ExploitDB没有找到POC，使用LLM生成')
    parser.add_argument('--only-llm', action='store_true', help='直接使用LLM生成POC，跳过ExploitDB')
    parser.add_argument('--no-nvd-api', action='store_false', dest='use_nvd_api', help='不使用NVD API获取CVE详情')
    parser.add_argument('--force-fetch', action='store_true', help='强制从网络获取POC，忽略本地SQLite缓存')
    parser.set_defaults(use_nvd_api=True, save_to_json=False, save_to_neo4j=False)
    
    return parser

def read_cve_list_from_file(file_path: str) -> List[str]:
    """从文件中读取CVE ID列表"""
    logger.info(f"从文件 {file_path} 读取CVE ID列表")
    cve_list = []
    
    try:
        with open(file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    cve_list.append(line)
        
        logger.info(f"从文件中读取了 {len(cve_list)} 个CVE ID")
        return cve_list
    except Exception as e:
        logger.error(f"读取CVE列表文件时出错: {str(e)}")
        return []

def get_cve_ids_from_db(query: str, db_path: str) -> List[str]:
    """
    从数据库查询CVE ID列表
    
    参数:
        query: SQL查询语句
        db_path: 数据库路径
        
    返回:
        CVE ID列表
    """
    logger.info(f"从数据库查询CVE ID列表: {query}")
    cve_list = []
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute(query)
        
        for row in cursor.fetchall():
            if row[0]:  # 确保不是None或空字符串
                cve_list.append(row[0])
                
        conn.close()
        
        logger.info(f"从数据库查询到 {len(cve_list)} 个CVE ID")
        return cve_list
    except Exception as e:
        logger.error(f"从数据库查询CVE ID时出错: {str(e)}")
        return []

def configure_proxies(args) -> Dict:
    """
    配置代理设置
    
    参数:
        args: 命令行参数
        
    返回:
        代理设置字典
    """
    proxies = {}
    
    if args.proxy:
        logger.info(f"使用HTTP代理: {args.proxy}")
        proxies['http'] = args.proxy
        proxies['https'] = args.proxy
        
    if args.socks_proxy:
        logger.info(f"使用SOCKS代理: {args.socks_proxy}")
        proxies['socks'] = args.socks_proxy
        
    return proxies

def process_single_cve(cve_id: str, args) -> bool:
    """
    处理单个CVE ID，获取所有匹配的POC并保存
    
    参数:
        cve_id: CVE ID
        args: 命令行参数
        
    返回:
        是否至少成功处理了一个POC
    """
    logger.info(f"处理CVE ID: {cve_id}")
    # --- 修改: 初始化 overall_success --- 
    overall_success = False 
    poc_source = "unknown"

    # 配置代理
    proxies = configure_proxies(args)

    # 1. 尝试从SQLite缓存获取 (注意: 当前缓存逻辑只取一个)
    sqlite_poc_data = None
    if not args.force_fetch:
        sqlite_poc_data = try_get_poc_from_sqlite(cve_id, args.db_path)
        if sqlite_poc_data:
            logger.info(f"从 SQLite 缓存中找到 {cve_id} 的 POC (EDB-ID: {sqlite_poc_data.get('edb_id')}) - 将跳过网络获取")
            # 如果缓存命中，我们认为这个CVE处理是成功的，但不触发后续的网络获取和保存
            # 如果需要即使缓存命中也去网络获取并保存更新，需要调整这里的逻辑
            return True # 直接返回成功，不重复获取和保存

    # 2. 如果缓存没有或强制获取，则尝试从 ExploitDB 获取所有 POC (使用 Playwright)
    poc_list_from_playwright = []
    logger.info(f"尝试使用 Playwright 从 ExploitDB 获取 {cve_id} 的所有 POC")
    playwright_collector = PlaywrightExploitDBCollector(
        proxies=proxies,
        timeout=args.timeout * 1000, 
        verbose=args.verbose
    )
    poc_list_from_playwright = playwright_collector.fetch_poc_by_cve(cve_id)

    if poc_list_from_playwright:
        logger.info(f"Playwright 找到 {len(poc_list_from_playwright)} 个 POC for {cve_id}")
        poc_source = "playwright_exploitdb"
        processed_count_playwright = 0
        # --- 修改: 遍历所有找到的 POC --- 
        for poc_data in poc_list_from_playwright:
            if not poc_data or not poc_data.get('edb_id'):
                logger.warning(f"跳过无效的 Playwright POC 数据项: {poc_data}")
                continue
            
            current_edb_id = poc_data.get('edb_id')
            logger.info(f"处理 Playwright 获取的 POC (EDB-ID: {current_edb_id})")
            
            # 保存到SQLite缓存 (仍然逐个保存)
            save_poc_to_sqlite(poc_data, args.db_path)
            
            # 保存到 JSON (仍然逐个保存)
            if args.save_to_json:
                output_dir = os.path.join(args.json_output_dir, poc_source.split('_')[0])
                save_poc_to_json(poc_data, output_dir=output_dir, cve_id=cve_id, llm=False)
            
            processed_count_playwright += 1
            overall_success = True 
            
        if processed_count_playwright > 0:
             logger.info(f"成功处理了 {processed_count_playwright} 个来自 Playwright 的 POC (SQLite/JSON) ")
             # --- 修改: 在循环外批量保存到 Neo4j --- 
             if args.save_to_neo4j and NEO4J_AVAILABLE:
                  logger.info(f"开始将 {processed_count_playwright} 个 POC 批量保存到 Neo4j...")
                  if save_exploitdb_poc_list_to_neo4j(poc_list_from_playwright):
                      logger.info("Neo4j 批量保存成功")
                  else:
                      logger.error("Neo4j 批量保存失败")
                      # 可以选择是否将 overall_success 设为 False，取决于业务逻辑
                      # overall_success = False 
             # --- 结束批量保存 ---
        else:
             logger.warning(f"Playwright 找到了 POC 数据，但未能成功处理任何一个")

    else:
        logger.warning(f"使用 Playwright 未能从 ExploitDB 获取到 {cve_id} 的任何 POC")

    # 3. 如果 Playwright 未找到任何 POC (overall_success 仍为 False)，尝试使用 LLM 生成
    if not overall_success and args.use_llm:
        logger.info(f"未能从 ExploitDB 获取 POC，尝试使用 LLM 生成 {cve_id} 的 POC")
        llm_poc_data = generate_poc_with_llm(cve_id, args.use_nvd_api, proxies)
        if llm_poc_data:
            logger.info(f"成功使用 LLM 生成 {cve_id} 的 POC")
            poc_source = "llm"
            
            # 保存 LLM POC 到 SQLite
            save_llm_poc_to_sqlite(llm_poc_data, args.db_path)
            
            # 保存 LLM POC 到 JSON
            if args.save_to_json:
                output_dir = os.path.join(args.json_output_dir, poc_source)
                save_poc_to_json(llm_poc_data, output_dir=output_dir, cve_id=cve_id, llm=True)
            
            overall_success = True # LLM 成功也算成功
        else:
            logger.error(f"使用 LLM 生成 {cve_id} 的 POC 也失败了")

    # 4. Final logging and delay
    if not overall_success:
        logger.error(f"处理 CVE: {cve_id} 失败，未能从任何来源获取或生成 POC")

    # 添加随机延迟
    if args.delay > 0:
        delay_time = random.uniform(args.delay * 0.5, args.delay * 1.5)
        logger.debug(f"等待 {delay_time:.2f} 秒...")
        time.sleep(delay_time)

    # --- 修改: 返回 overall_success --- 
    return overall_success

def save_poc_to_json(poc_data: Dict, output_dir: str = None, cve_id: str = None, llm: bool = False) -> None:
    """将POC数据保存到JSON文件"""
    if not output_dir:
        output_dir = os.path.join('data', 'poc', 'json')

    os.makedirs(output_dir, exist_ok=True)

    filename_cve = (cve_id or poc_data.get('cve_id', 'UNKNOWN_CVE')).replace('-', '_').replace('/','_')
    # --- 修改: 使用 Playwright 返回的 edb_id (如果有) ---
    edb_id = poc_data.get('edb_id')
    filename_suffix = f"_EDB_{edb_id}" if edb_id and not llm else (f"_LLM" if llm else "")
    file_path = os.path.join(output_dir, f"{filename_cve}{filename_suffix}.json")

    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(poc_data, f, indent=2, ensure_ascii=False)
        logger.info(f"POC 数据已保存到 JSON 文件: {file_path}")
    except Exception as e:
        logger.error(f"保存 POC 数据到 JSON 文件 {file_path} 时出错: {str(e)}")

def main():
    """主函数"""
    parser = setup_arg_parser()
    args = parser.parse_args()
    
    # 设置详细日志模式
    if args.verbose:
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            handler.setLevel(logging.DEBUG)
        logger.debug("详细模式已启用")
    
    # 获取CVE ID列表
    cve_list = []
    if args.cve:
        cve_list = [args.cve]
    elif args.file:
        cve_list = read_cve_list_from_file(args.file)
    elif args.db_query:
        cve_list = get_cve_ids_from_db(args.db_query, args.db_path)
    
    if not cve_list:
        logger.error("没有要处理的CVE ID")
        return
    
    logger.info(f"准备处理 {len(cve_list)} 个CVE ID")
    
    # 处理每个CVE ID
    success_count = 0
    failure_count = 0
    for cve_id in cve_list:
        # --- 修改: 接收 bool 返回值 ---
        success = process_single_cve(cve_id, args)
        if success:
            success_count += 1
        else:
            failure_count += 1 # --- 计数失败 ---
        
        # 添加随机延迟，防止被封
        if len(cve_list) > 1 and not args.mock and not args.only_llm:
            delay = random.uniform(1.0, 3.0)
            logger.debug(f"随机延迟 {delay:.2f} 秒")
            time.sleep(delay)
    
    # 报告结果
    logger.info(f"处理完成。成功处理 {success_count} 个 CVE，失败 {failure_count} 个。")
    
    # 添加：如果启用了save_neo4j参数，批量导入所有POC到Neo4j
    if args.save_to_neo4j and NEO4J_AVAILABLE:
        logger.info("开始将SQLite中所有POC批量导入Neo4j")
        try:
            # 导入sqlite2neo4j模块
            from src.data_collection.collectors.poc_collection.sqlite2neo4j import batch_import_to_neo4j
            # 检查数据库路径是否存在
            if not os.path.exists(args.db_path):
                logger.error(f"数据库文件不存在: {args.db_path}")
                return
                
            # 设置Neo4j配置文件路径
            project_root = Path(__file__).resolve().parents[3]
            neo4j_config_path = project_root / 'src' / 'data_collection' / 'config' / 'neo4j_config.json'
            
            logger.info(f"将使用数据库: {args.db_path}")
            logger.info(f"Neo4j配置文件路径: {neo4j_config_path}")
            
            # 传递配置文件路径
            batch_import_to_neo4j(args.db_path, neo4j_config_path)
            logger.info("成功将所有POC导入Neo4j")
        except ImportError as e:
            logger.error(f"导入sqlite2neo4j模块失败: {str(e)}")
            logger.info("尝试对sqlite2neo4j.py进行路径修正")
            try:
                # 尝试获取当前脚本所在目录
                script_dir = Path(__file__).resolve().parent
                collectors_dir = script_dir.parent / "collectors" / "poc_collection"
                sqlite2neo4j_path = collectors_dir / "sqlite2neo4j.py"
                
                if sqlite2neo4j_path.exists():
                    logger.info(f"找到sqlite2neo4j.py文件: {sqlite2neo4j_path}")
                    # 添加到系统路径
                    if str(collectors_dir) not in sys.path:
                        sys.path.append(str(collectors_dir))
                        logger.info(f"已添加到系统路径: {collectors_dir}")
                    
                    # 再次尝试导入
                    import sqlite2neo4j
                    logger.info("成功导入sqlite2neo4j模块")
                    sqlite2neo4j.batch_import_to_neo4j(args.db_path)
                    logger.info("成功将所有POC导入Neo4j")
                else:
                    logger.error(f"未找到sqlite2neo4j.py文件")
            except Exception as e2:
                logger.error(f"尝试修正路径后仍然失败: {str(e2)}")
        except Exception as e:
            logger.error(f"批量导入POC到Neo4j时出错: {str(e)}")

if __name__ == "__main__":
    main() 