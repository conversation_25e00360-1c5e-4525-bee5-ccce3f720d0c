#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub采集器测试脚本

验证GitHub安全公告采集器的可用性，测试包括：
1. 网络连接性
2. API令牌有效性
3. 代理配置有效性
4. 数据获取能力
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def load_config():
    """加载数据源配置"""
    config_path = os.path.join(project_root, 'config', 'sources.json')
    
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        return None
        
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config.get('sources', {}).get('GitHub', {})
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        return None

def test_github_collector():
    """测试GitHub采集器功能"""
    from src.collectors.github import GitHubCollector
    
    # 加载配置
    config = load_config()
    if not config:
        logger.error("无法加载GitHub配置")
        return False
    
    # 检查API Token
    api_token = config.get('api_token', '')
    if not api_token:
        logger.warning("未配置GitHub API令牌")
    elif len(api_token) < 35:
        logger.warning(f"GitHub API令牌可能不完整: {api_token[:10]}...")
    
    # 检查代理配置
    proxy = config.get('proxy', '')
    if not proxy:
        logger.warning("未配置代理，访问GitHub API可能会受到限制")
    else:
        logger.info(f"使用代理: {proxy}")
    
    # 创建采集器
    collector = GitHubCollector(config)
    
    # 测试连接
    logger.info("正在测试GitHub API连接...")
    if not collector.test_connection():
        logger.error("GitHub API连接测试失败")
        return False
    
    # 测试数据获取
    logger.info("正在测试GitHub数据采集功能...")
    # 获取最近7天的数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    try:
        data = collector.fetch_data(start_date, end_date)
        if data:
            logger.info(f"成功获取 {len(data)} 条GitHub安全公告数据")
            
            # 打印部分数据示例
            if len(data) > 0:
                sample = data[0]
                logger.info(f"数据示例: ID={sample.get('id')}, 标题={sample.get('summary', '')[:30]}...")
            
            return True
        else:
            logger.warning("未获取到GitHub安全公告数据")
            return False
    except Exception as e:
        logger.error(f"获取数据时出错: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("开始测试GitHub采集器...")
    
    if test_github_collector():
        logger.info("GitHub采集器测试成功!")
        sys.exit(0)
    else:
        logger.error("GitHub采集器测试失败")
        sys.exit(1) 