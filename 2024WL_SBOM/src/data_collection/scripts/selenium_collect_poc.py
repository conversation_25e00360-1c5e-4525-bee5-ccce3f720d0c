#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用Selenium增强的POC收集器测试脚本
使用方法:
    python selenium_collect_poc.py --cve CVE-2021-44228 [--proxy http://host:port] [--verbose]
"""

import os
import sys
import argparse
import logging
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

# 导入自定义模块
from src.data_collection.services.selenium_exploit_db_collector import SeleniumExploitDBCollector

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', 'selenium_poc_collection.log'), 'a')
    ]
)
logger = logging.getLogger('selenium_collect_poc')

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='使用Selenium增强的ExploitDB爬虫获取POC')
    parser.add_argument('--cve', required=True, help='CVE ID，例如CVE-2021-44228')
    parser.add_argument('--proxy', help='HTTP代理，例如 http://127.0.0.1:8080')
    parser.add_argument('--output-dir', default='/home/<USER>/data/poc/json', help='输出目录')
    parser.add_argument('--verbose', action='store_true', help='显示详细日志')
    parser.add_argument('--timeout', type=int, default=60, help='请求超时时间（秒）')
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 设置详细日志模式
    if args.verbose:
        logger.setLevel(logging.DEBUG)
        for handler in logger.handlers:
            handler.setLevel(logging.DEBUG)
        logger.debug("详细模式已启用")
    
    # 配置代理
    proxies = None
    if args.proxy:
        proxies = {
            'http': args.proxy,
            'https': args.proxy
        }
        logger.info(f"使用代理: {args.proxy}")
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建收集器
    logger.info("初始化Selenium增强的ExploitDB收集器")
    collector = SeleniumExploitDBCollector(
        proxies=proxies,
        timeout=args.timeout,
        verbose=args.verbose
    )
    
    # 获取POC
    logger.info(f"正在获取 {args.cve} 的POC...")
    poc_data = collector.fetch_poc_by_cve(args.cve)
    
    if poc_data:
        # 保存POC
        file_path = collector.save_poc_to_json(poc_data, args.output_dir)
        logger.info(f"POC已保存到: {file_path}")
        logger.info(f"标题: {poc_data.get('title', 'N/A')}")
        logger.info(f"作者: {poc_data.get('author', 'N/A')}")
        logger.info(f"平台: {poc_data.get('platform', 'N/A')}")
        logger.info(f"类型: {poc_data.get('type', 'N/A')}")
        logger.info(f"EDB-ID: {poc_data.get('edb_id', 'N/A')}")
        
        # 查看代码长度
        code_length = len(poc_data.get('poc_code', ''))
        logger.info(f"POC代码长度: {code_length} 字符")
        
        # 如果需要输出代码预览
        if args.verbose and 'poc_code' in poc_data:
            preview_lines = poc_data['poc_code'].split('\n')[:5]
            logger.info("POC代码预览 (前5行):")
            for i, line in enumerate(preview_lines, 1):
                logger.info(f"{i}: {line}")
    else:
        logger.error(f"未找到 {args.cve} 的POC")
        logger.info("可能原因:")
        logger.info("1. ExploitDB上没有该CVE的POC")
        logger.info("2. 网络连接问题")
        logger.info("3. Selenium配置问题")
        logger.info("4. 网站结构已改变，解析失败")
        logger.info("\n建议:")
        logger.info("- 检查网络连接和代理设置")
        logger.info("- 使用--verbose参数查看详细日志")
        logger.info("- 手动访问ExploitDB网站确认该CVE是否有POC")

if __name__ == "__main__":
    main() 