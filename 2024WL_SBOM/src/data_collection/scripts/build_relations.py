#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
漏洞实体关系建立脚本

从已采集的漏洞数据中提取各种实体，并建立它们之间的关系：
- AFFECTS: 漏洞影响组件
- HAS_VERSION: 组件包含版本
- FIXED_BY: 漏洞被补丁修复
- HAS_WEAKNESS: 漏洞包含弱点
"""

import os
import sys
import json
import re
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Set, Tuple

# 修复导入路径问题
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

# 现在导入src模块
from src.models.database import Database, DBVulnerability, DBPackage, DBReference
from src.models.entities import Vulnerability, Package, Version, Reference
from src.services.graph_store import GraphStore
from src.models.graph import GraphNode, NodeType, RelationType, GraphRelation
from src.utils.logger import setup_logger, logger

# 改进的CWE正则表达式，匹配更多格式
CWE_PATTERN = re.compile(r'(?:CWE[-_ :]+)?(\d+)', re.IGNORECASE)

def extract_cwe_ids(text: str) -> List[str]:
    """
    从文本中提取CWE ID
    
    Args:
        text: 文本内容
        
    Returns:
        CWE ID列表
    """
    if not text:
        return []
        
    # 尝试匹配CWE-数字格式
    cwe_ids = []
    for match in re.finditer(r'CWE[-_ :]+(\d+)', text, re.IGNORECASE):
        cwe_id = f"CWE-{match.group(1)}"
        if cwe_id not in cwe_ids:
            cwe_ids.append(cwe_id)
            
    # 如果没有找到任何CWE，尝试从知名CWE关键词中识别
    if not cwe_ids:
        known_cwe_keywords = {
            'injection': 'CWE-74',
            'sql injection': 'CWE-89',
            'cross-site scripting': 'CWE-79',
            'xss': 'CWE-79',
            'buffer overflow': 'CWE-120',
            'memory leak': 'CWE-401',
            'command injection': 'CWE-77',
            'information disclosure': 'CWE-200',
            'path traversal': 'CWE-22',
            'authentication bypass': 'CWE-287',
            'authorization bypass': 'CWE-285',
            'denial of service': 'CWE-400',
            'dos': 'CWE-400'
        }
        
        for keyword, cwe_id in known_cwe_keywords.items():
            if keyword in text.lower() and cwe_id not in cwe_ids:
                cwe_ids.append(cwe_id)
                
    return cwe_ids

def extract_patch_info(vulnerability: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    从漏洞数据中提取补丁信息
    
    Args:
        vulnerability: 漏洞数据
        
    Returns:
        补丁列表
    """
    patches = []
    
    # 从引用中查找补丁相关URL
    # 放宽补丁识别条件
    patch_keywords = ['patch', 'fix', 'commit', 'pr', 'pull', 'merge', 'issue', 'github.com', 'gitlab.com']
    
    # 尝试从references中找出可能的补丁URL
    for ref in vulnerability.get('references', []):
        url = ref.get('url', '')
        if not url:
            continue
            
        # 检查URL是否包含补丁关键词
        if any(keyword in url.lower() for keyword in patch_keywords):
            # 生成补丁ID
            patch_id = f"patch-{hashlib.md5(url.encode()).hexdigest()[:8]}-{vulnerability.get('id')}"
            
            # 提取发布日期（如果有）
            fix_date = None
            if vulnerability.get('last_modified_date'):
                fix_date = vulnerability.get('last_modified_date')
            elif vulnerability.get('published_date'):
                fix_date = vulnerability.get('published_date')
                
            patches.append({
                'id': patch_id,
                'url': url,
                'fix_date': fix_date,
                'verified': True  # 假设所有引用的补丁都是已验证的
            })
    
    # 如果没有找到任何补丁，创建一个虚拟补丁
    if not patches:
        vuln_id = vulnerability.get('id')
        patch_id = f"virtual-patch-{vuln_id}"
        
        # 尝试从标题构建一个描述性URL
        title = vulnerability.get('title', '').replace(' ', '-').lower()
        if title:
            virtual_url = f"https://advisory.example.org/{vuln_id}/{title}"
        else:
            virtual_url = f"https://advisory.example.org/{vuln_id}/patch"
            
        patches.append({
            'id': patch_id,
            'url': virtual_url,
            'fix_date': vulnerability.get('last_modified_date') or vulnerability.get('published_date'),
            'verified': False  # 虚拟补丁标记为未验证
        })
        
    return patches

def extract_component_versions(vulnerability: Dict[str, Any]) -> List[Tuple[Dict[str, Any], List[Dict[str, Any]]]]:
    """
    从漏洞数据中提取组件和版本信息
    
    Args:
        vulnerability: 漏洞数据
        
    Returns:
        组件和版本列表的元组
    """
    components = []
    
    for pkg in vulnerability.get('affected_packages', []):
        # 创建组件ID (使用生态系统:名称格式)
        ecosystem = pkg.get('ecosystem', 'unknown')
        name = pkg.get('name', '')
        
        if not name:
            continue
            
        component_id = f"{ecosystem}:{name}".lower()
        
        # 创建组件
        component = {
            'id': component_id,
            'name': name,
            'ecosystem': ecosystem,
            'description': f"Component affected by {vulnerability.get('id')}",
            'source': vulnerability.get('source', 'unknown')
        }
        
        # 创建版本
        versions = []
        
        # 受影响的版本
        affected_versions = pkg.get('affected_versions', [])
        if isinstance(affected_versions, str):
            affected_versions = [affected_versions]
            
        for ver in affected_versions:
            if not ver:
                continue
                
            version_id = f"{component_id}:{ver}".lower()
            versions.append({
                'id': version_id,
                'version': ver,
                'component_id': component_id,
                'release_date': None,  # 通常没有发布日期信息
                'is_affected': True,
                'is_fixed': False
            })
            
        # 已修复的版本
        fixed_versions = pkg.get('fixed_versions', [])
        if isinstance(fixed_versions, str):
            fixed_versions = [fixed_versions]
            
        for ver in fixed_versions:
            if not ver:
                continue
                
            version_id = f"{component_id}:{ver}".lower()
            versions.append({
                'id': version_id,
                'version': ver,
                'component_id': component_id,
                'release_date': None,  # 通常没有发布日期信息
                'is_affected': False,
                'is_fixed': True
            })
            
        # 如果没有显式的版本信息，创建一个通用版本
        if not versions:
            version_id = f"{component_id}:unknown"
            versions.append({
                'id': version_id,
                'version': 'unknown',
                'component_id': component_id,
                'release_date': None,
                'is_affected': True,
                'is_fixed': False
            })
            
        components.append((component, versions))
        
    return components

def create_or_update_node(graph: GraphStore, node_type: str, node_id: str, properties: Dict[str, Any]) -> bool:
    """
    创建或更新节点
    
    Args:
        graph: 图数据库存储服务实例
        node_type: 节点类型
        node_id: 节点ID
        properties: 节点属性
        
    Returns:
        是否创建或更新成功
    """
    try:
        if graph.driver is None:
            logger.error("未连接到图数据库")
            return False
            
        # 将字符串节点类型转换为NodeType枚举
        try:
            if node_type == 'Weakness':
                node_type_enum = NodeType.WEAKNESS
            else:
                node_type_enum = NodeType(node_type)
        except ValueError:
            logger.error(f"无效的节点类型: {node_type}")
            return False
        
        # 检查节点是否已存在
        existing_node = graph.get_node(node_type_enum, node_id)
        
        # 创建节点对象
        node = GraphNode(
            id=node_id,
            node_type=node_type_enum,
            properties=properties
        )
        
        if existing_node:
            logger.debug(f"更新节点: {node_type} {node_id}")
            # 更新节点
            return graph.update_node(node)
        else:
            logger.debug(f"创建节点: {node_type} {node_id}")
            # 创建节点
            return graph.create_node(node)
    except Exception as e:
        logger.error(f"创建或更新节点失败: {str(e)}, 节点类型: {node_type}, 节点ID: {node_id}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def create_relationship(graph: GraphStore, 
                      start_node_type: str, 
                      start_node_id: str, 
                      end_node_type: str, 
                      end_node_id: str, 
                      relation_type: str,
                      properties: Dict[str, Any] = None) -> bool:
    """
    创建关系
    
    Args:
        graph: 图数据库存储服务实例
        start_node_type: 起始节点类型
        start_node_id: 起始节点ID
        end_node_type: 目标节点类型
        end_node_id: 目标节点ID
        relation_type: 关系类型
        properties: 关系属性
        
    Returns:
        是否创建成功
    """
    try:
        if graph.driver is None:
            logger.error("未连接到图数据库")
            return False
        
        # 将字符串节点类型转换为NodeType枚举
        try:
            if start_node_type == 'Weakness':
                start_node_enum = NodeType.WEAKNESS
            else:
                start_node_enum = NodeType(start_node_type)
                
            if end_node_type == 'Weakness':
                end_node_enum = NodeType.WEAKNESS
            else:
                end_node_enum = NodeType(end_node_type)
        except ValueError as e:
            logger.error(f"无效的节点类型: {start_node_type} 或 {end_node_type}, 错误: {str(e)}")
            return False
            
        # 检查起始节点和目标节点是否存在
        start_node = graph.get_node(start_node_enum, start_node_id)
        if not start_node:
            logger.error(f"起始节点不存在: {start_node_type} {start_node_id}")
            return False
            
        end_node = graph.get_node(end_node_enum, end_node_id)
        if not end_node:
            logger.error(f"目标节点不存在: {end_node_type} {end_node_id}")
            return False
        
        # 将字符串关系类型转换为RelationType枚举
        try:
            if relation_type == 'HAS_WEAKNESS':
                rel_type_enum = RelationType.HAS_WEAKNESS
            else:
                rel_type_enum = RelationType(relation_type)
        except ValueError as e:
            logger.error(f"无效的关系类型: {relation_type}, 错误: {str(e)}")
            return False
        
        # 使用标准的GraphRelation对象创建关系
        relation = GraphRelation(
            source_id=start_node_id,
            target_id=end_node_id,
            relation_type=rel_type_enum,
            properties=properties or {}
        )
        
        # 调用GraphStore的create_relation方法
        return graph.create_relation(relation)
    except Exception as e:
        logger.error(f"创建关系失败: {str(e)}, 起始节点: {start_node_type}:{start_node_id}, 目标节点: {end_node_type}:{end_node_id}, 关系类型: {relation_type}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def batch_create_relationships(graph: GraphStore, relationships: List[Dict[str, Any]]) -> int:
    """
    批量创建关系
    
    Args:
        graph: 图数据库存储服务实例
        relationships: 关系列表，每个关系是一个字典，包含:
            - start_node_type: 起始节点类型
            - start_node_id: 起始节点ID
            - end_node_type: 目标节点类型
            - end_node_id: 目标节点ID
            - relation_type: 关系类型
            - properties: 关系属性(可选)
            
    Returns:
        成功创建的关系数量
    """
    if not relationships:
        return 0
        
    if graph.driver is None:
        logger.error("未连接到图数据库")
        return 0
        
    success_count = 0
    relations = []
    
    # 先检查所有节点是否存在
    for rel_info in relationships:
        start_node_type = rel_info['start_node_type']
        start_node_id = rel_info['start_node_id']
        end_node_type = rel_info['end_node_type']
        end_node_id = rel_info['end_node_id']
        relation_type = rel_info['relation_type']
        properties = rel_info.get('properties', {})
        
        try:
            # 将字符串节点类型转换为NodeType枚举
            try:
                if start_node_type == 'Weakness':
                    start_node_enum = NodeType.WEAKNESS
                else:
                    start_node_enum = NodeType(start_node_type)
                    
                if end_node_type == 'Weakness':
                    end_node_enum = NodeType.WEAKNESS
                else:
                    end_node_enum = NodeType(end_node_type)
            except ValueError as e:
                logger.error(f"无效的节点类型: {start_node_type} 或 {end_node_type}, 错误: {str(e)}")
                continue
            
            # 检查起始节点和目标节点是否存在
            start_node = graph.get_node(start_node_enum, start_node_id)
            if not start_node:
                logger.warning(f"起始节点不存在: {start_node_type} {start_node_id}")
                continue
                
            end_node = graph.get_node(end_node_enum, end_node_id)
            if not end_node:
                logger.warning(f"目标节点不存在: {end_node_type} {end_node_id}")
                continue
            
            # 将字符串关系类型转换为RelationType枚举
            try:
                if relation_type == 'HAS_WEAKNESS':
                    rel_type_enum = RelationType.HAS_WEAKNESS
                else:
                    rel_type_enum = RelationType(relation_type)
            except ValueError as e:
                logger.error(f"无效的关系类型: {relation_type}, 错误: {str(e)}")
                continue
            
            # 创建GraphRelation对象
            relation = GraphRelation(
                source_id=start_node_id,
                target_id=end_node_id,
                relation_type=rel_type_enum,
                properties=properties or {}
            )
            relations.append(relation)
        except Exception as e:
            logger.error(f"准备关系时出错: {str(e)}, 关系: {start_node_type}:{start_node_id} -> {end_node_type}:{end_node_id} ({relation_type})")
            
    # 批量创建关系
    if relations:
        success_count = graph.batch_create_relations(relations)
        logger.info(f"批量创建关系: 成功 {success_count}/{len(relationships)}")
    
    return success_count

# 添加一个辅助函数，为每个漏洞确保至少有一个CWE关联
def ensure_cwe_exists(vuln_id: str, description: str) -> List[str]:
    """
    确保漏洞至少有一个CWE关联
    
    Args:
        vuln_id: 漏洞ID
        description: 漏洞描述
        
    Returns:
        CWE ID列表
    """
    cwe_ids = extract_cwe_ids(description)
    
    # 如果没有找到CWE，分配一个默认的CWE
    if not cwe_ids:
        # 基于漏洞ID的前缀选择默认CWE
        if 'CVE' in vuln_id:
            # 对于一般CVE，使用CWE-1000 (未分类的漏洞类型)
            cwe_ids = ['CWE-1000']
        elif 'GHSA' in vuln_id:
            # GitHub安全公告通常与代码相关
            cwe_ids = ['CWE-20']  # 不正确的输入验证
        else:
            # 对于其他漏洞ID，使用CWE-0 (未知类型)
            cwe_ids = ['CWE-0']
    
    return cwe_ids

def create_default_weakness_nodes(graph: GraphStore) -> Set[str]:
    """
    创建常见的默认CWE节点
    
    Args:
        graph: 图数据库存储服务实例
        
    Returns:
        已创建的CWE ID集合
    """
    default_cwe_nodes = {
        'CWE-0': '未知类型',
        'CWE-20': '不正确的输入验证',
        'CWE-22': '路径遍历',
        'CWE-74': '注入',
        'CWE-77': '命令注入',
        'CWE-78': '操作系统命令注入',
        'CWE-79': '跨站脚本',
        'CWE-89': 'SQL注入',
        'CWE-120': '缓冲区溢出',
        'CWE-200': '信息泄露',
        'CWE-264': '权限、特权和访问控制',
        'CWE-287': '认证问题',
        'CWE-400': '拒绝服务',
        'CWE-601': 'URL重定向',
        'CWE-1000': '未分类的漏洞类型'
    }
    
    created_ids = set()
    for cwe_id, name in default_cwe_nodes.items():
        try:
            node = GraphNode(
                id=cwe_id,
                node_type=NodeType.WEAKNESS,
                properties={
                    'name': f"Common Weakness: {name}",
                    'type': 'CWE',
                    'description': name
                }
            )
            success = graph.create_node(node)
            if success:
                created_ids.add(cwe_id)
                logger.info(f"创建CWE节点成功: {cwe_id}")
            else:
                logger.warning(f"创建CWE节点失败: {cwe_id}")
        except Exception as e:
            logger.error(f"创建CWE节点异常: {cwe_id}, 错误: {str(e)}")
                
    return created_ids

def main():
    """主函数"""
    # 设置日志
    setup_logger()
    logger.info("开始建立漏洞实体关系...")
    
    # 连接数据库
    db_path = 'sqlite:///data/vulnerabilities.db'
    logger.info(f"连接数据库: {db_path}")
    db = Database(db_path)
    session = db.Session()
    
    # 连接图数据库
    graph_url = "bolt://localhost:7687"
    graph_user = "neo4j"
    graph_password = "password"
    logger.info(f"连接图数据库: {graph_url}")
    
    try:
        graph = GraphStore(graph_url, graph_user, graph_password)
        
        # 验证连接
        if not graph.is_connected():
            logger.error("无法连接到Neo4j图数据库，请检查配置")
            return
            
        logger.info("成功连接到Neo4j图数据库")
    except Exception as e:
        logger.error(f"连接Neo4j图数据库失败: {str(e)}")
        return
    
    try:
        # 检查NodeType枚举是否包含WEAKNESS类型
        try:
            NodeType.WEAKNESS
            logger.info("WEAKNESS节点类型已定义")
        except Exception as e:
            logger.error(f"WEAKNESS节点类型未定义: {str(e)}")
            logger.warning("将使用COMPONENT节点类型作为替代")
            # 未定义WEAKNESS节点类型时，使用COMPONENT代替
            NodeType_WEAKNESS = NodeType.COMPONENT
        
        # 首先创建默认的CWE节点
        logger.info("创建默认CWE节点...")
        
        # 使用正确的节点类型创建CWE节点
        default_cwe_nodes = {
            'CWE-0': '未知类型',
            'CWE-20': '不正确的输入验证',
            'CWE-22': '路径遍历',
            'CWE-74': '注入',
            'CWE-77': '命令注入',
            'CWE-78': '操作系统命令注入',
            'CWE-79': '跨站脚本',
            'CWE-89': 'SQL注入',
            'CWE-120': '缓冲区溢出',
            'CWE-200': '信息泄露',
            'CWE-264': '权限、特权和访问控制',
            'CWE-287': '认证问题',
            'CWE-400': '拒绝服务',
            'CWE-601': 'URL重定向',
            'CWE-1000': '未分类的漏洞类型'
        }
        
        created_ids = set()
        for cwe_id, name in default_cwe_nodes.items():
            try:
                node = GraphNode(
                    id=cwe_id,
                    node_type=NodeType.WEAKNESS,
                    properties={
                        'name': f"Common Weakness: {name}",
                        'type': 'CWE',
                        'description': name
                    }
                )
                success = graph.create_node(node)
                if success:
                    created_ids.add(cwe_id)
                    logger.info(f"创建CWE节点成功: {cwe_id}")
                else:
                    logger.warning(f"创建CWE节点失败: {cwe_id}")
            except Exception as e:
                logger.error(f"创建CWE节点异常: {cwe_id}, 错误: {str(e)}")
                
        default_cwe_ids = created_ids
        logger.info(f"已创建 {len(default_cwe_ids)} 个默认CWE节点")
        
        # 获取所有漏洞
        logger.info("从数据库中获取漏洞数据...")
        vulnerabilities = session.query(DBVulnerability).all()
        logger.info(f"共获取 {len(vulnerabilities)} 条漏洞记录")
        
        # 用于跟踪已创建的实体ID
        created_components = set()
        created_versions = set()
        created_patches = set()
        created_weaknesses = default_cwe_ids.copy()
        
        # 收集要创建的关系
        affects_relations = []
        has_version_relations = []
        fixed_by_relations = []
        has_weakness_relations = []
        
        # 处理每个漏洞
        for i, vuln_db in enumerate(vulnerabilities):
            try:
                # 进度日志
                if (i+1) % 10 == 0 or i+1 == len(vulnerabilities):
                    logger.info(f"处理进度: {i+1}/{len(vulnerabilities)} ({(i+1)/len(vulnerabilities)*100:.1f}%)")
                
                # 将数据库对象转换为字典
                vuln_data = {
                    'id': vuln_db.vuln_id,
                    'source': vuln_db.source,
                    'title': vuln_db.title,
                    'description': vuln_db.description,
                    'published_date': vuln_db.published_date,
                    'last_modified_date': vuln_db.last_modified_date,
                    'severity': vuln_db.severity,
                    'affected_packages': []
                }
                
                # 处理引用 - 检查references类型并正确处理
                vuln_data['references'] = []
                if vuln_db.references:
                    # 如果是字符串，尝试解析JSON
                    if isinstance(vuln_db.references, str):
                        try:
                            vuln_data['references'] = json.loads(vuln_db.references)
                        except json.JSONDecodeError:
                            logger.warning(f"无法解析引用JSON: {vuln_db.vuln_id}")
                    # 如果是列表或其他集合类型，直接使用
                    elif hasattr(vuln_db.references, '__iter__'):
                        for ref in vuln_db.references:
                            # 将DBReference对象转换为字典
                            ref_dict = {
                                'url': ref.url if hasattr(ref, 'url') else '',
                                'source': ref.source if hasattr(ref, 'source') else '',
                                'tags': ref.tags.split(',') if hasattr(ref, 'tags') and ref.tags else []
                            }
                            vuln_data['references'].append(ref_dict)
                
                # 处理关联的包
                for pkg_db in vuln_db.affected_packages:
                    pkg_data = {
                        'name': pkg_db.name,
                        'ecosystem': pkg_db.ecosystem,
                        'affected_versions': pkg_db.affected_versions if pkg_db.affected_versions else [],
                        'fixed_versions': pkg_db.fixed_versions if pkg_db.fixed_versions else []
                    }
                    vuln_data['affected_packages'].append(pkg_data)
                
                # 1. 创建漏洞节点（如果需要）
                vuln_props = {
                    'source': vuln_data['source'],
                    'title': vuln_data['title'],
                    'description': vuln_data['description'],
                    'severity': vuln_data['severity'],
                    'published_date': vuln_data['published_date'].isoformat() if vuln_data['published_date'] else None,
                    'last_modified_date': vuln_data['last_modified_date'].isoformat() if vuln_data['last_modified_date'] else None
                }
                
                success = create_or_update_node(graph, 'Vulnerability', vuln_data['id'], vuln_props)
                if not success:
                    logger.error(f"创建漏洞节点失败: {vuln_data['id']}")
                    continue
                
                # 2. 提取并创建组件和版本
                comp_ver_pairs = extract_component_versions(vuln_data)
                for component, versions in comp_ver_pairs:
                    # 创建组件
                    if component['id'] not in created_components:
                        success = create_or_update_node(graph, 'Component', component['id'], {
                            'name': component['name'],
                            'ecosystem': component['ecosystem'],
                            'description': component['description'],
                            'source': component['source']
                        })
                        if success:
                            created_components.add(component['id'])
                        else:
                            logger.error(f"创建组件节点失败: {component['id']}")
                            continue
                    
                    # 收集AFFECTS关系
                    # 准备受影响和已修复版本列表
                    affected_versions = []
                    fixed_versions = []
                    
                    for version in versions:
                        if version['is_affected']:
                            affected_versions.append(version['version'])
                        if version['is_fixed']:
                            fixed_versions.append(version['version'])
                    
                    affects_relations.append({
                        'start_node_type': 'Vulnerability',
                        'start_node_id': vuln_data['id'],
                        'end_node_type': 'Component',
                        'end_node_id': component['id'],
                        'relation_type': 'AFFECTS',
                        'properties': {
                            'severity': vuln_data['severity'],
                            'affected_versions': ','.join(affected_versions),
                            'fixed_versions': ','.join(fixed_versions)
                        }
                    })
                    
                    # 创建版本节点和关系
                    for version in versions:
                        if version['id'] not in created_versions:
                            success = create_or_update_node(graph, 'Version', version['id'], {
                                'version': version['version'],
                                'component_id': version['component_id'],
                                'release_date': version['release_date'].isoformat() if version['release_date'] else None,
                                'is_affected': version['is_affected'],
                                'is_fixed': version['is_fixed']
                            })
                            if success:
                                created_versions.add(version['id'])
                            else:
                                logger.error(f"创建版本节点失败: {version['id']}")
                                continue
                        
                        # 收集HAS_VERSION关系
                        # 添加is_latest属性
                        has_version_relations.append({
                            'start_node_type': 'Component',
                            'start_node_id': component['id'],
                            'end_node_type': 'Version',
                            'end_node_id': version['id'],
                            'relation_type': 'HAS_VERSION',
                            'properties': {
                                'release_date': version['release_date'].isoformat() if version['release_date'] else None,
                                'is_latest': version['is_fixed']  # 假设修复版本是最新的
                            }
                        })
                
                # 3. 提取并创建补丁
                patches = extract_patch_info(vuln_data)
                for patch in patches:
                    if patch['id'] not in created_patches:
                        success = create_or_update_node(graph, 'Patch', patch['id'], {
                            'url': patch['url'],
                            'fix_date': patch['fix_date'].isoformat() if patch['fix_date'] else None,
                            'verified': patch['verified']
                        })
                        if success:
                            created_patches.add(patch['id'])
                        else:
                            logger.error(f"创建补丁节点失败: {patch['id']}")
                            continue
                    
                    # 收集FIXED_BY关系
                    fixed_by_relations.append({
                        'start_node_type': 'Vulnerability',
                        'start_node_id': vuln_data['id'],
                        'end_node_type': 'Patch',
                        'end_node_id': patch['id'],
                        'relation_type': 'FIXED_BY',
                        'properties': {
                            'fix_date': patch['fix_date'].isoformat() if patch['fix_date'] else None,
                            'verified': patch['verified']
                        }
                    })
                
                # 4. 提取并创建CWE弱点
                cwe_ids = []
                # 确保漏洞至少有一个CWE关联
                cwe_ids = ensure_cwe_exists(vuln_data['id'], vuln_data['description'])
                
                # 也可以从引用中提取
                for ref in vuln_data.get('references', []):
                    if ref.get('tags') and 'cwe' in ref.get('tags'):
                        url = ref.get('url', '')
                        if 'cwe' in url.lower():
                            # 尝试从URL中提取CWE ID
                            cwe_matches = re.findall(r'CWE[-_ :]+(\d+)', url, re.IGNORECASE)
                            if cwe_matches:
                                cwe_id = f"CWE-{cwe_matches[0]}"
                                if cwe_id not in cwe_ids:
                                    cwe_ids.append(cwe_id)
                        
                for cwe_id in cwe_ids:
                    if cwe_id not in created_weaknesses:
                        success = create_or_update_node(graph, 'Weakness', cwe_id, {
                            'name': f"Common Weakness: {cwe_id}",
                            'type': 'CWE'
                        })
                        if success:
                            created_weaknesses.add(cwe_id)
                        else:
                            logger.error(f"创建弱点节点失败: {cwe_id}")
                            continue
                    
                    # 收集HAS_WEAKNESS关系
                    has_weakness_relations.append({
                        'start_node_type': 'Vulnerability',
                        'start_node_id': vuln_data['id'],
                        'end_node_type': 'Weakness',
                        'end_node_id': cwe_id,
                        'relation_type': 'HAS_WEAKNESS'
                    })
                    
            except Exception as e:
                logger.error(f"处理漏洞 {vuln_db.vuln_id} 时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
        
        # 批量创建关系
        logger.info("开始批量创建关系...")
        
        # 创建AFFECTS关系
        if affects_relations:
            logger.info(f"创建AFFECTS关系: {len(affects_relations)}个")
            affects_count = batch_create_relationships(graph, affects_relations)
            logger.info(f"成功创建AFFECTS关系: {affects_count}/{len(affects_relations)}")
        
        # 创建HAS_VERSION关系
        if has_version_relations:
            logger.info(f"创建HAS_VERSION关系: {len(has_version_relations)}个")
            has_version_count = batch_create_relationships(graph, has_version_relations)
            logger.info(f"成功创建HAS_VERSION关系: {has_version_count}/{len(has_version_relations)}")
        
        # 创建FIXED_BY关系
        if fixed_by_relations:
            logger.info(f"创建FIXED_BY关系: {len(fixed_by_relations)}个")
            fixed_by_count = batch_create_relationships(graph, fixed_by_relations)
            logger.info(f"成功创建FIXED_BY关系: {fixed_by_count}/{len(fixed_by_relations)}")
        
        # 创建HAS_WEAKNESS关系
        if has_weakness_relations:
            logger.info(f"创建HAS_WEAKNESS关系: {len(has_weakness_relations)}个")
            has_weakness_count = batch_create_relationships(graph, has_weakness_relations)
            logger.info(f"成功创建HAS_WEAKNESS关系: {has_weakness_count}/{len(has_weakness_relations)}")
                
        # 统计信息
        logger.info("实体关系建立完成!")
        logger.info(f"创建/更新的实体数量:")
        logger.info(f"  漏洞(Vulnerability): {len(vulnerabilities)}")
        logger.info(f"  组件(Component): {len(created_components)}")
        logger.info(f"  版本(Version): {len(created_versions)}")
        logger.info(f"  补丁(Patch): {len(created_patches)}")
        logger.info(f"  弱点(Weakness): {len(created_weaknesses)}")
        logger.info(f"创建的关系数量:")
        logger.info(f"  AFFECTS: {len(affects_relations)}")
        logger.info(f"  HAS_VERSION: {len(has_version_relations)}")
        logger.info(f"  FIXED_BY: {len(fixed_by_relations)}")
        logger.info(f"  HAS_WEAKNESS: {len(has_weakness_relations)}")
        
        # 验证关系建立情况
        logger.info("验证关系建立情况...")
        validation_queries = [
            ("MATCH ()-[r:AFFECTS]->() RETURN COUNT(r) AS count", "AFFECTS关系"),
            ("MATCH ()-[r:HAS_VERSION]->() RETURN COUNT(r) AS count", "HAS_VERSION关系"),
            ("MATCH ()-[r:FIXED_BY]->() RETURN COUNT(r) AS count", "FIXED_BY关系"),
            ("MATCH ()-[r:HAS_WEAKNESS]->() RETURN COUNT(r) AS count", "HAS_WEAKNESS关系")
        ]
        
        with graph.driver.session(database=graph.database) as session:
            for query, rel_name in validation_queries:
                try:
                    result = session.run(query)
                    record = result.single()
                    if record:
                        count = record["count"]
                        logger.info(f"  验证结果: 数据库中存在 {count} 个{rel_name}")
                except Exception as e:
                    logger.error(f"验证{rel_name}时出错: {str(e)}")
        
        # 注意：暂时不能创建DEPENDS_ON关系，因为缺少组件依赖数据
        logger.warning("注意: 无法创建组件间的DEPENDS_ON关系，因为缺少组件依赖数据。")
        logger.warning("建议添加专门的组件依赖数据采集功能。")
    
    except Exception as e:
        logger.error(f"处理数据时出错: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        
    finally:
        # 关闭数据库连接
        if session:
            session.close()
        if graph and graph.driver:
            graph.close()
            
        logger.info("脚本执行结束")

if __name__ == "__main__":
    main()