import asyncio
import argparse
import logging
import os
import sys

# 添加项目根目录到Python路径
# __file__ 是当前脚本 (playwright_collect_poc.py) 的路径
# os.path.dirname(__file__) 是脚本所在的目录 (scripts)
# os.path.join(..., '..', '..', '..') 向上导航三层到项目根目录
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from src.data_collection.services.playwright_exploit_db_collector import PlaywrightExploitDBCollector

# 设置日志格式
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description='Collect POC data from Exploit Database')
    parser.add_argument('--cve', type=str, required=True, help='CVE ID to search for')
    parser.add_argument('--output_dir', type=str, default='output', help='Directory to save the output files')
    args = parser.parse_args()

    collector = PlaywrightExploitDBCollector()

    # 获取 POC 列表 (调用同步接口)
    logger.info(f"正在获取 {args.cve} 的所有 POC...")
    # PlaywrightCollector 内部处理了 asyncio.run
    # --- 修改: 接收 POC 列表 ---
    all_poc_data = collector.fetch_poc_by_cve(args.cve)

    # --- 修改: 处理 POC 列表 ---
    if all_poc_data:
        logger.info(f"成功获取到 {len(all_poc_data)} 个匹配的 POC 数据")
        saved_files_count = 0
        # 遍历列表中的每个 POC 数据
        for poc_data in all_poc_data:
            if not poc_data or 'edb_id' not in poc_data:
                logger.warning("跳过无效的 POC 数据项")
                continue

            edb_id = poc_data.get('edb_id')
            # 保存单个 POC，传入 EDB ID 用于文件名
            file_path = collector.save_poc_to_json(poc_data, args.output_dir, edb_id_for_filename=edb_id)

            if file_path:
                saved_files_count += 1
                logger.info(f"--- POC EDB:{edb_id} 详情 ---")
                logger.info(f"  保存路径: {file_path}")
                logger.info(f"  标题: {poc_data.get('title', 'N/A')}")
                logger.info(f"  作者: {poc_data.get('author', 'N/A')}")
                logger.info(f"  平台: {poc_data.get('platform', 'N/A')}")
                logger.info(f"  类型: {poc_data.get('type', 'N/A')}")
                code_length = len(poc_data.get('poc_code', ''))
                logger.info(f"  代码长度: {code_length} 字符")
                # 可以在这里添加代码预览逻辑（如果需要）
            else:
                logger.error(f"保存 EDB-ID {edb_id} 的 POC 数据失败")

        if saved_files_count == len(all_poc_data):
            logger.info(f"所有 {saved_files_count} 个 POC 已成功保存。")
        elif saved_files_count > 0:
            logger.warning(f"成功保存了 {saved_files_count} 个 POC，但有 {len(all_poc_data) - saved_files_count} 个保存失败。")
        else:
            logger.error(f"获取到 {len(all_poc_data)} 个 POC 数据，但全部保存失败。")
    else:
        logger.error(f"未能为 {args.cve} 找到或获取任何匹配的 POC")
        # ...(省略之前的建议信息)

if __name__ == '__main__':
    main()