#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库优化脚本

添加索引和执行VACUUM操作，优化数据库性能。
"""

import os
import sys
import sqlite3
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def get_db_path():
    """获取数据库路径"""
    data_dir = os.path.join(project_root, 'data')
    # 确保数据目录存在
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    return os.path.join(data_dir, 'vulnerabilities.db')

def backup_database():
    """备份数据库"""
    db_path = get_db_path()
    backup_path = db_path + '.backup'
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        logger.info(f"数据库已备份到: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"备份数据库失败: {e}")
        return False

def add_indexes():
    """添加数据库索引"""
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    indexes = [
        # 漏洞表索引
        "CREATE INDEX IF NOT EXISTS idx_vulnerabilities_source ON vulnerabilities(source)",
        "CREATE INDEX IF NOT EXISTS idx_vulnerabilities_published_date ON vulnerabilities(published_date)",
        "CREATE INDEX IF NOT EXISTS idx_vulnerabilities_severity ON vulnerabilities(severity)",
        "CREATE INDEX IF NOT EXISTS idx_vulnerabilities_status ON vulnerabilities(status)",
        
        # 软件包表索引
        "CREATE INDEX IF NOT EXISTS idx_packages_name ON packages(name)",
        "CREATE INDEX IF NOT EXISTS idx_packages_ecosystem ON packages(ecosystem)",
        
        # 参考链接表索引 - references是SQLite关键字，需要用方括号
        "CREATE INDEX IF NOT EXISTS idx_references_source ON [references](source)",
        "CREATE INDEX IF NOT EXISTS idx_references_type ON [references](type)",
        
        # 关系表索引
        "CREATE INDEX IF NOT EXISTS idx_vuln_pkg_vuln_id ON vulnerability_package(vulnerability_id)",
        "CREATE INDEX IF NOT EXISTS idx_vuln_pkg_pkg_id ON vulnerability_package(package_id)",
        "CREATE INDEX IF NOT EXISTS idx_vuln_ref_vuln_id ON vulnerability_reference(vulnerability_id)",
        "CREATE INDEX IF NOT EXISTS idx_vuln_ref_ref_id ON vulnerability_reference(reference_id)"
    ]
    
    try:
        for idx_sql in indexes:
            cursor.execute(idx_sql)
            logger.info(f"执行索引创建: {idx_sql}")
        
        # 提交修改
        conn.commit()
        logger.info("所有索引已成功创建")
        return True
    
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()

def vacuum_database():
    """执行VACUUM操作，重新组织数据库文件，减少文件大小"""
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    
    try:
        # SQLite的VACUUM命令
        logger.info("开始执行VACUUM操作...")
        conn.execute("VACUUM")
        logger.info("VACUUM操作完成")
        return True
    
    except Exception as e:
        logger.error(f"VACUUM操作失败: {e}")
        return False
    
    finally:
        conn.close()

def analyze_database():
    """执行ANALYZE操作，更新统计信息，帮助查询优化器做出更好的决策"""
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    
    try:
        # SQLite的ANALYZE命令
        logger.info("开始执行ANALYZE操作...")
        conn.execute("ANALYZE")
        logger.info("ANALYZE操作完成")
        return True
    
    except Exception as e:
        logger.error(f"ANALYZE操作失败: {e}")
        return False
    
    finally:
        conn.close()

def main():
    """主函数"""
    logger.info("开始数据库优化流程...")
    
    # 步骤1: 备份数据库
    if not backup_database():
        logger.error("数据库备份失败，优化终止")
        return
    
    # 步骤2: 添加索引
    if not add_indexes():
        logger.error("添加索引失败，优化终止")
        return
    
    # 步骤3: 执行VACUUM操作
    if not vacuum_database():
        logger.warning("VACUUM操作失败，继续优化流程")
    
    # 步骤4: 执行ANALYZE操作
    if not analyze_database():
        logger.warning("ANALYZE操作失败，继续优化流程")
    
    # 步骤5: 验证优化是否成功
    logger.info("数据库优化完成")

if __name__ == "__main__":
    main() 