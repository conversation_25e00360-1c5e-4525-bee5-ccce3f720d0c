#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据采集器综合测试脚本

测试所有漏洞数据采集器的可用性，包括：
1. NVD数据采集器
2. GitHub安全公告采集器
3. RedHat安全数据采集器
4. De<PERSON>安全跟踪器采集器

每个采集器测试内容包括：
1. 网络连接性
2. API令牌/密钥有效性（如适用）
3. 代理配置有效性
4. 数据获取能力
"""

import os
import sys
import json
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 测试结果存储
TEST_RESULTS = {
    "测试时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    "收集器测试结果": {}
}

def load_config():
    """加载数据源配置"""
    config_path = os.path.join(project_root, 'config', 'sources.json')
    
    if not os.path.exists(config_path):
        logger.error(f"配置文件不存在: {config_path}")
        return None
        
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config.get('sources', {})
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        return None

def test_github_collector(config: Dict[str, Any]) -> Dict[str, Any]:
    """测试GitHub采集器功能"""
    logger.info("开始测试GitHub数据采集器...")
    result = {
        "状态": "失败",
        "连接测试": "未测试",
        "数据获取": "未测试",
        "错误信息": [],
        "数据样本": None,
        "获取数量": 0
    }
    
    try:
        from src.collectors.github import GitHubCollector
        
        # 检查API Token
        api_token = config.get('api_token', '')
        if not api_token:
            msg = "未配置GitHub API令牌"
            logger.warning(msg)
            result["错误信息"].append(msg)
        elif len(api_token) < 35:
            msg = f"GitHub API令牌可能不完整: {api_token[:10]}..."
            logger.warning(msg)
            result["错误信息"].append(msg)
        
        # 检查代理配置
        proxy = config.get('proxy', '')
        if not proxy:
            msg = "未配置代理，访问GitHub API可能会受到限制"
            logger.warning(msg)
            result["错误信息"].append(msg)
        
        # 创建采集器
        collector = GitHubCollector(config)
        
        # 测试连接
        logger.info("正在测试GitHub API连接...")
        if hasattr(collector, 'test_connection') and collector.test_connection():
            logger.info("GitHub API连接测试成功！")
            result["连接测试"] = "成功"
        else:
            msg = "GitHub API连接测试失败"
            logger.error(msg)
            result["连接测试"] = "失败"
            result["错误信息"].append(msg)
            return result
        
        # 测试数据获取
        logger.info("正在测试GitHub数据采集功能...")
        # 获取最近3天的数据以加快测试速度
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        
        data = collector.fetch_data(start_date, end_date)
        if data:
            count = len(data)
            logger.info(f"成功获取 {count} 条GitHub安全公告数据")
            result["数据获取"] = "成功"
            result["获取数量"] = count
            
            # 保存数据样本
            if count > 0:
                sample = data[0]
                sample_info = {
                    "ID": sample.get('id', 'unknown'),
                    "标题": sample.get('summary', '')[:50] + ('...' if len(sample.get('summary', '')) > 50 else ''),
                    "严重性": sample.get('severity', 'unknown'),
                    "发布时间": sample.get('published_date', '')
                }
                result["数据样本"] = sample_info
                logger.info(f"数据样本: {json.dumps(sample_info, ensure_ascii=False)}")
            
            result["状态"] = "成功"
        else:
            msg = "未获取到GitHub安全公告数据"
            logger.warning(msg)
            result["数据获取"] = "失败"
            result["错误信息"].append(msg)
    except Exception as e:
        msg = f"测试GitHub采集器时出错: {str(e)}"
        logger.error(msg)
        result["错误信息"].append(msg)
    
    return result

def test_nvd_collector(config: Dict[str, Any]) -> Dict[str, Any]:
    """测试NVD采集器功能"""
    logger.info("开始测试NVD数据采集器...")
    result = {
        "状态": "失败",
        "连接测试": "未测试",
        "数据获取": "未测试",
        "错误信息": [],
        "数据样本": None,
        "获取数量": 0
    }
    
    try:
        from src.collectors.nvd import NVDCollector
        
        # 检查API Key
        api_key = config.get('api_key', '')
        if api_key == 'your-api-key':
            msg = "使用的是默认API密钥，可能导致请求受限"
            logger.warning(msg)
            result["错误信息"].append(msg)
        
        # 创建采集器
        collector = NVDCollector(config)
        
        # 测试连接和数据获取
        logger.info("正在测试NVD数据采集功能...")
        # 获取最近1天的数据以加快测试速度
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)
        
        data = collector.fetch_data(start_date, end_date)
        if data:
            count = len(data)
            logger.info(f"成功获取 {count} 条NVD漏洞数据")
            result["连接测试"] = "成功"
            result["数据获取"] = "成功"
            result["获取数量"] = count
            
            # 保存数据样本
            if count > 0:
                sample = data[0]
                sample_info = {
                    "ID": sample.get('id', 'unknown'),
                    "标题": sample.get('summary', '')[:50] + ('...' if len(sample.get('summary', '')) > 50 else ''),
                    "严重性": sample.get('severity', 'unknown'),
                    "发布时间": sample.get('published_date', '')
                }
                result["数据样本"] = sample_info
                logger.info(f"数据样本: {json.dumps(sample_info, ensure_ascii=False)}")
            
            result["状态"] = "成功"
        else:
            msg = "未获取到NVD漏洞数据"
            logger.warning(msg)
            result["数据获取"] = "失败"
            result["错误信息"].append(msg)
    except Exception as e:
        msg = f"测试NVD采集器时出错: {str(e)}"
        logger.error(msg)
        result["错误信息"].append(msg)
    
    return result

def test_redhat_collector(config: Dict[str, Any]) -> Dict[str, Any]:
    """测试RedHat采集器功能"""
    logger.info("开始测试RedHat数据采集器...")
    result = {
        "状态": "失败",
        "连接测试": "未测试",
        "数据获取": "未测试",
        "错误信息": [],
        "数据样本": None,
        "获取数量": 0
    }
    
    try:
        from src.collectors.redhat import RedHatCollector
        
        # 创建采集器
        collector = RedHatCollector(config)
        
        # 测试连接和数据获取
        logger.info("正在测试RedHat数据采集功能...")
        # 获取最近3天的数据以加快测试速度
        end_date = datetime.now()
        start_date = end_date - timedelta(days=3)
        
        data = collector.fetch_data(start_date, end_date)
        if data:
            count = len(data)
            logger.info(f"成功获取 {count} 条RedHat漏洞数据")
            result["连接测试"] = "成功"
            result["数据获取"] = "成功"
            result["获取数量"] = count
            
            # 保存数据样本
            if count > 0:
                sample = data[0]
                sample_info = {
                    "ID": sample.get('id', 'unknown'),
                    "标题": sample.get('summary', '')[:50] + ('...' if len(sample.get('summary', '')) > 50 else ''),
                    "严重性": sample.get('severity', 'unknown'),
                    "发布时间": sample.get('published_date', '')
                }
                result["数据样本"] = sample_info
                logger.info(f"数据样本: {json.dumps(sample_info, ensure_ascii=False)}")
            
            result["状态"] = "成功"
        else:
            msg = "未获取到RedHat漏洞数据"
            logger.warning(msg)
            result["数据获取"] = "失败"
            result["错误信息"].append(msg)
    except Exception as e:
        msg = f"测试RedHat采集器时出错: {str(e)}"
        logger.error(msg)
        result["错误信息"].append(msg)
    
    return result

def test_debian_collector(config: Dict[str, Any]) -> Dict[str, Any]:
    """测试Debian采集器功能"""
    logger.info("开始测试Debian数据采集器...")
    result = {
        "状态": "失败",
        "连接测试": "未测试",
        "数据获取": "未测试",
        "错误信息": [],
        "数据样本": None,
        "获取数量": 0
    }
    
    try:
        from src.collectors.debian import DebianCollector
        
        # 创建采集器
        collector = DebianCollector(config)
        
        # 测试连接和数据获取
        logger.info("正在测试Debian数据采集功能...")
        # 获取最近7天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        data = collector.fetch_data(start_date, end_date)
        if data:
            count = len(data)
            logger.info(f"成功获取 {count} 条Debian漏洞数据")
            result["连接测试"] = "成功"
            result["数据获取"] = "成功"
            result["获取数量"] = count
            
            # 保存数据样本
            if count > 0:
                sample = data[0]
                sample_info = {
                    "ID": sample.get('id', 'unknown'),
                    "标题": sample.get('summary', '')[:50] + ('...' if len(sample.get('summary', '')) > 50 else ''),
                    "严重性": sample.get('severity', 'unknown'),
                    "发布时间": sample.get('published_date', ''),
                    "软件包": sample.get('package_name', '')
                }
                result["数据样本"] = sample_info
                logger.info(f"数据样本: {json.dumps(sample_info, ensure_ascii=False)}")
            
            result["状态"] = "成功"
        else:
            msg = "未获取到Debian漏洞数据"
            logger.warning(msg)
            result["数据获取"] = "失败"
            result["错误信息"].append(msg)
    except Exception as e:
        msg = f"测试Debian采集器时出错: {str(e)}"
        logger.error(msg)
        result["错误信息"].append(msg)
    
    return result

def save_test_results():
    """将测试结果保存到文件"""
    try:
        # 创建reports目录
        reports_dir = os.path.join(project_root, 'reports')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        
        # 保存JSON格式
        json_path = os.path.join(reports_dir, 'collectors_test_results.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(TEST_RESULTS, f, ensure_ascii=False, indent=2)
        logger.info(f"测试结果已保存到JSON文件: {json_path}")
        
        # 保存Markdown格式
        md_path = os.path.join(reports_dir, 'collectors_test_results.md')
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write("# 数据采集器测试报告\n\n")
            f.write(f"**测试时间**: {TEST_RESULTS['测试时间']}\n\n")
            
            for collector_name, result in TEST_RESULTS["收集器测试结果"].items():
                f.write(f"## {collector_name}\n\n")
                f.write(f"- **测试状态**: {result['状态']}\n")
                f.write(f"- **连接测试**: {result['连接测试']}\n")
                f.write(f"- **数据获取**: {result['数据获取']}\n")
                f.write(f"- **获取数量**: {result['获取数量']} 条\n")
                
                if result["错误信息"]:
                    f.write("\n### 错误信息\n\n")
                    for err in result["错误信息"]:
                        f.write(f"- {err}\n")
                
                if result["数据样本"]:
                    f.write("\n### 数据样本\n\n")
                    f.write("```json\n")
                    f.write(json.dumps(result["数据样本"], ensure_ascii=False, indent=2))
                    f.write("\n```\n")
                
                f.write("\n\n")
            
            f.write("## 测试总结\n\n")
            
            # 计算成功率
            total = len(TEST_RESULTS["收集器测试结果"])
            success = sum(1 for result in TEST_RESULTS["收集器测试结果"].values() if result["状态"] == "成功")
            f.write(f"- **测试采集器总数**: {total}\n")
            f.write(f"- **成功数量**: {success}\n")
            f.write(f"- **失败数量**: {total - success}\n")
            f.write(f"- **成功率**: {success/total*100:.1f}%\n")
            
        logger.info(f"测试结果已保存到Markdown文件: {md_path}")
        
        return True
    except Exception as e:
        logger.error(f"保存测试结果时出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("开始测试所有数据采集器...")
    
    # 加载配置
    configs = load_config()
    if not configs:
        logger.error("无法加载数据源配置")
        return False
    
    # 测试GitHub采集器
    if "GitHub" in configs:
        TEST_RESULTS["收集器测试结果"]["GitHub"] = test_github_collector(configs["GitHub"])
        # 测试完成后暂停2秒，避免连续请求
        time.sleep(2)
    else:
        logger.warning("配置中未找到GitHub数据源")
    
    # 测试NVD采集器
    if "NVD" in configs:
        TEST_RESULTS["收集器测试结果"]["NVD"] = test_nvd_collector(configs["NVD"])
        time.sleep(2)
    else:
        logger.warning("配置中未找到NVD数据源")
    
    # 测试RedHat采集器
    if "RedHat" in configs:
        TEST_RESULTS["收集器测试结果"]["RedHat"] = test_redhat_collector(configs["RedHat"])
        time.sleep(2)
    else:
        logger.warning("配置中未找到RedHat数据源")
    
    # 测试Debian采集器
    if "Debian" in configs:
        TEST_RESULTS["收集器测试结果"]["Debian"] = test_debian_collector(configs["Debian"])
    else:
        logger.warning("配置中未找到Debian数据源")
    
    # 保存测试结果
    save_test_results()
    
    # 输出简要结果
    logger.info("所有采集器测试完成，测试结果摘要：")
    for collector, result in TEST_RESULTS["收集器测试结果"].items():
        logger.info(f"{collector}: {result['状态']} (获取 {result['获取数量']} 条数据)")
    
    # 计算成功率
    total = len(TEST_RESULTS["收集器测试结果"])
    success = sum(1 for result in TEST_RESULTS["收集器测试结果"].values() if result["状态"] == "成功")
    logger.info(f"采集器测试成功率: {success}/{total} ({success/total*100:.1f}%)")
    
    return True

if __name__ == "__main__":
    main() 