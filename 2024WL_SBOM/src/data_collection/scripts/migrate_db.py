#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库迁移脚本

用于修复数据库中的重复记录问题，添加唯一性约束。
"""

import os
import sys
import sqlite3
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.models.database import Database

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def get_db_path():
    """获取数据库路径"""
    data_dir = os.path.join(project_root, 'data')
    # 确保数据目录存在
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    return os.path.join(data_dir, 'vulnerabilities.db')

def backup_database():
    """备份数据库"""
    db_path = get_db_path()
    backup_path = db_path + '.backup'
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        logger.info(f"数据库已备份到: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"备份数据库失败: {e}")
        return False

def remove_duplicate_relations():
    """删除重复的多对多关系记录"""
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 查找并删除vulnerability_package表中的重复记录
        logger.info("清理vulnerability_package表中的重复记录...")
        cursor.execute("""
            DELETE FROM vulnerability_package
            WHERE rowid NOT IN (
                SELECT MIN(rowid)
                FROM vulnerability_package
                GROUP BY vulnerability_id, package_id
            )
        """)
        pkg_deleted = cursor.rowcount
        logger.info(f"删除了 {pkg_deleted} 条重复的vulnerability_package记录")
        
        # 查找并删除vulnerability_reference表中的重复记录
        logger.info("清理vulnerability_reference表中的重复记录...")
        cursor.execute("""
            DELETE FROM vulnerability_reference
            WHERE rowid NOT IN (
                SELECT MIN(rowid)
                FROM vulnerability_reference
                GROUP BY vulnerability_id, reference_id
            )
        """)
        ref_deleted = cursor.rowcount
        logger.info(f"删除了 {ref_deleted} 条重复的vulnerability_reference记录")
        
        # 提交修改
        conn.commit()
        return pkg_deleted + ref_deleted
    
    except Exception as e:
        logger.error(f"清理重复记录失败: {e}")
        conn.rollback()
        return 0
    
    finally:
        conn.close()

def recreate_tables_with_constraints():
    """重新创建数据库表，添加唯一性约束"""
    # 获取数据库实例
    db_path = get_db_path()
    db = Database(f'sqlite:///{db_path}')
    
    try:
        # 先删除所有表，然后重新创建
        logger.info("重新创建数据库表，添加唯一性约束...")
        db.drop_tables()
        db.create_tables()
        logger.info("数据库表已成功重新创建")
        return True
    
    except Exception as e:
        logger.error(f"重新创建数据库表失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始数据库迁移流程...")
    
    # 步骤1: 备份数据库
    if not backup_database():
        logger.error("数据库备份失败，迁移终止")
        return
    
    # 步骤2: 清理重复数据
    total_deleted = remove_duplicate_relations()
    logger.info(f"总共清理了 {total_deleted} 条重复记录")
    
    # 步骤3: 验证迁移是否成功
    logger.info("数据库迁移完成")
    logger.info("注意：如果需要重新创建数据库结构以添加唯一性约束，请手动删除数据库文件，然后重新运行采集程序")

if __name__ == "__main__":
    main() 