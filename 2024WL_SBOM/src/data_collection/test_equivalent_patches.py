#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
等价补丁收集功能测试脚本
"""

import os
import sys
import json
import logging
import time
import argparse
import pathlib
from pprint import pprint

# 添加项目根目录到sys.path
sys.path.insert(0, "/home/<USER>")

from src.data_collection.services.patch_tracer.tracer import PatchTracer

def load_github_token_from_sources():
    """从sources.json配置文件中读取GitHub API密钥"""
    # 找到配置文件路径
    current_dir = pathlib.Path(__file__).parent.absolute()
    sources_file = current_dir / "config" / "sources.json"
    
    if not sources_file.exists():
        logger.warning(f"配置文件 {sources_file} 不存在")
        return None
    
    try:
        with open(sources_file, "r") as f:
            config = json.load(f)
            # 根据实际sources.json的结构读取API密钥
            return config.get("sources", {}).get("GitHub", {}).get("api_key")
    except Exception as e:
        logger.error(f"读取GitHub API令牌失败: {e}")
        return None

def main():
    """脚本主函数"""
    parser = argparse.ArgumentParser(description="等价补丁收集功能测试")
    parser.add_argument("--cve-id", required=True, help="CVE ID")
    parser.add_argument("--output-dir", required=True, help="输出目录")
    parser.add_argument("--max-depth", type=int, default=3, help="最大追踪深度")
    parser.add_argument("--log-level", default="INFO", 
                      choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                      help="日志级别")
    args = parser.parse_args()
    
    # 确保CVE ID格式为大写
    cve_id = args.cve_id.upper()
    
    # 设置日志级别
    log_level = getattr(logging, args.log_level)
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    # 创建输出目录
    output_dir = pathlib.Path(args.output_dir)
    if not output_dir.exists():
        output_dir.mkdir(parents=True)
    
    # CVE ID到初始补丁URL的映射
    initial_patches = {
        "CVE-2021-44228": "https://github.com/apache/logging-log4j2/commit/823bf97da4d97c20267a7966e99dc462bcd0d9a0",
        # 添加CVE-2023-0286的初始引用
        "CVE-2023-0286": "https://github.com/openssl/openssl/commit/2c6c9d439b484e1ba9830d8454a34fa4f80fdfe9",
        # 添加CVE-2023-26545的初始引用
        "CVE-2023-26545": "https://github.com/torvalds/linux/commit/fda6c89fe3d9aca073495a664e1d5aea28cd4377",
    }
    
    # 检查是否有已知的初始补丁URL
    if cve_id not in initial_patches:
        logger.error(f"没有为 {cve_id} 配置初始补丁URL，请先运行patch-trace命令")
        sys.exit(1)
    
    # 获取GitHub API令牌
    github_token = load_github_token_from_sources()
    if not github_token:
        logger.warning("未找到GitHub API令牌，可能会受到API限制")
    
    # 创建补丁跟踪器实例
    tracer = PatchTracer(github_token=github_token)
    
    # 获取等价补丁
    start_time = time.time()
    logger.info(f"开始查找 {cve_id} 的等价补丁...")
    
    # 使用已知的初始补丁URL
    patch_url = initial_patches[cve_id]
    logger.info(f"使用初始补丁URL: {patch_url}")
    
    # 查找等价补丁
    equivalent_patches = tracer._find_equivalent_patches(
        cve_id,
        [{"url": patch_url}],
        max_candidates=100
    )
    
    # 计算耗时
    elapsed_time = time.time() - start_time
    logger.info(f"查找等价补丁完成，耗时 {elapsed_time:.2f} 秒")
    
    # 输出结果
    result_count = sum(len(patches.get("equivalent_patches", [])) for url, patches in equivalent_patches.items())
    logger.info(f"共找到 {result_count} 个等价补丁")
    
    # 检查结果是否合理
    for url, patches in equivalent_patches.items():
        if "equivalent_patches" not in patches or not patches["equivalent_patches"]:
            logger.warning(f"找不到原始补丁 {url} 的等价补丁，可能原因:")
            logger.warning(f"1. 补丁未修改C/C++文件")
            logger.warning(f"2. 该CVE没有多个等价的修复提交")
            logger.warning(f"3. GitHub API访问受限")
            continue
            
        base_patch = patches.get("original_patch")
        if base_patch:
            logger.info(f"原始补丁: {base_patch['url']}")
        
            # 列出所有等价补丁
            for i, patch in enumerate(patches["equivalent_patches"]):
                # 跳过原始补丁
                if patch["reason"] == "original_base_patch":
                    continue
                
                logger.info(f"等价补丁 {i}: {patch['url']} ({patch['reason']})")
        else:
            logger.warning(f"原始补丁 {url} 没有original_patch字段")
    
    # 保存结果到输出目录
    result_file = output_dir / f"{cve_id.replace('-', '_')}_equivalent_patches.json"
    with open(result_file, "w") as f:
        json.dump(equivalent_patches, f, indent=2)
    
    logger.info(f"结果已保存到 {result_file}")

if __name__ == "__main__":
    main() 