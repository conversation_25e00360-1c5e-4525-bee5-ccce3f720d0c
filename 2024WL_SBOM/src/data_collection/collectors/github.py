#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub安全公告采集器模块

实现了从GitHub Security Advisory采集漏洞数据的功能。
使用GitHub GraphQL API v4。
API文档：https://docs.github.com/en/graphql
"""

from datetime import datetime
from typing import Dict, List, Any
import logging
import time
import json
import requests

from .base import BaseVulnerabilityCollector

logger = logging.getLogger(__name__)

class GitHubCollector(BaseVulnerabilityCollector):
    """
    GitHub安全公告采集器
    
    通过GitHub GraphQL API获取安全公告数据。
    支持按时间范围查询和增量更新。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化GitHub采集器
        
        Args:
            config: 配置信息，除基类配置外，还可包含：
                - api_token: GitHub API令牌
                - per_page: 每页结果数（默认100）
                - max_pages: 最大页数（默认无限制）
                - max_results: 最大结果数（默认无限制）
        """
        # 将api_token转换为api_key，以兼容基类
        if 'api_token' in config and 'api_key' not in config:
            config['api_key'] = config['api_token']
            
        super().__init__(config)
        self.per_page = config.get('per_page', 100)
        self.max_pages = config.get('max_pages', float('inf'))  # 默认无限制
        self.max_results = config.get('max_results', float('inf'))  # 默认无限制
        
        # 确保日志配置正确
        from ..utils.logger import logger
        
        # 打印启动信息，确保日志能够正常显示
        print("========== GitHub漏洞采集器已启动 ==========")
        print(f"每页记录数: {self.per_page}, 请求间隔: {self.delay}秒")
        if self.max_pages < float('inf'):
            print(f"最大页数限制: {self.max_pages}页")
        if self.max_results < float('inf'):
            print(f"最大结果数限制: {self.max_results}条")
        print("============================================")
        
        # 验证API令牌
        if not self.api_key:
            logger.warning("未提供GitHub API令牌，API请求可能受到严格的速率限制")
        elif len(self.api_key) < 35:  # GitHub API tokens通常很长
            logger.warning(f"提供的GitHub API令牌可能不完整: {self.api_key[:10]}...")
        
        # GraphQL API端点
        self.url = 'https://api.github.com/graphql'
        
    def make_api_request(self, 
                        endpoint: str, 
                        method: str = "POST", 
                        params: Dict[str, Any] = None,
                        headers: Dict[str, str] = None,
                        data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        重写基类方法，为GitHub API添加特定的认证头
        
        Args:
            endpoint: API端点
            method: 请求方法，默认为"POST"（GraphQL使用POST）
            params: 查询参数
            headers: 请求头
            data: 请求体数据
            
        Returns:
            API响应数据
        """
        if headers is None:
            headers = {}
            
        # GitHub API需要特定格式的Authorization头
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        # 添加GitHub API版本头
        headers['Accept'] = 'application/vnd.github.v4+json'
        
        # 调用基类的请求方法
        return super().make_api_request(
            endpoint=endpoint,
            method=method,
            params=params,
            headers=headers,
            data=data
        )
        
    def fetch_data(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内的GitHub安全公告数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含漏洞信息的字典列表
            
        Raises:
            ValueError: 日期范围无效时
            RequestError: API请求失败时
        """
        # 验证日期范围
        self.validate_date_range(start_date, end_date)
        
        all_advisories = []
        cursor = None
        page_num = 1
        start_time = datetime.now()
        total_pages = 0
        estimated_total = 0
        
        try:
            logger.info(f"【开始采集】GitHub安全公告 - 时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            logger.info(f"【配置信息】每页记录数: {self.per_page}, 请求间隔: {self.delay}秒, 最大重试次数: {self.max_retries}")
            
            # 输出限制信息
            if self.max_pages < float('inf'):
                logger.info(f"【限制设置】最大页数限制: {self.max_pages}页")
            if self.max_results < float('inf'):
                logger.info(f"【限制设置】最大结果数限制: {self.max_results}条")
                
            if self.api_key:
                logger.info(f"【认证状态】已使用API令牌 (令牌前缀: {self.api_key[:4]}...)")
            else:
                logger.warning(f"【认证状态】未使用API令牌，将受到严格的API请求限制!")
            
            while True:
                # 检查页数限制
                if page_num > self.max_pages:
                    logger.info(f"【达到限制】已达到最大页数限制 ({self.max_pages}页)，停止采集")
                    break
                    
                # 构建GraphQL查询
                query = self._build_graphql_query(
                    start_date=start_date,
                    end_date=end_date,
                    cursor=cursor,
                    per_page=self.per_page
                )
                
                logger.debug(f"GraphQL查询: {query}")
                logger.info(f"【请求页面】第{page_num}页数据，游标: {cursor if cursor else '起始页'}")
                
                # 发送API请求 - 对于GraphQL API，直接使用完整URL
                headers = {'Authorization': f'Bearer {self.api_key}',
                          'Accept': 'application/vnd.github.v4+json'}
                
                # 这里不使用make_api_request，而是直接使用make_request
                from ..utils.http import make_request, handle_rate_limit
                
                try:
                    # 设置代理参数
                    proxies = None
                    if self.proxy:
                        logger.info(f"【网络配置】使用代理: {self.proxy}")
                        proxies = {
                            'http': self.proxy,
                            'https': self.proxy
                        }
                    else:
                        logger.info("【网络配置】直接连接，不使用代理")
                    
                    # 添加请求重试机制
                    max_attempts = 3
                    for attempt in range(1, max_attempts + 1):
                        try:
                            logger.info(f"【API请求】第{page_num}页，第{attempt}/{max_attempts}次尝试")
                            request_start = datetime.now()
                            response = make_request(
                                url=self.url,
                                method='POST',
                                headers=headers,
                                json={'query': query},  # 使用json参数而不是data
                                timeout=self.timeout,
                                max_retries=self.max_retries,
                                proxies=proxies  # 添加代理配置
                            )
                            request_time = (datetime.now() - request_start).total_seconds()
                            
                            # 处理限流
                            rate_info = handle_rate_limit(response)
                            remaining = rate_info.get('remaining', 'unknown')
                            reset_time = rate_info.get('reset_time', 'unknown')
                            logger.info(f"【API响应】状态码: {response.status_code}, 请求耗时: {request_time:.2f}秒, 剩余请求配额: {remaining}, 配额重置时间: {reset_time}")
                            break  # 成功则跳出重试循环
                            
                        except requests.exceptions.ConnectionError as ce:
                            if attempt < max_attempts:
                                wait_time = attempt * 10  # 递增等待时间
                                logger.warning(f"【网络错误】连接失败(尝试 {attempt}/{max_attempts}): {str(ce)}. 等待 {wait_time} 秒后重试...")
                                time.sleep(wait_time)
                            else:
                                logger.error(f"【网络错误】连接失败，达到最大重试次数: {str(ce)}")
                                raise
                        except requests.exceptions.Timeout as te:
                            if attempt < max_attempts:
                                wait_time = attempt * 5
                                logger.warning(f"【请求超时】(尝试 {attempt}/{max_attempts}): {str(te)}. 等待 {wait_time} 秒后重试...")
                                time.sleep(wait_time)
                            else:
                                logger.error(f"【请求超时】达到最大重试次数: {str(te)}")
                                raise
                        except Exception as e:
                            logger.error(f"【请求错误】: {str(e)}")
                            raise
                    
                    # 请求间隔
                    logger.info(f"【请求间隔】等待 {self.delay} 秒后继续...")
                    time.sleep(self.delay)
                    
                    # 解析响应
                    logger.info(f"【数据解析】开始解析第{page_num}页API响应...")
                    response_data = response.json()
                    if 'data' not in response_data:
                        logger.debug(f"完整响应内容: {json.dumps(response_data)[:2000]}...")
                    
                    # 检查API错误
                    if 'errors' in response_data:
                        errors = response_data['errors']
                        error_msgs = [err.get('message', 'Unknown error') for err in errors if isinstance(err, dict)]
                        logger.error(f"【API错误】GitHub返回错误: {', '.join(error_msgs)}")
                        logger.debug(f"完整错误信息: {json.dumps(errors)}")
                        break
                    
                    # 提取安全公告数据
                    data = response_data.get('data', {})
                    if not data:
                        logger.warning("【数据缺失】API响应中没有数据字段")
                        break
                        
                    security_advisories = data.get('securityAdvisories', {})
                    if not security_advisories:
                        logger.warning("【数据缺失】API响应中没有安全公告数据")
                        break
                        
                    nodes = security_advisories.get('nodes', [])
                    count = len(nodes)
                    total_count = security_advisories.get('totalCount', 0)
                    
                    # 估计总页数
                    if page_num == 1 and total_count > 0:
                        estimated_total = total_count
                        estimated_pages = (total_count + self.per_page - 1) // self.per_page
                        logger.info(f"【数据统计】API报告共有 {total_count} 条安全公告，预计需要 {estimated_pages} 页")
                    
                    logger.info(f"【页面数据】第{page_num}页获取到 {count} 条安全公告{f'，总计 {total_count} 条' if total_count else ''}")
                    
                    if count == 0:
                        logger.info("【采集完成】本页没有安全公告数据，结束获取")
                        break
                    
                    # 更新统计信息
                    total_pages = page_num
                    
                    # 处理分页信息
                    page_info = security_advisories.get('pageInfo', {})
                    has_next_page = page_info.get('hasNextPage', False)
                    
                    # 更新游标
                    if has_next_page:
                        cursor = page_info.get('endCursor')
                        logger.info(f"【分页信息】有下一页数据，下一页游标: {cursor}")
                    else:
                        logger.info("【分页信息】没有下一页数据，采集将结束")
                        
                    # 处理公告数据
                    logger.info(f"【数据处理】开始处理第{page_num}页的 {count} 条安全公告...")
                    processed_count = 0
                    valid_count = 0
                    for i, node in enumerate(nodes):
                        try:
                            # 检查是否达到最大结果数限制
                            if len(all_advisories) >= self.max_results:
                                logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止处理")
                                break
                                
                            # 直接打印原始数据，确保能看到
                            node_id = node.get('ghsaId', f'未知ID_{i+1}')
                            print(f"\n\n开始处理第 {i+1}/{count} 个安全公告: {node_id}")
                            
                            vulnerability = self._process_advisory(node)
                            processed_count += 1
                            
                            if vulnerability and vulnerability.get('id'):
                                all_advisories.append(vulnerability)
                                valid_count += 1
                                print(f"安全公告 {vulnerability.get('id')} 处理成功!")
                            else:
                                print(f"安全公告 {node_id} 处理失败或无效!")
                            
                            if (i+1) % 10 == 0 or i+1 == count:
                                percent_page = (i+1) / count * 100
                                if estimated_total > 0:
                                    percent_total = (len(all_advisories) / estimated_total) * 100
                                    logger.info(f"【处理进度】页内: {i+1}/{count} ({percent_page:.1f}%)，总进度: {len(all_advisories)}/{estimated_total} ({percent_total:.1f}%)")
                                else:
                                    logger.info(f"【处理进度】页内: {i+1}/{count} ({percent_page:.1f}%)")
                        except Exception as e:
                            error_msg = f"【处理错误】处理第{i+1}条安全公告时出错: {str(e)}"
                            logger.error(error_msg)
                            print(error_msg)
                    
                    logger.info(f"【处理结果】第{page_num}页处理完成，成功处理 {processed_count} 条，有效记录 {valid_count} 条")
                    
                    # 检查是否达到最大结果数限制
                    if len(all_advisories) >= self.max_results:
                        logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止采集")
                        break
                    
                    page_num += 1
                    
                    # 如果没有下一页，则退出循环
                    if not has_next_page:
                        break
                        
                except Exception as e:
                    logger.error(f"【处理异常】处理API响应时出错: {str(e)}")
                    break
            
            # 统计信息
            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"【采集完成】GitHub安全公告采集结束，总用时: {duration:.1f}秒")
            logger.info(f"【结果统计】共获取 {len(all_advisories)} 条有效安全公告数据，采集 {total_pages} 页")
            logger.info(f"【性能指标】平均每页耗时: {duration/max(total_pages,1):.1f}秒，平均每条记录耗时: {duration/max(len(all_advisories),1):.2f}秒")
            
            return all_advisories
            
        except Exception as e:
            logger.error(f"【严重错误】GitHub数据采集失败: {str(e)}")
            if all_advisories:
                logger.info(f"【部分数据】在失败前已采集 {len(all_advisories)} 条数据")
            return all_advisories
            
    def _build_graphql_query(self, start_date: datetime, end_date: datetime, 
                           cursor: str = None, per_page: int = 100) -> str:
        """
        构建GraphQL查询语句
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            cursor: 分页游标
            per_page: 每页结果数
            
        Returns:
            GraphQL查询语句
        """
        after = f'after: "{cursor}"' if cursor else ''
        # 根据GitHub API文档，只使用updatedSince参数
        query = f'''
        {{
          securityAdvisories(first: {per_page} {after}
            orderBy: {{field: UPDATED_AT, direction: ASC}}
            updatedSince: "{start_date.isoformat()}Z"
          ) {{
            pageInfo {{
              hasNextPage
              endCursor
            }}
            nodes {{
              ghsaId
              summary
              description
              severity
              publishedAt
              updatedAt
              withdrawnAt
              references {{
                url
              }}
              identifiers {{
                type
                value
              }}
              vulnerabilities(first: 10) {{
                nodes {{
                  package {{
                    ecosystem
                    name
                  }}
                  vulnerableVersionRange
                  firstPatchedVersion {{
                    identifier
                  }}
                }}
              }}
            }}
          }}
        }}
        '''
        return query
        
    def clean_data(self, advisory: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理并标准化安全公告数据
        
        Args:
            advisory: 原始安全公告数据
            
        Returns:
            清理后的漏洞信息字典
        """
        # 使用专门的处理方法
        return self._process_advisory(advisory)
        
    def _extract_affected_packages(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从安全公告中提取受影响的包信息（已不再使用，功能已整合到_process_advisory方法）
        
        Args:
            data: 安全公告数据
            
        Returns:
            受影响包信息列表
        """
        # 此方法保留用于向下兼容，实际处理已移到_process_advisory方法
        logger.debug("_extract_affected_packages方法已弃用，使用_process_advisory方法代替")
        return []

    def test_connection(self) -> bool:
        """
        测试与GitHub API的连接
        
        发送一个简单的GraphQL查询以验证：
        1. API令牌是否有效
        2. 网络连接是否正常
        3. 代理配置是否有效
        
        Returns:
            连接是否成功
        """
        logger.info("测试GitHub API连接...")
        
        # 简单的GraphQL查询，只获取仓库信息
        test_query = """
        {
          viewer {
            login
            name
          }
        }
        """
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Accept': 'application/vnd.github.v4+json'
        }
        
        # 设置代理参数
        proxies = None
        if self.proxy:
            logger.info(f"使用代理测试GitHub API: {self.proxy}")
            proxies = {
                'http': self.proxy,
                'https': self.proxy
            }
        
        try:
            from ..utils.http import make_request
            
            response = make_request(
                url=self.url,
                method='POST',
                headers=headers,
                json={'query': test_query},
                timeout=self.timeout,
                max_retries=2,
                proxies=proxies
            )
            
            data = response.json()
            
            if 'errors' in data:
                error_msg = data['errors'][0]['message'] if data['errors'] else "未知错误"
                logger.error(f"GitHub API测试失败: {error_msg}")
                return False
            
            if 'data' in data and 'viewer' in data['data']:
                viewer = data['data']['viewer']
                username = viewer.get('login', 'unknown')
                logger.info(f"GitHub API连接成功! 认证用户: {username}")
                return True
            
            logger.warning("GitHub API返回了意外的响应格式")
            return False
            
        except Exception as e:
            logger.error(f"测试GitHub API连接时出错: {str(e)}")
            return False 

    def _process_advisory(self, advisory: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理安全公告数据
        
        Args:
            advisory: 安全公告数据
            
        Returns:
            处理后的漏洞信息字典
        """
        try:
            # 防止advisory为None
            if advisory is None:
                logger.error("安全公告数据为None")
                return {}
                
            # 提取基本信息
            ghsa_id = advisory.get('ghsaId', '')
            
            # 使用print直接输出到控制台，确保能看到信息
            print(f"\n\n================ 安全公告 {ghsa_id} ================")
            
            # 提取发布和更新日期
            published_at = advisory.get('publishedAt')
            updated_at = advisory.get('updatedAt')
            
            # 转换日期格式
            published_date = None
            updated_date = None
            
            if published_at:
                try:
                    published_date = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
                except Exception as e:
                    logger.warning(f"无法解析发布日期 {published_at}: {str(e)}")
            
            if updated_at:
                try:
                    updated_date = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                except Exception as e:
                    logger.warning(f"无法解析更新日期 {updated_at}: {str(e)}")
            
            # 提取CVE ID (如果有)
            cve_ids = []
            identifiers = advisory.get('identifiers', []) or []
            for identifier in identifiers:
                if identifier is not None and identifier.get('type') == 'CVE':
                    cve_ids.append(identifier.get('value', ''))
            
            # 获取摘要和描述
            summary = advisory.get('summary', '无摘要')
            description = advisory.get('description', '无详细描述')
            severity = advisory.get('severity', '未知').lower()
            
            # 提取受影响的包信息
            vulnerable_packages = []
            
            # 安全获取vulnerabilities和nodes
            vulnerabilities = advisory.get('vulnerabilities')
            if vulnerabilities is not None:
                nodes = vulnerabilities.get('nodes', []) or []
                
                for vuln in nodes:
                    if vuln is None:
                        continue
                        
                    package_info = vuln.get('package')
                    if package_info is None:
                        continue
                    
                    # 仅处理有效的包信息
                    package_name = package_info.get('name', '')
                    ecosystem = package_info.get('ecosystem', '')
                    
                    vulnerable_version_range = vuln.get('vulnerableVersionRange', '')
                    
                    # 跳过无效的包信息
                    if not package_name or not ecosystem:
                        continue
                    
                    # 安全获取firstPatchedVersion
                    first_patched = vuln.get('firstPatchedVersion')
                    first_patched_version = ''
                    if first_patched is not None:
                        first_patched_version = first_patched.get('identifier', '')
                    
                    # 添加到受影响包列表
                    vulnerable_packages.append({
                        'name': package_name,
                        'ecosystem': ecosystem,
                        'vulnerable_version_range': vulnerable_version_range,
                        'first_patched_version': first_patched_version
                    })
            
            # 提取引用信息
            references = []
            refs = advisory.get('references', []) or []
            for ref in refs:
                if ref is not None:
                    references.append({
                        'url': ref.get('url', ''),
                        'type': 'advisory'
                    })
                    
            # 直接输出到控制台
            cves_str = ", ".join(cve_ids) if cve_ids else "无CVE ID"
            packages_str = "\n  - ".join([f"{p['ecosystem']}:{p['name']} (受影响版本: {p['vulnerable_version_range']})" for p in vulnerable_packages]) if vulnerable_packages else "无受影响包"
            
            # 使用print确保信息显示
            print(f"漏洞ID: {ghsa_id}")
            print(f"关联CVE: {cves_str}")
            print(f"严重性: {severity}")
            print(f"发布日期: {published_date}")
            print(f"更新日期: {updated_date}")
            print(f"摘要: {summary[:200]}{'...' if len(summary) > 200 else ''}")
            print(f"受影响包:\n  - {packages_str}")
            if references and len(references) > 0:
                print(f"参考链接: {references[0].get('url', '无')}")
            print("=" * 60 + "\n")
            
            # 构建漏洞信息字典
            vulnerability = {
                'id': ghsa_id,
                'source': 'github',
                'published_date': published_date,
                'updated_date': updated_date,
                'summary': summary,
                'description': description,
                'severity': severity,
                'references': references,
                'affected_packages': vulnerable_packages,
                'cve_ids': cve_ids,
                'raw_data': advisory
            }
            
            # 同时也通过logger输出，以防print有问题
            logger.info(f"【安全公告】ID: {ghsa_id}, CVE: {cves_str}, 严重性: {severity}")
            logger.info(f"【漏洞摘要】{summary[:100]}{'...' if len(summary) > 100 else ''}")
            logger.info(f"【受影响包】{packages_str}")
            logger.info(f"【漏洞日期】发布: {published_date}, 更新: {updated_date}")
            if references:
                logger.info(f"【参考链接】{references[0].get('url', '无参考链接')}")
            logger.info("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
            
            return vulnerability
        
        except Exception as e:
            logger.error(f"处理安全公告时出错: {str(e)}")
            logger.debug(f"处理失败的安全公告数据: {advisory}")
            print(f"处理安全公告时出错: {str(e)}")
            return {} 