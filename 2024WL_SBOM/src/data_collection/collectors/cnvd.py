import requests
from lxml import etree
import copy
import json
import time
import os
import sqlite3
import asyncio
import random
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError


class CNVDCrawler:
    class CookieFetcher:
        def __init__(self):
            self.browser = None
            self.context = None
            self.page = None
            self.playwright = None
            self.user_agent = None

        async def init_browser(self):
            """初始化Playwright浏览器"""
            max_retries = 3
            retries = 0
            
            while retries < max_retries:
                try:
                    self.playwright = await async_playwright().start()
                    
                    # 随机选择一个User-Agent
                    user_agents = [
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
                        'Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
                    ]
                    self.user_agent = random.choice(user_agents)
                    
                    # 启动浏览器，设置无头模式和其他参数
                    self.browser = await self.playwright.chromium.launch(
                        headless=True,
                        args=[
                            '--disable-blink-features=AutomationControlled',
                            '--no-sandbox',
                            '--disable-setuid-sandbox',
                            '--disable-infobars',
                            '--disable-dev-shm-usage',
                            '--disable-web-security',
                            '--ignore-certificate-errors',
                            '--ignore-certificate-errors-spki-list'
                        ]
                    )
                    
                    # 创建上下文，设置User-Agent
                    self.context = await self.browser.new_context(
                        user_agent=self.user_agent,
                        viewport={'width': 1920, 'height': 1080},
                        java_script_enabled=True,
                        ignore_https_errors=True  # 忽略HTTPS错误
                    )
                    
                    # 创建新页面
                    self.page = await self.context.new_page()
                    
                    # 设置超时时间
                    self.page.set_default_timeout(60000)  # 增加超时时间
                    
                    # 访问目标网站
                    print("正在访问目标网站，获取cookies...")
                    await self.page.goto("https://www.cnvd.org.cn/flaw/list", wait_until="networkidle")
                    
                    # 等待页面加载完成
                    await asyncio.sleep(5)
                    
                    # 处理可能的521错误
                    await self._handle_521_error()
                    
                    # 如果成功初始化，退出循环
                    print("浏览器初始化成功")
                    return
                    
                except Exception as e:
                    print(f"浏览器初始化错误: {e}")
                    # 清理资源
                    if self.page:
                        await self.page.close()
                    if self.context:
                        await self.context.close()
                    if self.browser:
                        await self.browser.close()
                    if self.playwright:
                        await self.playwright.stop()
                        
                    retries += 1
                    if retries < max_retries:
                        print(f"正在重试初始化浏览器 ({retries}/{max_retries})...")
                        await asyncio.sleep(5)
                    else:
                        print("浏览器初始化失败，已达到最大重试次数")
                        raise Exception("无法初始化Playwright浏览器")

        async def _handle_521_error(self):
            """处理521错误，执行动态脚本"""
            try:
                # 检查页面内容是否包含521错误
                content = await self.page.content()
                if "521" in content and "Web server is down" in content:
                    print("检测到521错误，等待动态脚本执行...")
                    
                    # 等待页面加载完成
                    await self.page.wait_for_load_state("networkidle")
                    
                    # 等待一段时间，让动态脚本执行
                    await asyncio.sleep(10)  # 增加等待时间
                    
                    # 再次检查页面内容
                    content = await self.page.content()
                    if "521" in content and "Web server is down" in content:
                        print("动态脚本执行后仍返回521错误，尝试刷新页面...")
                        await self.page.reload(wait_until="networkidle")
                        await asyncio.sleep(5)
            except PlaywrightTimeoutError:
                print("等待页面加载超时，继续执行...")
            except Exception as e:
                print(f"处理521错误时发生异常: {e}")
                # 继续执行，不要因为这里的错误停止整个流程

        async def new_cookie(self, url="https://www.cnvd.org.cn/flaw/list"):
            """获取新的cookie"""
            if not self.page:
                await self.init_browser()
            
            try:
                # 访问目标URL
                await self.page.goto(url, wait_until="networkidle")
                
                # 处理可能的521错误
                await self._handle_521_error()
                
                # 等待页面加载完成
                await asyncio.sleep(3)
                
                # 获取cookies
                cookies = await self.context.cookies()
                
                # 提取需要的cookie
                c = {}
                for cookie in cookies:
                    if cookie['name'] == '__jsl_clearance_s':
                        c['__jsl_clearance_s'] = cookie['value']
                    elif cookie['name'] == 'JSESSIONID':
                        c['JSESSIONID'] = cookie['value']
                    elif cookie['name'] == '__jsluid_s':
                        c['__jsluid_s'] = cookie['value']
                
                # 检查是否获取到所有需要的cookie
                if '__jsl_clearance_s' in c and 'JSESSIONID' in c and '__jsluid_s' in c:
                    return c
                else:
                    print("未能获取到所有需要的cookie，尝试重新获取...")
                    # 关闭当前页面和上下文
                    await self.page.close()
                    await self.context.close()
                    
                    # 重新创建上下文和页面
                    self.context = await self.browser.new_context(
                        user_agent=self.user_agent,
                        viewport={'width': 1920, 'height': 1080},
                        java_script_enabled=True
                    )
                    self.page = await self.context.new_page()
                    self.page.set_default_timeout(30000)
                    
                    # 重新访问目标网站
                    await self.page.goto(url, wait_until="networkidle")
                    await self._handle_521_error()
                    await asyncio.sleep(5)
                    
                    # 再次获取cookies
                    cookies = await self.context.cookies()
                    c = {}
                    for cookie in cookies:
                        if cookie['name'] == '__jsl_clearance_s':
                            c['__jsl_clearance_s'] = cookie['value']
                        elif cookie['name'] == 'JSESSIONID':
                            c['JSESSIONID'] = cookie['value']
                        elif cookie['name'] == '__jsluid_s':
                            c['__jsluid_s'] = cookie['value']
                    
                    if '__jsl_clearance_s' in c and 'JSESSIONID' in c and '__jsluid_s' in c:
                        return c
                    else:
                        print("多次尝试后仍未能获取到所有需要的cookie")
                        return False
            except Exception as e:
                print(f"获取cookie时出错: {e}")
                return False

        async def close_browser(self):
            """关闭浏览器"""
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

    def __init__(self, db_path=None, tablename="cnvd_vulnerabilities"):
        # 默认使用固定路径
        if db_path is None:
            # 使用项目根目录下的data目录
            data_dir = "/home/<USER>/data"
            os.makedirs(data_dir, exist_ok=True)
            self.db_path = os.path.join(data_dir, "cnvd_vulnerabilities.db")
        else:
            self.db_path = db_path
            
        self.tablename = tablename
        self.cookie_fetcher = self.CookieFetcher()
        self.cookies = None
        self.user_agent = None
        
        # 初始化数据库
        self.init_database()
        print(f"数据库路径: {self.db_path}")

    def connect_database(self):
        """连接到SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        return conn, cursor

    def close_database(self, conn, cursor):
        """关闭数据库连接"""
        if cursor:
            cursor.close()
        if conn:
            conn.close()

    def init_database(self) -> None:
        """
        初始化SQLite数据库并创建表（如果不存在）
        
        创建包含漏洞信息字段的表结构，并设置主键和默认值。
        """
        conn, cursor = self.connect_database()
        try:
            # 创建表（如果不存在）
            cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS {self.tablename} (
                cnvd_id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                cve_id TEXT DEFAULT NULL,
                level TEXT DEFAULT NULL,
                vulType TEXT DEFAULT NULL,
                vendor TEXT DEFAULT NULL,
                product TEXT DEFAULT NULL,
                disclosure_date TEXT DEFAULT NULL,
                update_date TEXT DEFAULT NULL,
                description TEXT DEFAULT NULL,
                solution TEXT DEFAULT NULL,
                reference_links TEXT DEFAULT NULL
            )
            ''')
            conn.commit()
        finally:
            self.close_database(conn, cursor)
        print("数据库初始化成功")

    def save_to_database(self, vulnerability_data):
        """将漏洞数据保存到数据库"""
        conn, cursor = self.connect_database()
        try:
            # 准备SQL语句
            sql = f'''
            INSERT OR REPLACE INTO {self.tablename} 
            (cnvd_id, title, cve_id, level, vulType, vendor, product, 
             disclosure_date, update_date, description, solution, reference_links)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            
            # 提取数据
            cnvd_id = vulnerability_data.get('CNVD-ID', '')
            title = vulnerability_data.get('title', '')
            cve_id = vulnerability_data.get('CVE ID', '')
            level = vulnerability_data.get('危害级别', '')
            vulType = vulnerability_data.get('漏洞类型', '')
            vendor = ''  # 厂商信息需要从影响产品中提取
            product = vulnerability_data.get('影响产品', '')
            disclosure_date = vulnerability_data.get('公开日期', '')
            update_date = vulnerability_data.get('更新时间', '')
            description = vulnerability_data.get('漏洞描述', '')
            solution = vulnerability_data.get('漏洞解决方案', '')
            reference_links = vulnerability_data.get('参考链接', '')
            
            # 执行SQL
            cursor.execute(sql, (
                cnvd_id, title, cve_id, level, vulType, vendor, product,
                disclosure_date, update_date, description, solution, reference_links
            ))
            
            conn.commit()
            return True
        except Exception as e:
            print(f"保存数据到数据库时出错: {e}")
            return False
        finally:
            self.close_database(conn, cursor)

    def _clean_text(self, xpath_result):
        """清理字符串中的换行符、回车符、制表符和空格"""
        return "".join(xpath_result).replace('\r', '').replace('\n', '').replace('\t', '').replace(' ', '')

    async def get_page(self, page_num=1, max_retries=3):
        """获取指定页面的漏洞列表"""
        retries = 0
        while retries < max_retries:
            if not self.cookies:
                self.cookies = await self.cookie_fetcher.new_cookie()
                self.user_agent = self.cookie_fetcher.user_agent
            
            # 更新请求头中的User-Agent
            headers = {
                'Host': 'www.cnvd.org.cn',
                'Connection': 'keep-alive',
                'Cache-Control': 'max-age=0',
                'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Referer': 'https://www.cnvd.org.cn/flaw/list',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6'
            }
            
            offset = page_num * 10 - 10
            params = (('flag', 'true'),)
            data = {
                'number': '\u8BF7\u8F93\u5165\u7CBE\u786E\u7F16\u53F7',
                'startDate': '',
                'endDate': '',
                'flag': 'true',
                'field': '',
                'order': '',
                'numPerPage': '10',
                'offset': str(offset),
                'max': '10'
            }
            
            try:
                response = requests.post(
                    'https://www.cnvd.org.cn/flaw/list',
                    headers=headers,
                    cookies=self.cookies,
                    params=params,
                    data=data,
                    verify=False,  # 禁用SSL验证
                    timeout=30     # 添加超时设置
                )
                
                print(f'列表页 状态码：{response.status_code}，页码: {page_num}')
                
                if response.status_code == 200:
                    # 检查返回内容是否为空
                    if not response.content or len(response.content) < 100:
                        print("返回内容为空，可能是cookie访问次数限制，尝试重新获取cookie...")
                        self.cookies = await self.cookie_fetcher.new_cookie()
                        self.user_agent = self.cookie_fetcher.user_agent
                        retries += 1
                        continue
                    
                    html = response.content
                    element = etree.HTML(html)
                    url_lis = element.xpath("//table[@class='tlist']//tbody//tr//td[1]//a//@href")
                    title_lis = element.xpath("//table[@class='tlist']//tbody//tr//td[1]//a//@title")
                    degree_lis = [t.strip() for t in element.xpath("//table[@class='tlist']//tbody//tr//td[2]/text()") if t.strip()]
                    click_lis = [t.strip() for t in element.xpath("//table[@class='tlist']//tbody//tr//td[3]/text()") if t.strip()]
                    date_lis = [t.strip() for t in element.xpath("//table[@class='tlist']//tbody//tr//td[6]/text()") if t.strip()]
                    
                    # 检查是否成功获取到数据
                    if not url_lis or not title_lis:
                        print("未能获取到列表数据，可能是页面结构变化或反爬机制触发")
                        retries += 1
                        continue
                    
                    Bug_inform = {
                        'url': '',
                        'title': '',
                        'degree': '',
                        'click': '',
                        'date': '',
                        'inform': {}
                    }
                    
                    Bug_lis = []
                    for i in range(min(10, len(url_lis))):
                        bug_dic = copy.deepcopy(Bug_inform)
                        bug_dic['url'] = url_lis[i]
                        bug_dic['title'] = title_lis[i]
                        bug_dic['degree'] = degree_lis[i] if i < len(degree_lis) else ''
                        bug_dic['click'] = click_lis[i] if i < len(click_lis) else ''
                        bug_dic['date'] = date_lis[i] if i < len(date_lis) else ''
                        Bug_lis.append(bug_dic)
                    return Bug_lis
                    
                elif response.status_code == 521:
                    print("\ncookie 失效！正在更新cookie...\n")
                    self.cookies = await self.cookie_fetcher.new_cookie()
                    self.user_agent = self.cookie_fetcher.user_agent
                    await asyncio.sleep(3)
                    print('更新完成，重新请求')
                    retries += 1
                else:
                    print(f"请求失败，状态码: {response.status_code}")
                    retries += 1
                    await asyncio.sleep(3)
            except requests.exceptions.SSLError as e:
                print(f"SSL错误: {e}")
                print("尝试绕过SSL验证...")
                retries += 1
                await asyncio.sleep(3)
            except requests.exceptions.Timeout as e:
                print(f"请求超时: {e}")
                retries += 1
                await asyncio.sleep(3)
            except requests.exceptions.ConnectionError as e:
                print(f"连接错误: {e}")
                # 可能是防火墙拦截或IP被临时封禁
                print("可能被防火墙拦截，等待时间更长后重试...")
                await asyncio.sleep(10)  # 等待更长时间
                retries += 1
            except Exception as e:
                print(f"请求出错: {e}")
                retries += 1
                await asyncio.sleep(3)
        
        print(f"获取第{page_num}页数据失败，已达到最大重试次数")
        return False

    async def get_inform(self, url, max_retries=3):
        """获取漏洞详情页信息"""
        retries = 0
        while retries < max_retries:
            if not self.cookies:
                self.cookies = await self.cookie_fetcher.new_cookie()
                self.user_agent = self.cookie_fetcher.user_agent
            
            # 更新请求头中的User-Agent
            headers = {
                'Host': 'www.cnvd.org.cn',
                'Connection': 'keep-alive',
                'Cache-Control': 'max-age=0',
                'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': self.user_agent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Referer': 'https://www.cnvd.org.cn/flaw/list',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6'
            }
            
            # 确保URL格式正确
            if url.startswith('http'):
                full_url = url  # URL已经是完整的
            else:
                # 确保URL不重复添加域名前缀
                full_url = 'https://www.cnvd.org.cn' + url
            
            try:
                # 设置SSL验证选项
                # 这里我们设置verify=False来暂时绕过SSL验证问题
                # 在生产环境中应谨慎使用，这里仅用于解决当前错误
                response = requests.get(
                    full_url, 
                    headers=headers, 
                    cookies=self.cookies,
                    verify=False,  # 禁用SSL验证
                    timeout=30     # 添加超时设置
                )
                print(f'详情页 状态码：{response.status_code}，URL: {full_url}')
                
                if response.status_code == 200:
                    # 检查返回内容是否为空
                    if not response.content or len(response.content) < 100:
                        print("返回内容为空，可能是cookie访问次数限制，尝试重新获取cookie...")
                        self.cookies = await self.cookie_fetcher.new_cookie()
                        self.user_agent = self.cookie_fetcher.user_agent
                        retries += 1
                        continue
                    
                    html = response.content
                    tree = etree.HTML(html)

                    field_to_xpath = {
                        "CNVD-ID": '//tr[td[text()="CNVD-ID"]]/td[2]//text()',
                        "公开日期": '//tr[td[text()="公开日期"]]/td[2]//text()',
                        "危害级别": '//tr[td[text()="危害级别"]]/td[2]//text()',
                        "影响产品": '//tr[td[text()="影响产品"]]/td[2]//text()',
                        "CVE ID": '//tr[td[text()="CVE ID"]]/td[2]//text()',
                        "漏洞描述": '//tr[td[text()="漏洞描述"]]/td[2]//text()',
                        "漏洞类型": '//tr[td[text()="漏洞类型"]]/td[2]//text()',
                        "参考链接": '//tr[td[text()="参考链接"]]/td[2]//text()',
                        "漏洞解决方案": '//tr[td[text()="漏洞解决方案"]]/td[2]//text()',
                        "厂商补丁": '//tr[td[text()="厂商补丁"]]/td[2]//text()',
                        "验证信息": '//tr[td[text()="验证信息"]]/td[2]//text()',
                        "报送时间": '//tr[td[text()="报送时间"]]/td[2]//text()',
                        "收录时间": '//tr[td[text()="收录时间"]]/td[2]//text()',
                        "更新时间": '//tr[td[text()="更新时间"]]/td[2]//text()',
                        "漏洞附件": '//tr[td[text()="漏洞附件"]]/td[2]//text()',
                    }

                    data = {field: self._clean_text(tree.xpath(xpath)) for field, xpath in field_to_xpath.items()}
                    
                    # 检查是否成功获取到数据
                    if not data.get("CNVD-ID"):
                        print("未能获取到详情数据，可能是页面结构变化或反爬机制触发")
                        retries += 1
                        continue
                    
                    return data
                    
                elif response.status_code == 521:
                    print("\ncookie 失效！正在更新cookie...\n")
                    self.cookies = await self.cookie_fetcher.new_cookie()
                    self.user_agent = self.cookie_fetcher.user_agent
                    await asyncio.sleep(3)
                    print('更新完成，重新请求')
                    retries += 1
                else:
                    print(f"请求失败，状态码: {response.status_code}")
                    retries += 1
                    await asyncio.sleep(3)
            except requests.exceptions.SSLError as e:
                print(f"SSL错误: {e}")
                print("尝试绕过SSL验证...")
                retries += 1
                await asyncio.sleep(3)
            except requests.exceptions.Timeout as e:
                print(f"请求超时: {e}")
                retries += 1
                await asyncio.sleep(3)
            except requests.exceptions.ConnectionError as e:
                print(f"连接错误: {e}")
                # 可能是防火墙拦截或IP被临时封禁
                print("可能被防火墙拦截，等待时间更长后重试...")
                await asyncio.sleep(10)  # 等待更长时间
                retries += 1
            except Exception as e:
                print(f"请求出错: {e}")
                retries += 1
                await asyncio.sleep(3)
        
        print(f"获取详情页数据失败，已达到最大重试次数")
        return False

    async def crawl_pages(self, first_page=1, last_page=1, sleep_time=4):
        """
        批量爬取指定页面范围的漏洞信息并保存到数据库
        
        Args:
            first_page: 起始页码
            last_page: 结束页码
            sleep_time: 每页之间的等待时间（秒）
            
        Returns:
            dict: 包含爬取统计信息的字典
        """
        # 初始化浏览器
        await self.cookie_fetcher.init_browser()
        self.cookies = await self.cookie_fetcher.new_cookie()
        self.user_agent = self.cookie_fetcher.user_agent
        
        sum_ = (last_page - first_page + 1) * 10
        error = sum_
        success = 0
        
        for num in range(first_page, last_page+1):
            print(f"\n开始爬取第 {num} 页...")
            bug_lis = await self.get_page(num)
            if bug_lis == 'refresh':
                await asyncio.sleep(sleep_time)
                bug_lis = await self.get_page(num)
                
            if isinstance(bug_lis, list):
                for i, bug in enumerate(bug_lis):
                    print(f"  正在爬取第 {num} 页第 {i+1} 条数据: {bug['title']}")
                    inform = await self.get_inform(bug['url'])
                    if inform == 'refresh':
                        inform = await self.get_inform(bug['url'])
                        
                    if isinstance(inform, dict):
                        # 合并基本信息到详情信息中
                        inform['title'] = bug['title']
                        # 保存到数据库
                        if self.save_to_database(inform):
                            success += 1
                            error -= 1
                            print(f"    成功保存: {inform.get('CNVD-ID', 'Unknown')} - {inform.get('title', 'Unknown')}")
                        else:
                            print(f"    数据保存失败: {bug['url']}")
                    else:
                        print(f"    详情页数据获取失败: {bug['url']}")
                    
                    # 随机等待时间，避免被反爬
                    wait_time = sleep_time + random.uniform(1, 3)
                    print(f"    等待 {wait_time:.2f} 秒后继续...")
                    await asyncio.sleep(wait_time)
            else:
                print(f"第 {num} 页列表获取失败！")
                
        print("\n\n目标爬取", sum_)
        print("成功", success, "   失败", error)
        
        # 关闭浏览器
        await self.cookie_fetcher.close_browser()
        
        # 返回统计信息
        return {
            "planned_count": sum_,
            "total_count": success,
            "failed_count": error,
            "crawled_pages": last_page - first_page + 1
        }

    async def close(self):
        """关闭浏览器"""
        await self.cookie_fetcher.close_browser()


async def main():
    crawler = CNVDCrawler()
    
    try:
        # 提示用户输入
        user_input = input('请输入要抓取的起始页和终止页序号（如：5 10），按q退出：')
        
        # 检查是否退出
        if user_input.lower() == 'q':
            print("程序已退出")
            return
        
        # 解析输入
        num_lis = [int(num) for num in user_input.split(' ') if num != '']
        if len(num_lis) == 2:
            # 执行爬取
            await crawler.crawl_pages(num_lis[0], num_lis[1], 10)
            print("\n爬取完成，按任意键退出...")
            input()
        else:
            print("输入格式不正确，请输入两个数字，例如：5 10")
    except KeyboardInterrupt:
        print("\n程序已终止")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 确保关闭浏览器
        await crawler.close()


# 添加供main.py调用的运行函数
async def run_crawler(start_page=1, end_page=3, sleep_time=4, db_path=None):
    """
    运行CNVD爬虫的函数，用于从main.py调用
    
    Args:
        start_page: 起始页码，默认为1
        end_page: 结束页码，默认为3
        sleep_time: 每页之间的等待时间（秒），默认为4
        db_path: 数据库路径，默认为None（使用默认路径）
        
    Returns:
        dict: 包含爬取统计信息的字典
    """
    crawler = CNVDCrawler(db_path=db_path)
    
    try:
        print(f"开始爬取CNVD数据，页面范围: {start_page} - {end_page}")
        result = await crawler.crawl_pages(start_page, end_page, sleep_time)
        return {
            "success": True,
            "crawled_pages": result.get("crawled_pages", 0),
            "vulnerabilities": result.get("total_count", 0),
            "message": "CNVD数据爬取完成"
        }
    except Exception as e:
        print(f"CNVD爬取出错: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "CNVD数据爬取过程中发生错误"
        }
    finally:
        await crawler.close()


# 提供同步接口，便于main.py调用
def run_cnvd_crawler(start_page=1, end_page=3, sleep_time=4, db_path=None):
    """
    运行CNVD爬虫的同步函数入口
    
    此函数会被main.py调用，处理异步转同步的问题
    
    Args:
        start_page: 起始页码，默认为1
        end_page: 结束页码，默认为3
        sleep_time: 每页之间的等待时间（秒），默认为4
        db_path: 数据库路径，默认为None（使用默认路径）
        
    Returns:
        dict: 包含爬取统计信息的字典
    """
    try:
        return asyncio.run(run_crawler(
            start_page=start_page,
            end_page=end_page,
            sleep_time=sleep_time,
            db_path=db_path
        ))
    except KeyboardInterrupt:
        return {
            "success": False,
            "message": "用户中断了CNVD爬取操作"
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "运行CNVD爬虫时出现异常"
        }


if __name__ == '__main__':
    asyncio.run(main()) 