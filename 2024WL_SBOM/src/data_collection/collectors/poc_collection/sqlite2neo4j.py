import sqlite3
import json
import os
from neo4j import GraphDatabase
from pathlib import Path

# 获取项目根目录
PROJECT_ROOT = Path(__file__).resolve().parents[4]
CONFIG_DIR = PROJECT_ROOT / 'src' / 'data_collection' / 'config'
NEO4J_CONFIG_PATH = CONFIG_DIR / 'neo4j_config.json'

# ConfigHandler类，用于管理Neo4j连接
class ConfigHandler:
    def __init__(self, config_path=None):
        """初始化Neo4j连接管理器
        
        Args:
            config_path: 配置文件路径(可选)，默认使用NEO4J_CONFIG_PATH
        """
        self.config_path = config_path or NEO4J_CONFIG_PATH
        self.config = self.load_config()
        self.driver = self.create_driver()

    def load_config(self):
        """加载Neo4j配置"""
        try:
            with open(self.config_path, "r") as f:
                config = json.load(f)
                print(f"已从 {self.config_path} 加载Neo4j配置")
                return config
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"配置文件加载错误: {e}，将使用默认配置")
            
            # 使用默认配置
            return {
                "neo4j": {
                    "host": "bolt://localhost:7687",
                    "username": "neo4j",
                    "password": "password"
                }
            }

    def create_driver(self):
        """创建Neo4j驱动连接"""
        try:
            neo4j_config = self.config.get("neo4j", {})
            host = neo4j_config.get("host", "bolt://localhost:7687")
            username = neo4j_config.get("username", "neo4j")
            password = neo4j_config.get("password", "password")
            
            print(f"正在连接Neo4j数据库: {host}")
            return GraphDatabase.driver(host, auth=(username, password))
        except Exception as e:
            print(f"创建Neo4j驱动时出错: {e}")
            print("请确保Neo4j服务正在运行，并检查配置文件中的连接信息")
            raise


# ATTENTION ! DELETE complexity and exploit_difficulty from the class
class ExploitDetail:
    def __init__(self, poc_id, exploit_db_id, title, poc,
                 publication_date, author, platform, cve, t, v):
        self.poc_id = poc_id
        self.exploit_db_id = exploit_db_id
        self.title = title
        self.poc = poc
        # self.complexity = complexity
        # self.exploit_difficulty = exploit_difficulty
        self.publication_date = publication_date
        self.author = author
        self.platform = platform
        self.cve = cve
        self.t = t  # type
        self.v = v  # verified? 1 or 0


def create_poc_in_neo4j(driver, exploit_detail):
    """
    创建POC节点，并确保与CVE节点建立关系
    注意：此函数已被更新，新的逻辑直接在main函数中实现
    保留此函数仅为了兼容性
    """
    with driver.session() as session:
        query = """
        MERGE (v:Vulnerability {cve_id: $cve_id})
        ON CREATE SET v.title = $title
        WITH v
        MERGE (p:POC {poc_id: $poc_id, source: 'exploitdb'})
        SET p.code_snippet = $poc_code,
            p.exploit_db_id = $exploit_db_id,
            p.publication_date = $date,
            p.author = $author,
            p.platform = $platform,
            p.title = $title,
            p.type = $type,
            p.v = $v
        MERGE (v)-[:HAS_POC]->(p)
        RETURN p.poc_id
        """
        result = session.run(
            query,
            cve_id=exploit_detail.cve,
            title=exploit_detail.title,
            poc_id=exploit_detail.poc_id,
            poc_code=exploit_detail.poc,
            exploit_db_id=exploit_detail.exploit_db_id,
            date=exploit_detail.publication_date,
            author=exploit_detail.author,
            platform=exploit_detail.platform,
            type=exploit_detail.t,
            v=exploit_detail.v
        )


# 在使用之前，需要修改配置文件
def main():
    # 使用配置文件创建Handler
    handler = ConfigHandler(NEO4J_CONFIG_PATH)
    
    # SQLite connection
    db_path = os.getenv("POC_DB_PATH", "./exploitDetailed.db")
    print(f"连接SQLite数据库: {db_path}")
    sqlite_conn = sqlite3.connect(db_path)
    cursor = sqlite_conn.cursor()

    # Neo4j connection
    driver = handler.driver

    # Read data from SQLite
    cursor.execute("SELECT * FROM exploitDetail")
    rows = cursor.fetchall()
    
    # 将SQLite数据转换为字典列表，然后使用统一的函数导入
    pocs_data = []
    for row in rows:
        try:
            # 获取POC相关字段 - 修正字段映射
            # SQLite表结构: EDB_ID | Date | V | Title | Type | Platform | Author | CVE | POC
            poc_data = {
                'exploit_db_id': row[0],        # EDB_ID
                'publication_date': row[1],     # Date
                'v': row[2],                    # V
                'title': row[3],                # Title
                'type': row[4],                 # Type
                'platform': row[5],             # Platform
                'author': row[6],               # Author
                'cve_id': row[7],               # CVE
                'poc_code': row[8],             # POC
                'source': 'exploitdb'
            }
            pocs_data.append(poc_data)
        except Exception as e:
            print(f"处理SQLite数据时出错: {e}")
    
    # 使用统一的函数导入POC数据
    print(f"将从SQLite读取的 {len(pocs_data)} 条POC记录导入Neo4j...")
    integrate_pocs_to_neo4j(pocs_data, driver=driver)
    
    # 关闭连接
    cursor.close()
    sqlite_conn.close()
    driver.close()


def integrate_pocs_to_neo4j(pocs_data, neo4j_config_path=None, driver=None):
    """直接将爬取或LLM生成的POC数据集成到Neo4j
    
    Args:
        pocs_data: 包含POC信息的列表，每个POC应为字典格式，包含必要字段：
                  - 对于爬取的POC: exploit_db_id/edb_id, title, author, platform, type, cve_id, 
                                  verified/v, publication_date/date, poc_code/code/code_snippet
                  - 对于LLM生成的POC: cve_id, title, author, platform, type, 
                                   severity, base_score, poc_code/code/code_snippet, source='llm'
        neo4j_config_path: Neo4j配置文件路径(可选)，不提供则使用默认路径
        driver: 已存在的Neo4j驱动实例(可选)，如果提供则不会创建新的驱动
    """
    # 创建或使用已有驱动
    should_close_driver = False
    if driver is None:
        # 使用提供的路径或默认配置路径
        config_path = neo4j_config_path or NEO4J_CONFIG_PATH
        handler = ConfigHandler(config_path)
        driver = handler.driver
        should_close_driver = True
    
    success_count = 0
    processed_ids = set()  # 用于跟踪已处理的POC ID，避免重复导入
    
    for poc_dict in pocs_data:
        try:
            # 确定POC来源
            is_llm_poc = poc_dict.get('source') == 'llm'
            
            # 统一字段名称 - 处理各种可能的字段名
            cve_id = poc_dict.get('cve_id', poc_dict.get('cve', 'N/A'))
            if cve_id == 'N/A':
                cve_id = "null"
                
            # 处理ExploitDB ID - 统一字段名
            edb_id = poc_dict.get('exploit_db_id', poc_dict.get('edb_id', None))
            
            # 处理POC代码字段 - 统一字段名(增加code和code_snippet作为可能的字段名)
            poc_code = poc_dict.get('poc_code', 
                        poc_dict.get('poc', 
                            poc_dict.get('code', 
                                poc_dict.get('code_snippet', ''))))
            
            # 对所有POC的必要字段进行检查
            if poc_code == '':
                print(f"警告: 跳过缺少POC代码的记录")
                continue
                
            # 如果exploit_db_id字段缺失或为None，则跳过非LLM POC
            if not is_llm_poc and (edb_id is None or edb_id == ''):
                print(f"警告: 跳过缺少exploit_db_id的ExploitDB POC记录")
                continue
                
            # 检查LLM POC必须有CVE ID
            if is_llm_poc and (cve_id == "null" or cve_id == ''):
                print(f"警告: 跳过缺少CVE ID的LLM生成POC记录")
                continue
                
            # 处理日期字段 - 统一字段名
            date = poc_dict.get('publication_date', poc_dict.get('date', ''))
            
            # 处理title字段
            title = poc_dict.get('title', '')
            
            # 处理verified/v字段 - 统一字段名
            verified = poc_dict.get('verified', poc_dict.get('v', 0))
            
            # 处理平台和类型
            platform = poc_dict.get('platform', '')
            poc_type = poc_dict.get('type', '')
            
            # 处理作者
            author = poc_dict.get('author', '')
            
            # 生成唯一标识
            if is_llm_poc:
                poc_id = f"POC-{cve_id}-LLM-{hash(poc_code)%10000}"
            else:
                poc_id = f"POC-{cve_id}-{edb_id}"
                
            # 检查是否已处理过该POC，避免重复导入
            if poc_id in processed_ids:
                print(f"跳过重复的POC: {poc_id}")
                continue
                
            processed_ids.add(poc_id)
            
            with driver.session() as session:
                if is_llm_poc:
                    # 对于LLM生成的POC
                    query = """
                    MERGE (v:Vulnerability {cve_id: $cve_id})
                    ON CREATE SET v.title = $title, 
                                  v.description = $description,
                                  v.severity = $severity, 
                                  v.score = $score
                    WITH v
                    MERGE (p:POC {poc_id: $poc_id, source: 'llm'})
                    SET p.code_snippet = $poc_code,
                        p.author = $author,
                        p.publication_date = $date,
                        p.title = $title,
                        p.type = $type,
                        p.platform = $platform,
                        p.v = 0
                    MERGE (v)-[:HAS_POC]->(p)
                    RETURN p.poc_id
                    """
                    
                    result = session.run(
                        query,
                        cve_id=cve_id,
                        title=title,
                        poc_id=poc_id,
                        description=poc_dict.get('description', ''),
                        severity=poc_dict.get('severity', 'UNKNOWN'),
                        score=poc_dict.get('base_score', 0.0),
                        poc_code=poc_code,
                        author=author,
                        date=date,
                        type=poc_type,
                        platform=platform
                    )
                else:
                    # 对于ExploitDB的POC
                    query = """
                    MERGE (v:Vulnerability {cve_id: $cve_id})
                    ON CREATE SET v.title = $title
                    WITH v
                    MERGE (p:POC {poc_id: $poc_id, source: 'exploitdb'})
                    SET p.code_snippet = $poc_code,
                        p.exploit_db_id = $exploit_db_id,
                        p.publication_date = $date,
                        p.author = $author,
                        p.platform = $platform,
                        p.title = $title,
                        p.type = $type,
                        p.v = $v
                    MERGE (v)-[:HAS_POC]->(p)
                    RETURN p.poc_id
                    """
                    
                    result = session.run(
                        query,
                        cve_id=cve_id,
                        title=title,
                        poc_id=poc_id,
                        poc_code=poc_code,
                        exploit_db_id=edb_id,
                        date=date,
                        author=author,
                        platform=platform,
                        type=poc_type,
                        v=verified
                    )
                
                success_count += 1
                
        except Exception as e:
            print(f"处理POC失败: {e}")
    
    if should_close_driver:
        driver.close()
    print(f"成功将{success_count}/{len(pocs_data)}个POC集成到Neo4j中。")


def save_pocs_to_sqlite(pocs_data, db_path='./exploitDetailed.db'):
    """将爬取或LLM生成的POC数据保存到SQLite数据库
    
    Args:
        pocs_data: 包含POC信息的列表，每个POC应为字典格式
        db_path: SQLite数据库路径
    """
    print(f"此函数已被弃用。请使用collect_poc.py中的函数：")
    print(f"1. 对于ExploitDB的POC: save_poc_to_sqlite(poc_data, db_path)")
    print(f"2. 对于LLM生成的POC: save_llm_poc_to_sqlite(poc_data, db_path)")


def example_usage():
    """示例：如何使用本模块集成POC数据"""
    # 示例POC数据，可以来自爬虫或LLM生成
    example_exploitdb_pocs = [
        {
            'edb_id': 12345,
            'title': 'Example POC Title',
            'author': 'Security Researcher',
            'platform': 'Linux',
            'type': 'Remote',
            'cve_id': 'CVE-2023-1234',
            'verified': 1,
            'date': '2023-01-01',
            'poc_code': '#!/bin/bash\necho "This is an example POC"\n',
            'source': 'exploitdb'
        }
    ]
    
    example_llm_pocs = [
        {
            'cve_id': 'CVE-2023-5678',
            'title': 'LLM Generated POC Example',
            'author': 'LLM Generator',
            'platform': 'Windows',
            'type': 'Local',
            'severity': 'HIGH',
            'base_score': 8.5,
            'poc_code': '# Python\nimport os\nprint("POC executed")\n',
            'date': '2023-02-15',
            'source': 'llm',
            'description': 'This is a vulnerability description'
        }
    ]
    
    print("示例1：集成ExploitDB的POC到Neo4j")
    integrate_pocs_to_neo4j(example_exploitdb_pocs)
    
    print("\n示例2：集成LLM生成的POC到Neo4j")
    integrate_pocs_to_neo4j(example_llm_pocs)
    
    print("\n注意：保存到SQLite请使用collect_poc.py中的函数")
    print("1. 对于ExploitDB的POC:")
    print("   from src.data_collection.scripts.collect_poc import save_poc_to_sqlite")
    print("   save_poc_to_sqlite(poc_data, db_path)")
    print("2. 对于LLM生成的POC:")
    print("   from src.data_collection.scripts.collect_poc import save_llm_poc_to_sqlite")
    print("   save_llm_poc_to_sqlite(poc_data, db_path)")


if __name__ == '__main__':
    main()
    # 取消下面的注释来运行示例
    # example_usage()


def integrate_with_collect_poc(db_path=None, neo4j_config_path=None):
    """与collect_poc.py集成的函数
    
    此函数用于在collect_poc.py的流程结束后，将所有SQLite中的POC导入到Neo4j。
    执行步骤：
    1. 从exploitDetail表中读取所有POC记录
    2. 从llm_poc表中读取所有POC记录
    3. 将这些记录转换为标准格式
    4. 使用integrate_pocs_to_neo4j函数将它们导入Neo4j
    
    Args:
        db_path: SQLite数据库路径，如果为None则使用环境变量或默认路径
        neo4j_config_path: Neo4j配置文件路径，如果为None则使用默认路径
    
    用法：
    在collect_poc.py处理完所有CVE后，调用此函数即可。
    
    示例：
    ```python
    # 在collect_poc.py的main函数末尾添加
    from src.data_collection.collectors.poc_collection.sqlite2neo4j import integrate_with_collect_poc
    
    def main():
        # 原有处理逻辑...
        # ...
        
        # 所有CVE处理完成后，将SQLite中的POC导入Neo4j
        if args.save_neo4j:
            logger.info("开始将SQLite中的POC导入Neo4j")
            integrate_with_collect_poc()
    ```
    """
    import sqlite3
    import os
    from datetime import datetime
    import traceback
    
    # 使用提供的路径或环境变量获取数据库路径
    if db_path is None:
        db_path = os.getenv("POC_DB_PATH", "/home/<USER>/data/poc/exploitDetailed.db")

    print(f"正在从SQLite数据库 {db_path} 导入POC到Neo4j...")
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件不存在: {db_path}")
        return
    
    try:
        # 连接SQLite数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='exploitDetail'")
        if not cursor.fetchone():
            print(f"警告: exploitDetail表不存在，将跳过处理ExploitDB数据")
            exploit_db_pocs = []
        else:
            # 读取exploitDetail表中的所有POC
            print("正在读取exploitDetail表...")
            cursor.execute("SELECT * FROM exploitDetail")
            exploit_db_rows = cursor.fetchall()
            
            # 将exploitDetail表中的记录转换为标准格式
            exploit_db_pocs = []
            for row in exploit_db_rows:
                try:
                    row_dict = dict(row)
                    # 确保字段名与主函数一致，使用exploit_db_id而不是edb_id
                    poc_data = {
                        'exploit_db_id': row_dict['EDB_ID'],
                        'publication_date': row_dict['Date'],
                        'title': row_dict['Title'],
                        'type': row_dict['Type'],
                        'platform': row_dict['Platform'],
                        'author': row_dict['Author'],
                        'cve_id': row_dict['CVE'],
                        'poc_code': row_dict['POC'],
                        'v': row_dict.get('V', 0),
                        'source': 'exploitdb'
                    }
                    exploit_db_pocs.append(poc_data)
                except Exception as e:
                    print(f"处理exploitDetail表行时出错: {e}")
                    print(f"问题行: {row}")
            
            print(f"从exploitDetail表中读取了 {len(exploit_db_pocs)} 条记录")
        
        # 检查llm_poc表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='llm_poc'")
        if not cursor.fetchone():
            print(f"警告: llm_poc表不存在，将跳过处理LLM数据")
            llm_pocs = []
        else:
            # 读取llm_poc表中的所有POC
            print("正在读取llm_poc表...")
            try:
                cursor.execute("SELECT * FROM llm_poc")
                llm_rows = cursor.fetchall()
                
                # 将llm_poc表中的记录转换为标准格式
                llm_pocs = []
                for row in llm_rows:
                    try:
                        row_dict = dict(row)
                        poc_data = {
                            'cve_id': row_dict['CVE_ID'],
                            'date': row_dict['Date'] or datetime.now().strftime("%Y-%m-%d"),
                            'title': row_dict['Title'],
                            'type': row_dict['Type'],
                            'platform': row_dict['Platform'],
                            'author': row_dict['Author'],
                            'poc_code': row_dict['POC'],
                            'severity': row_dict['Severity'],
                            'base_score': row_dict['Score'],
                            'source': 'llm'
                        }
                        llm_pocs.append(poc_data)
                    except Exception as e:
                        print(f"处理llm_poc表行时出错: {e}")
                        print(f"问题行: {row}")
                
                print(f"从llm_poc表中读取了 {len(llm_pocs)} 条记录")
            except sqlite3.Error as e:
                print(f"读取llm_poc表时出错: {e}")
                print(f"可能是表结构不匹配，请检查表结构")
                llm_pocs = []
        
        # 关闭数据库连接
        conn.close()
        
        # 合并所有POC
        all_pocs = exploit_db_pocs + llm_pocs
        
        if not all_pocs:
            print("没有找到任何POC记录")
            return
        
        # 导入到Neo4j
        print(f"开始将 {len(all_pocs)} 条POC记录导入Neo4j...")
        integrate_pocs_to_neo4j(all_pocs, neo4j_config_path)
        print("导入完成")
    except Exception as e:
        print(f"处理过程中出错: {e}")
        traceback.print_exc()


# 可以创建一个集成点，供collect_poc.py调用
def batch_import_to_neo4j(db_path=None, neo4j_config_path=None):
    """从SQLite批量导入POC到Neo4j的集成点
    
    Args:
        db_path: SQLite数据库路径，如果为None则使用环境变量或默认路径
        neo4j_config_path: Neo4j配置文件路径，如果为None则使用默认路径
        
    Returns:
        bool: 是否成功导入
    """
    import os
    import sys
    import traceback
    
    # 输出系统环境信息，帮助调试
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查配置文件
    config_path = neo4j_config_path or NEO4J_CONFIG_PATH
    print(f"Neo4j配置文件路径: {config_path}")
    if os.path.exists(config_path):
        print(f"Neo4j配置文件存在")
    else:
        print(f"警告: Neo4j配置文件不存在，将使用默认配置")
    
    if db_path:
        os.environ["POC_DB_PATH"] = db_path
        print(f"设置数据库路径: {db_path}")
    
    # 确保db_path存在
    db_path = os.getenv("POC_DB_PATH", "/home/<USER>/data/poc/exploitDetailed.db")
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件不存在: {db_path}")
        print(f"请检查路径并确保文件存在。")
        return False
    
    try:
        print(f"开始批量导入: {db_path}")
        integrate_with_collect_poc(db_path, config_path)
        return True
    except ModuleNotFoundError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所有必要的模块: pip install neo4j")
        return False
    except Exception as e:
        print(f"批量导入过程中出错: {e}")
        traceback.print_exc()
        return False
