import random
import sqlite3
import time
import os

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait


def find_latest_in_db():
    try:
        conn = sqlite3.connect('exploitDetailed.db')
        cursor = conn.cursor()
        cursor.execute('SELECT EDB_ID FROM exploitDetail ORDER BY EDB_ID DESC LIMIT 1')
        result = cursor.fetchone()
        if result:
            print(f"Latest EDB_ID in DB: {result[0]}")
        else:
            print("No data found!")
        cursor.close()
        conn.close()
        return result[0] if result else "N/A"
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return "N/A"


def crawl_exploit_range(start_id, end_id, chromedriver_path=None, db_path='./exploitDetailed.db', proxy=None):
    """
    爬取指定范围的exploit-id的poc并保存到数据库
    
    参数:
    start_id: 起始的EDB-ID
    end_id: 结束的EDB-ID
    chromedriver_path: ChromeDriver的路径，如果为None则自动查找
    db_path: 数据库文件路径
    proxy: 代理地址，格式如 "http://host:port" 或 "socks5://host:port"
    
    返回:
    成功爬取的EDB-ID数量
    """
    # 设置 Chrome 选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")  # 在Docker中运行必要的选项
    chrome_options.add_argument("--disable-dev-shm-usage")  # 解决内存不足问题
    chrome_options.add_argument("--disable-gpu")  # 禁用GPU加速
    
    # 如果提供了代理，设置代理
    if proxy:
        chrome_options.add_argument(f'--proxy-server={proxy}')
        print(f"Using proxy: {proxy}")
    
    # 设置 WebDriver
    if chromedriver_path:
        service = Service(chromedriver_path)
    else:
        service = Service()  # 自动查找ChromeDriver
    
    # 创建WebDriver
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("WebDriver initialized in headless mode")

    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "en,zh-CN;q=0.9,zh;q=0.8",
        "cache-control": "max-age=0",
        "priority": "u=0, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "none",
        "sec-fetch-user": "?1",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36"
    }

    # 初始化 SQLite 数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 创建表格
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS tmp (
            EDB_ID int PRIMARY KEY,
            Date TEXT,
            V INT,
            Title TEXT,
            Type TEXT,
            Platform TEXT,
            Author TEXT,
            CVE TEXT,
            POC TEXT
        )
    ''')
    conn.commit()

    # 先访问主页获取cookies
    url = "https://www.exploit-db.com/"
    print(f"Accessing {url} to get cookies...")
    driver.get(url)
    print("Page loaded successfully")

    session = requests.Session()
    cookies = driver.get_cookies()
    for cookie in cookies:
        session.cookies.set(cookie['name'], cookie['value'])

    # 设置用于后续请求的 Headers
    headers.update({
        "cookie": "; ".join([f"{k}={v}" for k, v in session.cookies.get_dict().items()])
    })

    driver.quit()  # 获取cookies后就可以关闭浏览器，后续使用requests
    print("WebDriver closed, continuing with requests")

    # 设置请求的代理
    request_proxies = None
    if proxy:
        request_proxies = {
            "http": proxy,
            "https": proxy
        }

    success_count = 0
    edb_id = int(start_id)

    while edb_id <= int(end_id):
        try:
            print(f"Processing EDB-ID: {edb_id}")
            url = f"https://www.exploit-db.com/exploits/{edb_id}"
            raw_url = f"https://www.exploit-db.com/raw/{edb_id}"

            raw_response = session.get(raw_url, headers=headers, proxies=request_proxies)
            response = session.get(url, headers=headers, proxies=request_proxies)

            # 检查是否已经存在于数据库中
            cursor.execute("SELECT edb_id FROM tmp WHERE edb_id = ?", (str(edb_id),))
            result = cursor.fetchone()
            if result:
                print(f"EDB-ID {edb_id} already exists in the database")
                edb_id += 1
                continue

            # 如果页面存在
            if response.status_code == 200 and raw_response.status_code == 200:
                # 爬取exploits页面部分
                soup = BeautifulSoup(response.text, 'lxml')
                # 抓取EDB-ID, CVE, Author, Type, Platform, Date, EDB Verified信息
                data = {}
                try:
                    h1_element = soup.find('h1')
                    title = h1_element.text.strip() if h1_element else 'N/A'
                    data['Title'] = title

                    # 提取 EDB-ID
                    edb_id_element = soup.find('h4', string='''
                                                                EDB-ID:
                                                            ''')
                    next_h6 = edb_id_element.find_next('h6') if edb_id_element else None
                    data['EDB-ID'] = next_h6.text.strip() if next_h6 else str(edb_id)

                    # 提取 CVE
                    cve_element = soup.find('h4', string='''
                                                                CVE:
                                                            ''')
                    next_a = cve_element.find_next('a') if cve_element else None
                    data['CVE'] = next_a.text.strip() if next_a else 'N/A'

                    # 提取 Author
                    author_element = soup.find('h4', string='''
                                                                Author:
                                                            ''')
                    next_author = author_element.find_next('a') if author_element else None
                    data['Author'] = next_author.text.strip() if next_author else 'N/A'
                    if data['CVE'] == data['Author']: data['CVE'] = 'N/A'

                    # 提取 Type
                    type_element = soup.find('h4', string='''
                                                                Type:
                                                            ''')
                    next_type = type_element.find_next('a') if type_element else None
                    data['Type'] = next_type.text.strip() if next_type else 'N/A'

                    # 提取 Platform
                    platform_element = soup.find('h4', string='''
                                                                Platform:
                                                            ''')
                    next_platform = platform_element.find_next('a') if platform_element else None
                    data['Platform'] = next_platform.text.strip() if next_platform else 'N/A'

                    # 提取 Date
                    date_element = soup.find('h4', string='''
                                                                Date:
                                                            ''')
                    next_date = date_element.find_next('h6') if date_element else None
                    data['Date'] = next_date.text.strip() if next_date else 'N/A'

                    # 提取 EDB Verified
                    data['V'] = 1 if soup.find('i', {'class': 'mdi-check'}) else 0
                except Exception as e:
                    print("提取数据时发生错误：", e)
                print(data)
                # 爬取raw部分
                page_content = raw_response.text
                lines = page_content.splitlines()[:20]
                tmp1 = [line for line in lines if line.startswith('#') and 'http' in line]
                tmp2 = [line for line in page_content.splitlines() if not line.startswith("#")]
                body_lines = tmp1 + tmp2
                body_content = "\n".join(body_lines).strip()

                # 存入数据库
                cursor.execute('''
                    INSERT OR IGNORE INTO tmp (EDB_ID, Date, V, Title, Type, Platform, Author, CVE, POC)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['EDB-ID'], data['Date'], data['V'], data['Title'], data['Type'], data['Platform'],
                    data['Author'],
                    data['CVE'], body_content))
                conn.commit()
                print(f"EDB-ID {edb_id} data saved.")
                success_count += 1
            else:
                print(
                    f"EDB-ID {edb_id} not found or error. Response code: {response.status_code}, Raw response code: {raw_response.status_code}")

            edb_id += 1
            # 随机延时避免被封
            sleep_time = random.randint(5, 10)
            print(f"Waiting for {sleep_time} seconds before next request...")
            time.sleep(sleep_time)
        except Exception as e:
            print(f"Error processing EDB-ID {edb_id}: {e}")
            edb_id += 1
            sleep_time = random.randint(15, 20)
            time.sleep(sleep_time)

    conn.close()
    print(f"Data extraction completed. Successfully crawled {success_count} exploits.")
    return success_count


def initial_exploitcrawler(latest_edb_id_in_db, chromedriver_path=None, db_path='./exploitDetailed.db', proxy=None):
    # 设置 Chrome 选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")  # 在Docker中运行必要的选项
    chrome_options.add_argument("--disable-dev-shm-usage")  # 解决内存不足问题
    chrome_options.add_argument("--disable-gpu")  # 禁用GPU加速
    
    # 如果提供了代理，设置代理
    if proxy:
        chrome_options.add_argument(f'--proxy-server={proxy}')
        print(f"Using proxy: {proxy}")
    
    # 设置 WebDriver
    if chromedriver_path:
        service = Service(chromedriver_path)
    else:
        service = Service()  # 自动查找ChromeDriver
    
    # 创建WebDriver
    driver = webdriver.Chrome(service=service, options=chrome_options)
    print("WebDriver initialized in headless mode")

    headers = {
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "en,zh-CN;q=0.9,zh;q=0.8",
        "cache-control": "max-age=0",
        "priority": "u=0, i",
        "sec-ch-ua": "\"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "none",
        "sec-fetch-user": "?1",
        "upgrade-insecure-requests": "1",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36"
    }

    # 初始化 SQLite 数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 创建表格
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS exploitDetail (
            EDB_ID int PRIMARY KEY,
            Date TEXT,
            V INT,
            Title TEXT,
            Type TEXT,
            Platform TEXT,
            Author TEXT,
            CVE TEXT,
            POC TEXT
        )
    ''')
    conn.commit()

    url = "https://www.exploit-db.com/"
    print(f"Accessing {url} to get the latest EDB-ID...")
    driver.get(url)

    session = requests.Session()
    for cookie in driver.get_cookies():
        session.cookies.set(cookie['name'], cookie['value'])
    
    # 设置用于后续请求的 Headers
    headers.update({
        "cookie": "; ".join([f"{k}={v}" for k, v in session.cookies.get_dict().items()])
    })

    latest_edb_id = None
    try:
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'table tbody tr td a[href*="/download/"]'))
        )

        first_link = driver.find_element(By.CSS_SELECTOR, 'table tbody tr td a[href*="/download/"]')

        href = first_link.get_attribute('href')
        if href is not None:
            latest_edb_id = href.split('/')[-1]
            print(f"Latest EDB-ID: {latest_edb_id}")
        else:
            raise Exception("Could not get href attribute from link")
    except Exception as e:
        print(f"Error occurred: {e}")
        driver.quit()
        return

    driver.quit()
    print("WebDriver closed, continuing with requests")

    # 设置请求的代理
    request_proxies = None
    if proxy:
        request_proxies = {
            "http": proxy,
            "https": proxy
        }

    edb_id = int(latest_edb_id_in_db)

    while edb_id <= int(latest_edb_id):  
        try:
            print(f"Processing EDB-ID: {edb_id}")
            url = f"https://www.exploit-db.com/exploits/{edb_id}"
            raw_url = f"https://www.exploit-db.com/raw/{edb_id}"

            raw_response = session.get(raw_url, headers=headers, proxies=request_proxies)
            response = session.get(url, headers=headers, proxies=request_proxies)

            # 检查是否已经存在于数据库中
            cursor.execute("SELECT edb_id FROM exploitDetail WHERE edb_id = ?", (str(edb_id),))
            result = cursor.fetchone()
            if result:
                print(f"EDB-ID {edb_id} already exists in the database")
                edb_id += 1
                continue

            # 如果页面存在
            if raw_response.status_code == 200 and response.status_code == 200:
                # 爬取exploits页面部分
                soup = BeautifulSoup(response.text, 'lxml')
                # 抓取EDB-ID, CVE, Author, Type, Platform, Date, EDB Verified信息
                data = {}
                try:
                    h1_element = soup.find('h1')
                    title = h1_element.text.strip() if h1_element else 'N/A'
                    data['Title'] = title

                    # 提取 EDB-ID
                    edb_id_element = soup.find('h4', string='''
                                                                EDB-ID:
                                                            ''')
                    next_h6 = edb_id_element.find_next('h6') if edb_id_element else None
                    data['EDB-ID'] = next_h6.text.strip() if next_h6 else str(edb_id)

                    # 提取 CVE
                    cve_element = soup.find('h4', string='''
                                                                CVE:
                                                            ''')
                    next_a = cve_element.find_next('a') if cve_element else None
                    data['CVE'] = next_a.text.strip() if next_a else 'N/A'

                    # 提取 Author
                    author_element = soup.find('h4', string='''
                                                                Author:
                                                            ''')
                    next_author = author_element.find_next('a') if author_element else None
                    data['Author'] = next_author.text.strip() if next_author else 'N/A'
                    if data['CVE'] == data['Author']: data['CVE'] = 'N/A'

                    # 提取 Type
                    type_element = soup.find('h4', string='''
                                                                Type:
                                                            ''')
                    next_type = type_element.find_next('a') if type_element else None
                    data['Type'] = next_type.text.strip() if next_type else 'N/A'

                    # 提取 Platform
                    platform_element = soup.find('h4', string='''
                                                                Platform:
                                                            ''')
                    next_platform = platform_element.find_next('a') if platform_element else None
                    data['Platform'] = next_platform.text.strip() if next_platform else 'N/A'

                    # 提取 Date
                    date_element = soup.find('h4', string='''
                                                                Date:
                                                            ''')
                    next_date = date_element.find_next('h6') if date_element else None
                    data['Date'] = next_date.text.strip() if next_date else 'N/A'

                    # 提取 EDB Verified
                    data['V'] = 1 if soup.find('i', {'class': 'mdi-check'}) else 0
                except Exception as e:
                    print("提取数据时发生错误：", e)
                print(data)
                # 爬取raw部分
                page_content = raw_response.text
                lines = page_content.splitlines()[:20]
                tmp1 = [line for line in lines if line.startswith('#') and 'http' in line]
                tmp2 = [line for line in page_content.splitlines() if not line.startswith("#")]
                body_lines = tmp1 + tmp2
                body_content = "\n".join(body_lines).strip()

                # 存入数据库
                cursor.execute('''
                    INSERT OR IGNORE INTO exploitDetail (EDB_ID, Date, V, Title, Type, Platform, Author, CVE, POC)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    data['EDB-ID'], data['Date'], data['V'], data['Title'], data['Type'], data['Platform'],
                    data['Author'],
                    data['CVE'], body_content))
                conn.commit()
                print(f"EDB-ID {edb_id} data saved.")
            else:
                print(f"EDB-ID {edb_id} not found or error. Response code: {response.status_code}, Raw response code: {raw_response.status_code}")

            edb_id += 1
            sleep_time = random.randint(15, 20)
            print(f"Waiting for {sleep_time} seconds before next request...")
            time.sleep(sleep_time)
        except Exception as e:
            print(f"Error processing EDB-ID {edb_id}: {e}")
            edb_id += 1
            sleep_time = random.randint(15, 20)
            time.sleep(sleep_time)

    conn.close()
    print("Data extraction completed.")


if __name__ == '__main__':
    # 创建数据库目录（如果不存在）
    os.makedirs(os.path.dirname('./exploitDetailed.db') or '.', exist_ok=True)
    
    # 设置代理参数（如有需要）
    proxy = None  # 例如 "http://localhost:10809" 或 "socks5://localhost:10808"
    
    conn = sqlite3.connect('./exploitDetailed.db')
    cursor = conn.cursor()
    cursor.execute('SELECT EDB_ID FROM exploitDetail order by EDB_ID desc limit 2')
    tmp = cursor.fetchall()
    print(tmp)
    
    # 爬取指定范围的exploit-id
    # 示例: 51000-51020
    crawl_exploit_range(51000, 51020, proxy=proxy)
    
    # 要初始化爬取最新的exploit，取消下面的注释
    # latest_edb_id_in_db = find_latest_in_db()
    # if latest_edb_id_in_db != "N/A":
    #     initial_exploitcrawler(latest_edb_id_in_db, proxy=proxy)
