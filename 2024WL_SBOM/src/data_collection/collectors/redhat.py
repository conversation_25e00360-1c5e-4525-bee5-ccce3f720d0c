#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RedHat漏洞数据采集器模块

实现了从RedHat Security Data API采集漏洞数据的功能。
API文档：https://access.redhat.com/documentation/en-us/red_hat_security_data_api/
"""

from datetime import datetime
from typing import Dict, List, Any
import logging

from .base import BaseVulnerabilityCollector

logger = logging.getLogger(__name__)

class RedHatCollector(BaseVulnerabilityCollector):
    """
    RedHat漏洞数据采集器
    
    通过RedHat Security Data API获取安全公告数据。
    支持按时间范围查询和增量更新。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化RedHat采集器
        
        Args:
            config: 配置信息，除基类配置外，还可包含：
                - per_page: 每页结果数（默认100）
                - max_results: 最大结果数（默认无限制）
        """
        super().__init__(config)
        self.per_page = config.get('per_page', 100)
        self.max_results = config.get('max_results', float('inf'))  # 默认无限制
        
        # 记录限制
        if self.max_results < float('inf'):
            logger.info(f"【限制设置】RedHat最大结果数限制: {self.max_results}条")
            
    def fetch_data(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内的RedHat安全公告数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含漏洞信息的字典列表
            
        Raises:
            ValueError: 日期范围无效时
            RequestError: API请求失败时
        """
        # 验证日期范围
        self.validate_date_range(start_date, end_date)
        
        all_advisories = []
        page = 1
        
        try:
            logger.info(f"【开始采集】RedHat安全公告 - 时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            if self.max_results < float('inf'):
                logger.info(f"【限制设置】最大结果数限制: {self.max_results}条")
            
            while True:
                # 检查是否达到最大结果数限制
                if len(all_advisories) >= self.max_results:
                    logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止采集")
                    break
                
                # 计算本次请求的每页结果数
                current_per_page = self.per_page
                if self.max_results < float('inf'):
                    # 如果设置了最大结果数限制，确保不超过限制
                    current_per_page = min(current_per_page, self.max_results - len(all_advisories))
                
                # 构建请求参数
                params = {
                    'after': start_date.strftime('%Y-%m-%d'),
                    'before': end_date.strftime('%Y-%m-%d'),
                    'page': page,
                    'per_page': current_per_page
                }
                
                logger.info(f"【请求数据】页码: {page}, 每页结果数: {current_per_page}")
                
                # 发送API请求
                response_data = self.make_api_request(
                    endpoint='cve.json',
                    params=params
                )
                
                # 检查是否有数据
                if not response_data or not isinstance(response_data, list) or len(response_data) == 0:
                    logger.info("【采集完成】没有更多数据，结束获取")
                    break
                
                # 记录获取的数据量
                current_count = len(response_data)
                logger.info(f"【获取数据】第{page}页获取到 {current_count} 条安全公告")
                
                # 清理并添加数据
                for i, advisory in enumerate(response_data):
                    # 检查是否达到最大结果数限制
                    if len(all_advisories) >= self.max_results:
                        logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止处理")
                        break
                    
                    cleaned_data = self.clean_data(advisory)
                    all_advisories.append(cleaned_data)
                    
                    # 定期打印进度
                    if (i+1) % 10 == 0 or i+1 == current_count:
                        logger.info(f"【处理进度】页内: {i+1}/{current_count}, 总进度: {len(all_advisories)}")
                
                # 如果本页获取的数据量小于请求的每页数量，说明没有更多数据了
                if current_count < current_per_page:
                    logger.info("【采集完成】没有更多数据，结束获取")
                    break
                
                # 检查是否达到最大结果数限制
                if len(all_advisories) >= self.max_results:
                    logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止采集")
                    break
                
                page += 1
            
            logger.info(f"【采集完成】RedHat数据采集结束，共获取 {len(all_advisories)} 条漏洞数据")
            return all_advisories
            
        except Exception as e:
            logger.error(f"【采集错误】获取RedHat数据失败: {str(e)}")
            if all_advisories:
                logger.info(f"【部分数据】在失败前已采集 {len(all_advisories)} 条数据")
            return all_advisories
            
    def clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理和标准化RedHat漏洞数据
        
        Args:
            data: 原始RedHat数据
            
        Returns:
            清理后的数据
        """
        try:
            # 提取基本信息
            cleaned = {
                'source': 'RedHat',
                'id': data.get('CVE'),
                'title': data.get('bugzilla_description'),
                'public_date': data.get('public_date'),
                'modified_date': data.get('modified_date'),
                'severity': data.get('severity'),
                'state': data.get('state'),
                
                # 提取CVSS评分
                'metrics': {
                    'cvss_v3': self._extract_cvss_v3(data),
                    'cvss_v2': self._extract_cvss_v2(data)
                },
                
                # 提取受影响的包信息
                'affected_packages': self._extract_affected_packages(data),
                
                # 提取参考链接
                'references': self._extract_references(data),
                
                # 提取修复信息
                'fixes': self._extract_fixes(data),
                
                # 原始数据
                'raw_data': data
            }
            
            return cleaned
            
        except Exception as e:
            logger.error(f"清理RedHat数据失败: {str(e)}")
            return data
            
    def _extract_cvss_v3(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取CVSS v3评分信息"""
        cvss3 = data.get('cvss3', {})
        return {
            'version': '3.0',
            'vector_string': cvss3.get('cvss3_scoring_vector'),
            'base_score': cvss3.get('cvss3_base_score'),
            'status': cvss3.get('status')
        } if cvss3 else {}
        
    def _extract_cvss_v2(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取CVSS v2评分信息"""
        cvss2 = data.get('cvss', {})
        return {
            'version': '2.0',
            'vector_string': cvss2.get('cvss_scoring_vector'),
            'base_score': cvss2.get('cvss_base_score'),
            'status': cvss2.get('status')
        } if cvss2 else {}
        
    def _extract_affected_packages(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取受影响的包信息"""
        packages = []
        for package in data.get('affected_packages', []):
            packages.append({
                'name': package.get('package_name'),
                'module': package.get('module_name'),
                'product': package.get('product_name'),
                'release': package.get('release'),
                'arch': package.get('arch'),
                'fix_state': package.get('fix_state')
            })
        return packages
        
    def _extract_references(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取参考链接"""
        references = []
        
        # 添加Bugzilla链接
        if data.get('bugzilla'):
            references.append({
                'type': 'bugzilla',
                'url': f"https://bugzilla.redhat.com/{data['bugzilla']}"
            })
            
        # 添加其他参考链接
        for ref in data.get('references', []):
            references.append({
                'type': 'other',
                'url': ref
            })
            
        return references
        
    def _extract_fixes(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取修复信息"""
        fixes = []
        for fix in data.get('fixes', []):
            fixes.append({
                'ticket': fix.get('ticket'),
                'state': fix.get('state'),
                'resolution': fix.get('resolution'),
                'release': fix.get('release')
            })
        return fixes 