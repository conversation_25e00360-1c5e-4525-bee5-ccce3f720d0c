#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CNNVD漏洞数据采集器模块

实现了从中国国家漏洞数据库(CNNVD)采集漏洞数据的功能。
通过网页爬取方式获取CNNVD的漏洞信息，存储到本地SQLite数据库。
官方网站：https://www.cnnvd.org.cn/
"""

import os
import sys
import argparse
import sqlite3
import datetime
import requests
import json
import time
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Dict, List, Any, Tuple, Optional
from lxml import html
import re


class CNNVDCrawler:
    """
    CNNVD漏洞数据采集器
    
    通过爬取CNNVD官方网站获取漏洞数据信息。
    支持单线程和多线程采集模式，可以指定起始和结束页进行采集。
    采集的数据存储在本地SQLite数据库中。
    """
    
    # API接口和配置常量
    Pages_url = "https://www.cnnvd.org.cn/web/homePage/cnnvdVulList"
    Poc_url = "https://www.cnnvd.org.cn/web/cnnvdVul/getCnnnvdDetailOnDatasource"
    Timeout = 6
    Time_Delay = 2
    Max_retries = 3
    
    # HTTP请求头配置
    header = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json;charset=UTF-8',
        'Origin': 'https://www.cnnvd.org.cn',
        'Pragma': 'no-cache',
        'Referer': 'https://www.cnnvd.org.cn/home/<USER>',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode':'cors',
        'Sec-Fetch-Site': 'same-origin',
        'sec-ch-ua': '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    }
    
    # 任务状态和配置类
    class Task:
        """任务状态和配置信息"""
        count = 0            # 成功采集的记录数
        skipped_count = 0    # 跳过的记录数（重复记录）
        failure_flag = False # 任务失败标志
        process = 30         # 进程数量
        threads = 17         # 线程数量
        page_has_duplicate = False  # 当前页面是否包含重复数据的标志
    
    # 分页请求参数配置类
    class Page:
        """
        分页请求参数配置
        
        用于构建CNNVD网站分页查询请求的参数。
        支持自定义关键词、危害等级、漏洞类型等条件。
        """
        def __init__(self, pageIndex=1, pageSize=50, keyword="", hazardLevel="", vulType="", vendor="", product="", dateType=""):
            """
            初始化分页请求参数
            
            Args:
                pageIndex: 页码，默认为1
                pageSize: 每页记录数，默认为50
                keyword: 搜索关键词，支持CNNVD编号、CVE编号等
                hazardLevel: 危害等级（1:严重; 2:高危; 3:中危; 4:低危）
                vulType: 漏洞类型，需要使用页面上的哈希值
                vendor: 厂商名称，参考vendor.json
                product: 产品名称，当前暂不可用
                dateType: 日期类型（publishTime/updateTime）
            """
            self.info = {
                "pageIndex": pageIndex,  # 页码
                "pageSize": pageSize,    # 每页记录数
                "keyword": keyword,      # 关键词，支持CNNVD编号、CVE编号
                "hazardLevel": hazardLevel,  # 危害等级（1:严重; 2:高危; 3:中危; 4:低危）
                "vulType": vulType,      # 漏洞类型，需要使用页面上的哈希值
                "vendor": vendor,        # 厂商名称，参考vendor.json
                "product": product,      # 产品名称，当前暂不可用
                "dateType": dateType,    # 日期类型（publishTime/updateTime）
                # 可选参数，与dateType一起使用
                # beginTime:"2023-09-23", 
                # endTime:"2024-06-06",
            }
    
    def __init__(self):
        """
        初始化CNNVD爬虫
        
        设置数据库路径、表名和命令行描述信息，
        并初始化数据库结构。
        """
        # 设置路径
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 直接使用固定的数据目录路径
        self.data_dir = "/home/<USER>/data"
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 数据库配置
        self.database_path = os.path.join(self.data_dir, "cnnvd_data.db")
        self.tablename = "cnnvd_data"
        
        # 命令行工具描述
        self.description = "CNNVD_spider_requests: Crawl CNNVD Data Rapidly"
        self.ps = ">>> 需要指明起始页数和结尾页数 <<<"
        
        # 初始化数据库
        self.init_database()

    # ================ DATABASE METHODS ================
    
    def init_database(self) -> None:
        """
        初始化SQLite数据库并创建表（如果不存在）
        
        创建包含漏洞信息字段的表结构，并设置主键和默认值。
        """
        conn, cursor = self.connect_database()
        try:
            # 创建表（如果不存在）
            cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS {self.tablename} (
                cnnvd_id TEXT PRIMARY KEY,
                vulName TEXT NOT NULL,
                cve_id TEXT DEFAULT NULL,
                vulType TEXT DEFAULT NULL,
                hazardLevel INTEGER DEFAULT 3,
                vulDesc TEXT DEFAULT NULL,
                referUrl TEXT DEFAULT NULL,
                patch TEXT DEFAULT NULL,
                updateTime TIMESTAMP
            )
            ''')
            conn.commit()
        finally:
            self.close_database(conn, cursor)
        print("Database initialized successfully")

    def connect_database(self) -> Tuple[sqlite3.Connection, sqlite3.Cursor]:
        """
        连接SQLite数据库
        
        Returns:
            包含数据库连接和游标的元组
        """
        conn = sqlite3.connect(self.database_path)
        cursor = conn.cursor()
        return conn, cursor

    def insert_data(self, conn: sqlite3.Connection, cursor: sqlite3.Cursor, values: Tuple) -> None:
        """
        将数据插入SQLite数据库
        
        Args:
            conn: 数据库连接
            cursor: 数据库游标
            values: 包含要插入数据的元组
        """
        sql = f"INSERT INTO {self.tablename} (cnnvd_id, vulName, cve_id, vulType, hazardLevel, vulDesc, referUrl, patch, updateTime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"
        cursor.execute(sql, values)
        conn.commit()

    def close_database(self, conn: sqlite3.Connection, cursor: sqlite3.Cursor) -> None:
        """
        关闭SQLite数据库连接
        
        Args:
            conn: 数据库连接
            cursor: 数据库游标
        """
        cursor.close()
        conn.close()

    def edit_data(self, source_data: Dict[str, Any]) -> Tuple:
        """
        格式化数据用于数据库插入
        
        将API返回的原始数据转换为数据库表结构所需的格式。
        
        Args:
            source_data: 原始漏洞数据字典
            
        Returns:
            格式化后的数据元组，用于数据库插入
        """
        cnnvd_id = source_data['cnnvdCode']
        vulName = source_data['vulName']
        cve_id = source_data['cveCode']
        vulType = source_data['vulType']
        hazardLevel = source_data['hazardLevel']
        vulDesc = source_data['vulDesc']
        referUrl = source_data['referUrl']
        patch = source_data['patch']
        updateTime = datetime.datetime.strptime(source_data['updateTime'], "%Y-%m-%d %H:%M:%S")
        
        res = (cnnvd_id, vulName, cve_id, vulType, hazardLevel, vulDesc, referUrl, patch, updateTime)
        return res

    def sqlite_insert_data(self, source_data: Dict[str, Any]) -> None:
        """
        插入数据到SQLite数据库并处理错误
        
        处理重复记录和其他数据库错误，更新任务状态计数器。
        
        Args:
            source_data: 原始漏洞数据字典
        """
        conn = None
        try:
            conn, cursor = self.connect_database()
            values = self.edit_data(source_data)
            self.insert_data(conn, cursor, values)
            print(">>> save to database successfully!")
            self.Task.count += 1  # 增加成功计数
        except sqlite3.IntegrityError as ie:
            # 标记当前页面存在重复记录
            self.Task.page_has_duplicate = True
            self.Task.skipped_count += 1  # 增加跳过计数
            print(f">>> record already exists, skipping to next page... {source_data['cnnvdCode']}")
        except sqlite3.Error as e:
            print(">>> database wrong! skip!", e)  
        finally:
            if conn:
                self.close_database(conn, cursor)

    # ================ ENGINE METHODS ================
    
    def get_page_pocs(self, page_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        获取页面漏洞列表
        
        通过POST请求获取指定页码的漏洞记录列表。
        支持自动重试机制。
        
        Args:
            page_info: 分页请求参数
            
        Returns:
            包含漏洞基本信息的字典列表
            
        Raises:
            AssertionError: 网络请求失败时
        """
        retries = 0
        connected = False
        while not connected and retries < self.Max_retries:
            try:
                response = requests.post(self.Pages_url, headers=self.header, json=page_info, timeout=self.Timeout)
                connected = True
            except requests.exceptions.RequestException:
                retries += 1
                print(f'Timeout, retry {retries} ...')

        assert response.status_code == 200, ">>> network wrong! u can't get page"
        page_pocs = json.loads(response.text)["data"]["records"]

        return page_pocs

    def get_poc(self, poc: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取漏洞详情
        
        根据漏洞ID获取完整的漏洞详情信息。
        支持自动重试机制。
        
        Args:
            poc: 包含漏洞基本信息的字典
            
        Returns:
            包含漏洞详细信息的字典
        """
        retries = 0
        connected = False
        goal = {}
        Poc_info = {}
        Poc_info['id'] = poc['id']
        Poc_info['vulType'] = poc['vulType']
        Poc_info['cnnvdCode'] = poc['cnnvdCode']
        while not connected and retries < self.Max_retries:
            try:
                response = requests.post(self.Poc_url, headers=self.header, json=Poc_info, timeout=self.Timeout)
                goal = json.loads(response.text)["data"]["cnnvdDetail"]
                connected = True
            except Exception as e:
                retries += 1
                print(">>> wrong info:", e)
                print(f'>>> something wrong, retry {retries} ... if retry==3 ,we will skip')

        return goal

    def delay_time(self) -> None:
        """
        请求间延迟
        
        在请求之间添加延迟，避免被服务器限制访问。
        """
        print(">>>> delay..." + str(self.Time_Delay) + "s")
        time.sleep(self.Time_Delay)

    def engine_start_one(self, page_info: Dict[str, Any]) -> bool:
        """
        使用单线程处理一个页面
        
        顺序获取并处理指定页面中的所有漏洞记录。
        如果遇到重复记录，会提前结束并跳到下一页。
        
        Args:
            page_info: 分页请求参数
            
        Returns:
            是否应继续爬取下一页的布尔值
        """
        # 重置当前页面的重复标志
        self.Task.page_has_duplicate = False
        
        page_pocs = self.get_page_pocs(page_info)
        if len(page_pocs) == 0:
            print(">>> no search results, engine stop")
            return False
        else:
            for i in page_pocs:
                goal = self.get_poc(i)
                if len(goal) != 0:
                    print(">>> now dealing..." + goal['vulName'])
                    self.sqlite_insert_data(goal)
                    # 如果发现重复记录，跳转到下一页
                    if self.Task.page_has_duplicate:
                        print(">>> Duplicate found, skipping to next page")
                        return True  # 继续下一页
                self.delay_time()
            return True  # 继续下一页

    def engine_start_thread(self, page_info: Dict[str, Any]) -> bool:
        """
        使用多线程处理一个页面
        
        先检查第一条记录以快速确定是否需要跳过当前页，
        然后使用线程池并行处理剩余记录。
        
        Args:
            page_info: 分页请求参数
            
        Returns:
            是否应继续爬取下一页的布尔值
        """
        # 重置当前页面的重复标志
        self.Task.page_has_duplicate = False
        
        page_pocs = self.get_page_pocs(page_info)
        if len(page_pocs) == 0:
            print(">>> no search results, engine stop")
            return False
        else:
            # 先检查第一条记录，快速确定是否需要跳过该页面
            if len(page_pocs) > 0:
                first_poc = self.get_poc(page_pocs[0])
                if len(first_poc) != 0:
                    print(">>> checking first record..." + first_poc['vulName'])
                    self.sqlite_insert_data(first_poc)
                    if self.Task.page_has_duplicate:
                        print(">>> First record is duplicate, skipping to next page")
                        return True  # 继续下一页
            
            # 如果第一条记录不是重复的，处理剩余记录
            remaining_pocs = page_pocs[1:] if len(page_pocs) > 0 else []
            
            if len(remaining_pocs) > 0:
                with ThreadPoolExecutor(max_workers=self.Task.threads) as t:
                    goals = [t.submit(self.get_poc, i) for i in remaining_pocs]
                    
                    for goal in as_completed(goals):
                        goal = goal.result()
                        if len(goal) != 0:
                            print(">>> now dealing..." + goal['vulName'])
                            self.sqlite_insert_data(goal)
                            # 如果发现重复记录，提前返回
                            if self.Task.page_has_duplicate:
                                print(">>> Duplicate found, skipping to next page")
                                return True  # 继续下一页
            
            return True  # 继续下一页

    def get_total_pages(self, token=None) -> int:
        """
        获取漏洞数据的总页数
        
        通过POST请求API获取总记录数，然后计算总页数
        
        Args:
            token: 可选的认证token
        
        Returns:
            总页数
        """
        print(">>> 正在通过API获取总页数...")
        
        # 创建基本的分页请求参数
        page = self.Page(pageIndex=1, pageSize=50)
        
        # 创建请求头，复制原始header
        headers = dict(self.header)
        
        # 如果提供了token，添加到请求头中
        if token:
            headers['token'] = token
            print(">>> 使用提供的token进行请求")
        
        # 尝试获取总页数
        retries = 0
        connected = False
        
        while not connected and retries < self.Max_retries:
            try:
                # 发送POST请求到API
                response = requests.post(
                    self.Pages_url, 
                    headers=headers, 
                    json=page.info, 
                    timeout=self.Timeout
                )
                connected = True
                
                # 检查响应状态码
                if response.status_code != 200:
                    print(f">>> API响应状态码异常: {response.status_code}")
                    retries += 1
                    continue
                    
                # 解析API响应
                data = json.loads(response.text)
                
                # API成功响应的格式: {"code": 200, "success": true, "data": {"total": 287936, "pageSize": 10, ...}}
                if data.get("code") == 200 and data.get("success") and "data" in data:
                    api_data = data["data"]
                    total_records = api_data.get("total", 0)  # 总记录数
                    page_size = api_data.get("pageSize", 50)  # 每页记录数
                    
                    # 计算总页数
                    total_pages = (total_records + page_size - 1) // page_size
                    print(f">>> API获取成功: 总记录数: {total_records}, 每页记录数: {page_size}, 总页数: {total_pages}")
                    return total_pages
                else:
                    print(f">>> API响应异常: code={data.get('code')}, success={data.get('success')}")
                    print(f">>> 响应数据: {data}")
                    retries += 1
                    
            except (requests.exceptions.RequestException, json.JSONDecodeError) as e:
                retries += 1
                print(f'>>> 请求或解析错误: {e}, 重试 {retries}/{self.Max_retries}')
                time.sleep(1)  # 短暂等待后重试
        
        # 如果所有尝试均失败，使用默认值
        print(">>> 无法获取总页数，使用默认值10")
        return 10

    def parse_api_response_for_pages(self, api_response: str) -> int:
        """
        解析API响应以获取总页数
        
        直接从API响应JSON字符串中提取总记录数和页面大小，计算总页数
        
        Args:
            api_response: API响应的JSON字符串
        
        Returns:
            总页数，失败时返回0
        """
        try:
            data = json.loads(api_response)
            
            # 验证响应格式
            if data.get("code") == 200 and data.get("success") and "data" in data:
                api_data = data["data"]
                total_records = api_data.get("total", 0)  # 总记录数
                page_size = api_data.get("pageSize", 50)  # 每页记录数
                
                # 计算总页数
                if total_records > 0 and page_size > 0:
                    total_pages = (total_records + page_size - 1) // page_size
                    return total_pages
                
            print(f">>> API响应格式不符合预期: {data}")
            return 0
            
        except json.JSONDecodeError as e:
            print(f">>> 无法解析API响应: {e}")
            return 0

    def get_latest_record_date(self) -> datetime.datetime:
        """
        获取数据库中最新记录的日期
        
        Returns:
            最新记录的更新时间，如果没有记录则返回1970-01-01
        """
        conn, cursor = self.connect_database()
        try:
            cursor.execute(f"SELECT updateTime FROM {self.tablename} ORDER BY updateTime DESC LIMIT 1")
            result = cursor.fetchone()
            
            if result and result[0]:
                latest_date = result[0]
                if isinstance(latest_date, str):
                    latest_date = datetime.datetime.strptime(latest_date, "%Y-%m-%d %H:%M:%S")
                print(f">>> 数据库中最新记录日期: {latest_date}")
                return latest_date
            else:
                # 如果没有记录，返回一个很早的日期
                return datetime.datetime(1970, 1, 1)
        except Exception as e:
            print(f">>> 获取最新记录日期出错: {e}, 使用默认日期1970-01-01")
            return datetime.datetime(1970, 1, 1)
        finally:
            self.close_database(conn, cursor)

    def auto_crawl(self, mode="all", max_pages=None) -> None:
        """
        自动爬取模式
        
        自动确定起始页和终止页，然后开始爬取数据。
        
        Args:
            mode: 爬取模式，"all"表示全部爬取，"update"表示只爬取更新
            max_pages: 最大爬取页数，防止爬取过多页面
        """
        # 获取总页数
        total_pages = self.get_total_pages()
        
        # 如果指定了最大页数，则限制总页数
        if max_pages and max_pages > 0 and max_pages < total_pages:
            total_pages = max_pages
            print(f">>> 限制爬取页数为: {max_pages}")
        
        if mode == "all":
            # 全部爬取模式，从第1页开始
            print(f">>> 自动爬取模式: 全部爬取, 从第1页到第{total_pages}页")
            self.get_all(1, total_pages)
        else:
            # 更新模式，只爬取最新的页面
            # 默认爬取前10页，通常新漏洞都在前面几页
            update_pages = min(10, total_pages)
            print(f">>> 自动爬取模式: 更新模式, 从第1页到第{update_pages}页")
            self.update_data(1, update_pages)

    # ================ CLI METHODS ================
    
    def update_data(self, start_page: int, end_page: int) -> None:
        """
        使用单线程模式更新数据
        
        从指定的起始页到结束页爬取数据，使用单线程模式。
        
        Args:
            start_page: 起始页码
            end_page: 结束页码
        """
        Start_page = start_page
        End_page = end_page

        for i in range(Start_page, End_page + 1):
            print(">>> now crawling page ", i)
            Page_info = self.Page(i, 50).info
            continue_crawl = self.engine_start_one(Page_info)
            if not continue_crawl:
                break  # 如果引擎指示停止，则中断爬取
        
        print(f">>> task finished, success: {self.Task.count}, skipped: {self.Task.skipped_count}")

    def get_all_pre(self, i: int) -> bool:
        """
        处理get_all方法的单个页面
        
        Args:
            i: 页码
            
        Returns:
            是否应继续爬取下一页的布尔值
        """
        print(">>> now crawling page ", i)
        Page_info = self.Page(i, 50).info
        return self.engine_start_thread(Page_info)

    def get_all(self, start_page: int, end_page: int) -> None:
        """
        使用多线程模式获取所有数据
        
        从指定的起始页到结束页爬取数据，使用多线程模式加速爬取。
        
        Args:
            start_page: 起始页码
            end_page: 结束页码
        """
        Start_page = start_page
        End_page = end_page

        # 由于ProcessPoolExecutor不能很好地与实例方法一起工作，
        # 为简单起见，顺序运行任务
        for i in range(Start_page, End_page + 1):
            continue_crawl = self.get_all_pre(i)
            if not continue_crawl:
                break  # 如果引擎指示停止，则中断爬取

        print(f">>> task finished, success: {self.Task.count}, skipped: {self.Task.skipped_count}")

    def parse_cli(self, args: argparse.Namespace, parser: argparse.ArgumentParser) -> None:
        """
        解析命令行参数
        
        根据命令行参数决定使用哪种模式爬取数据。
        
        Args:
            args: 解析后的命令行参数
            parser: 命令行参数解析器实例
        """
        # 自动模式优先
        if args.auto:
            if args.all:
                self.auto_crawl(mode="all", max_pages=args.max_pages)
            elif args.update:
                self.auto_crawl(mode="update", max_pages=args.max_pages)
            else:
                # 默认使用更新模式
                self.auto_crawl(mode="update", max_pages=args.max_pages)
        # 手动指定页码模式
        elif args.start_page is not None and args.end_page is not None:
            if args.all:
                self.get_all(args.start_page, args.end_page)
            elif args.update:
                self.update_data(args.start_page, args.end_page)
            else:
                # 未指定模式，默认使用更新模式
                self.update_data(args.start_page, args.end_page)
        else:
            parser.print_help()

    def run_cli(self) -> None:
        """
        运行命令行界面
        
        设置命令行参数并解析用户输入。
        """
        parser = argparse.ArgumentParser(formatter_class=argparse.RawTextHelpFormatter,
                                        description=self.description,
                                        epilog=self.ps)

        # 爬取模式参数
        crawler_mode = parser.add_argument_group('爬取模式')
        crawler_mode.add_argument('-A', '--all', action='store_true', help='使用多线程爬取所有数据')
        crawler_mode.add_argument('-U', '--update', action='store_true', help='使用单线程更新数据')
        crawler_mode.add_argument('--auto', action='store_true', help='自动模式：自动确定起始页和终止页')
        
        # 页码参数
        page_args = parser.add_argument_group('页码参数')
        page_args.add_argument('-s', '--start_page', type=int, help='起始页码')
        page_args.add_argument('-e', '--end_page', type=int, help='终止页码')
        page_args.add_argument('--max_pages', type=int, help='自动模式下的最大爬取页数')
        
        args = parser.parse_args()

        self.parse_cli(args, parser)



if __name__ == "__main__":
    # 创建CNNVDCrawler类的实例  
    crawler = CNNVDCrawler()
    
    # 启动爬虫
    crawler.run_cli()