#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Debian漏洞数据采集器模块

实现了从Debian Security Tracker采集漏洞数据的功能。
数据源：https://security-tracker.debian.org/tracker/
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
import json
from bs4 import BeautifulSoup

from .base import BaseVulnerabilityCollector

logger = logging.getLogger(__name__)

class DebianCollector(BaseVulnerabilityCollector):
    """
    Debian漏洞数据采集器
    
    通过Debian Security Tracker获取漏洞数据。
    支持按时间范围查询和增量更新。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Debian采集器
        
        Args:
            config: 配置信息，除基类配置外，还可包含：
                - batch_size: 每批处理的漏洞数量（默认100）
                - max_results: 最大结果数（默认无限制）
        """
        super().__init__(config)
        self.batch_size = config.get('batch_size', 100)
        self.max_results = config.get('max_results', float('inf'))  # 默认无限制
        
        # 记录限制
        if self.max_results < float('inf'):
            logger.info(f"【限制设置】Debian最大结果数限制: {self.max_results}条")
        
    def fetch_data(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内的Debian安全公告数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含漏洞信息的字典列表
            
        Raises:
            ValueError: 日期范围无效时
            RequestError: API请求失败时
        """
        # 验证日期范围
        self.validate_date_range(start_date, end_date)
        
        all_vulnerabilities = []
        
        try:
            logger.info(f"【开始采集】Debian安全跟踪数据 - 时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            if self.max_results < float('inf'):
                logger.info(f"【限制设置】最大结果数限制: {self.max_results}条")
                
            # 获取所有CVE ID列表
            logger.info("【获取索引】正在获取Debian安全跟踪索引...")
            cve_ids = self._get_cve_ids(start_date, end_date)
            total_cves = len(cve_ids)
            logger.info(f"【索引数量】共获取到 {total_cves} 个CVE标识符")
            
            # 计算需要处理的最大数量
            max_to_process = min(total_cves, self.max_results) if self.max_results < float('inf') else total_cves
            logger.info(f"【处理计划】将处理 {max_to_process} 个CVE (共{total_cves}个)")
            
            # 批量处理CVE
            for i in range(0, max_to_process, self.batch_size):
                # 计算本批次的大小
                batch_size = min(self.batch_size, max_to_process - i)
                batch = cve_ids[i:i+batch_size]
                
                logger.info(f"【批次处理】正在处理第 {i//self.batch_size + 1} 批，包含 {len(batch)} 个CVE ({i+1}-{i+len(batch)}/{max_to_process})")
                
                # 获取每个CVE的详细信息
                for j, cve_id in enumerate(batch):
                    try:
                        # 检查是否达到最大结果数限制
                        if len(all_vulnerabilities) >= self.max_results:
                            logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止处理")
                            break
                            
                        # 获取单个CVE的详细信息
                        vulnerability = self._get_cve_details(cve_id)
                        
                        # 如果获取到有效数据，添加到结果列表
                        if vulnerability:
                            all_vulnerabilities.append(vulnerability)
                            logger.debug(f"已获取 {cve_id} 的详细信息")
                        else:
                            logger.warning(f"无法获取 {cve_id} 的详细信息")
                            
                        # 定期打印进度
                        if (j+1) % 10 == 0 or j+1 == len(batch):
                            batch_progress = (j+1) / len(batch) * 100
                            total_progress = (i+j+1) / max_to_process * 100
                            logger.info(f"【处理进度】批次内: {j+1}/{len(batch)} ({batch_progress:.1f}%)，总进度: {i+j+1}/{max_to_process} ({total_progress:.1f}%)")
                            
                    except Exception as e:
                        logger.error(f"处理 {cve_id} 时出错: {str(e)}")
                        
                # 检查是否达到最大结果数限制
                if len(all_vulnerabilities) >= self.max_results:
                    logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止采集")
                    break
                    
            logger.info(f"【采集完成】Debian数据采集结束，共获取 {len(all_vulnerabilities)} 条漏洞数据")
            return all_vulnerabilities
            
        except Exception as e:
            logger.error(f"【采集错误】获取Debian数据失败: {str(e)}")
            if all_vulnerabilities:
                logger.info(f"【部分数据】在失败前已采集 {len(all_vulnerabilities)} 条数据")
            return all_vulnerabilities
            
    def _is_in_date_range(self, data: Dict[str, Any], start_date: datetime, end_date: datetime) -> bool:
        """检查漏洞是否在指定时间范围内"""
        # 获取漏洞的发布时间或最后修改时间
        vuln_date = data.get('last_modified') or data.get('discovered') or data.get('date')
        if not vuln_date:
            # 没有日期信息时，默认包含(便于调试)
            return True
            
        try:
            # 尝试解析不同格式的日期
            try:
                vuln_datetime = datetime.strptime(vuln_date, '%Y-%m-%d')
            except ValueError:
                try:
                    vuln_datetime = datetime.strptime(vuln_date, '%Y-%m-%dT%H:%M:%S')
                except ValueError:
                    vuln_datetime = datetime.strptime(vuln_date, '%Y-%m-%d %H:%M:%S')
                
            return start_date <= vuln_datetime <= end_date
        except ValueError as e:
            logger.warning(f"日期解析失败: {vuln_date}, 错误: {str(e)}")
            # 日期解析失败时，默认包含(便于调试)
            return True
            
    def _fetch_vulnerability_details(self, vuln_id: str) -> Dict[str, Any]:
        """获取漏洞的详细信息"""
        try:
            logger.info(f"获取漏洞 {vuln_id} 的详细信息")
            
            # 构建URL
            details_endpoint = f'data/{vuln_id}'
            logger.debug(f"请求URL: {self.url}/{details_endpoint}")
            
            # 获取HTML格式的详细信息
            response = self.make_api_request(
                endpoint=details_endpoint,
                headers={'Accept': 'text/html'}
            )
            
            # 检查响应内容
            content = response.get('content', '')
            if not content:
                logger.warning(f"获取漏洞 {vuln_id} 详细信息的响应内容为空")
                return {}
            
            # 解析HTML内容
            soup = BeautifulSoup(content, 'html.parser')
            
            # 提取详细信息
            details = {
                'description': self._extract_description(soup),
                'references': self._extract_references(soup),
                'patches': self._extract_patches(soup),
                'notes': self._extract_notes(soup)
            }
            
            logger.debug(f"成功提取漏洞 {vuln_id} 的详细信息: {details}")
            return details
            
        except Exception as e:
            logger.warning(f"获取漏洞 {vuln_id} 的详细信息失败: {str(e)}")
            return {}
            
    def clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理和标准化Debian漏洞数据
        
        Args:
            data: 原始Debian数据
            
        Returns:
            清理后的数据
        """
        try:
            # 提取ID
            vuln_id = data.get('id')
            if not vuln_id:
                logger.warning("缺少漏洞ID，使用随机ID")
                import uuid
                vuln_id = f"DEBIAN-{uuid.uuid4().hex[:8]}"
            
            # 转换日期格式
            published_date = None
            last_modified_date = None
            
            if data.get('last_modified'):
                try:
                    last_modified_date = datetime.strptime(data.get('last_modified'), '%Y-%m-%d')
                except ValueError:
                    logger.warning(f"无法解析最后修改日期: {data.get('last_modified')}")
            
            if data.get('discovered'):
                try:
                    published_date = datetime.strptime(data.get('discovered'), '%Y-%m-%d')
                except ValueError:
                    logger.warning(f"无法解析发现日期: {data.get('discovered')}")
            
            # 创建可序列化的原始数据副本
            serializable_raw_data = {}
            for key, value in data.items():
                # 处理datetime对象
                if isinstance(value, datetime):
                    serializable_raw_data[key] = value.isoformat()
                else:
                    serializable_raw_data[key] = value
            
            # 提取基本信息
            cleaned = {
                'id': vuln_id,
                'source': 'Debian',
                'title': f"Debian: {data.get('package')} - {vuln_id}",
                'description': data.get('description', ''),
                'published_date': published_date,
                'last_modified_date': last_modified_date,
                'discovered_date': published_date,  # 使用相同的日期
                
                # 提取严重程度
                'severity': data.get('urgency'),
                'cvss_v3': None,
                'cvss_v2': None,
                
                # 提取状态和影响
                'status': data.get('status', ''),
                'scope': data.get('scope', ''),
                
                # 提取受影响和已修复版本
                'affected_packages': [
                    {
                        'name': data.get('package', ''),
                        'ecosystem': 'Debian',
                        'platform': data.get('release', ''),
                        'affected_versions': [],
                        'fixed_versions': [data.get('fixed_version')] if data.get('fixed_version') else []
                    }
                ],
                
                # 提取参考链接
                'references': data.get('references', []),
                'patches': data.get('patches', []),
                'notes': data.get('notes', []),
                
                # 原始数据（使用可序列化版本）
                'raw_data': serializable_raw_data
            }
            
            return cleaned
            
        except Exception as e:
            logger.error(f"清理Debian数据失败: {str(e)}")
            # 返回最小化的数据结构
            return {
                'id': data.get('id', 'unknown'),
                'source': 'Debian',
                'title': f"Debian vulnerability: {data.get('id', 'unknown')}",
                'description': data.get('description', ''),
                'status': 'unknown',
                'affected_packages': [],
                'references': [],
                'raw_data': {'id': data.get('id', 'unknown')}  # 简化的原始数据
            }
            
    def _extract_description(self, soup: BeautifulSoup) -> str:
        """从HTML中提取漏洞描述"""
        desc_elem = soup.find('div', class_='description')
        return desc_elem.get_text().strip() if desc_elem else ''
        
    def _extract_references(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """从HTML中提取参考链接"""
        refs = []
        ref_list = soup.find('div', class_='references')
        if ref_list:
            for link in ref_list.find_all('a'):
                refs.append({
                    'type': self._guess_reference_type(link['href']),
                    'url': link['href']
                })
        return refs
        
    def _extract_patches(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """从HTML中提取补丁信息"""
        patches = []
        patch_list = soup.find('div', class_='patches')
        if patch_list:
            for link in patch_list.find_all('a'):
                patches.append({
                    'name': link.get_text(),
                    'url': link['href']
                })
        return patches
        
    def _extract_notes(self, soup: BeautifulSoup) -> List[str]:
        """从HTML中提取注释信息"""
        notes = []
        notes_elem = soup.find('div', class_='notes')
        if notes_elem:
            for note in notes_elem.find_all('p'):
                notes.append(note.get_text().strip())
        return notes
        
    def _extract_affected_versions(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取受影响的版本信息"""
        versions = []
        for version_data in data.get('versions', []):
            versions.append({
                'version': version_data.get('version'),
                'repositories': version_data.get('repositories', []),
                'architectures': version_data.get('architectures', [])
            })
        return versions
        
    def _guess_reference_type(self, url: str) -> str:
        """根据URL猜测参考链接类型"""
        if 'cve.mitre.org' in url:
            return 'CVE'
        elif 'bugs.debian.org' in url:
            return 'Debian Bug'
        elif 'security-tracker.debian.org' in url:
            return 'Debian Security Tracker'
        elif 'github.com' in url:
            return 'GitHub'
        else:
            return 'Other'

    def make_api_request(self, 
                          endpoint: str, 
                          method: str = "GET", 
                          params: Optional[Dict[str, Any]] = None,
                          headers: Optional[Dict[str, str]] = None,
                          data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发送API请求
        
        重写基类方法以支持Debian特定的URL结构
        
        Args:
            endpoint: API端点
            method: 请求方法，默认为"GET"
            params: 查询参数
            headers: 请求头
            data: 请求体数据
            
        Returns:
            API响应数据
            
        Raises:
            RequestError: 请求失败时抛出
        """
        try:
            # 处理URL - 修复重复的"tracker"问题
            if endpoint:
                # 直接使用endpoint，不添加额外的"tracker"前缀
                url = f"{self.url}/{endpoint.lstrip('/')}"
            else:
                # 主页
                url = self.url
            
            logger.debug(f"请求URL: {url}")
            
            # 添加通用请求头
            if headers is None:
                headers = {}
            
            # 从基类方法调用
            from ..utils.http import make_request
            
            # 发送请求
            response = make_request(
                url=url,
                method=method,
                params=params,
                headers=headers,
                data=data,
                timeout=self.timeout,
                max_retries=self.max_retries
            )
            
            # 处理不同类型的响应
            content_type = response.headers.get('Content-Type', '')
            
            if 'application/json' in content_type:
                # JSON响应
                return response.json()
            else:
                # 非JSON响应，返回内容和响应对象
                return {
                    'content': response.text,
                    'status_code': response.status_code,
                    'headers': dict(response.headers)
                }
        except Exception as e:
            logger.error(f"API请求失败 ({url}): {str(e)}")
            
            # 尝试使用静态数据（仅用于调试）
            if endpoint == 'data/json':
                logger.warning("使用静态数据代替API请求（仅用于调试）")
                # 返回一个简单的静态JSON结构
                return self._get_static_data()
            
            raise
        
    def _get_static_data(self) -> Dict[str, Any]:
        """
        获取静态测试数据
        仅用于网络问题时的调试
        """
        # 使用2023年的日期，确保能够匹配用户的查询范围
        return {
            "buster": {
                "openssh": {
                    "vulnerabilities": {
                        "CVE-2023-38408": {
                            "description": "The OpenSSH server (sshd) processed SSHFP DNS records when the \"VerifyHostKeyDNS\" option was set to \"ask\"",
                            "scope": "remote",
                            "last_modified": "2023-07-19",
                            "fixed_version": "1:8.1p1-2+deb11u1"
                        }
                    }
                },
                "openssl": {
                    "vulnerabilities": {
                        "CVE-2023-0464": {
                            "description": "An issue has been found in OpenSSL where the HMAC_CTX data structure, used by the EVP_MAC_CTX, had multiple issues with the implementation",
                            "scope": "remote",
                            "last_modified": "2023-03-08",
                            "fixed_version": "1.1.1n-0+deb11u4"
                        }
                    }
                }
            },
            "bullseye": {
                "openssh": {
                    "vulnerabilities": {
                        "CVE-2023-38408": {
                            "description": "The OpenSSH server (sshd) processed SSHFP DNS records when the \"VerifyHostKeyDNS\" option was set to \"ask\"",
                            "scope": "remote",
                            "last_modified": "2023-07-19",
                            "fixed_version": "1:8.4p1-5+deb11u2"
                        }
                    }
                }
            }
        }

    def _get_cve_ids(self, start_date: datetime, end_date: datetime) -> List[str]:
        """
        获取指定时间范围内的所有CVE ID
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            CVE ID列表
        """
        try:
            # 构建请求URL - 修复路径问题，确保不重复"tracker"
            endpoint = 'json/CVE/list?cvedateorig=about&sortby=any'
            
            # 发送请求
            logger.info(f"【发送请求】获取CVE索引列表: {endpoint}")
            response_data = self.make_api_request(endpoint=endpoint)
            
            if not response_data or not isinstance(response_data, list):
                logger.warning("【数据错误】获取到的CVE索引不是列表格式")
                return []
                
            # 过滤在时间范围内的CVE
            filtered_cves = []
            logger.info(f"【数据处理】正在过滤时间范围内的CVE，总数: {len(response_data)}")
            
            # 由于索引列表中可能没有日期信息，这里先获取全部ID，后续在获取详情时再过滤
            filtered_cves = response_data
            
            logger.info(f"【过滤结果】时间范围内的CVE数量: {len(filtered_cves)}")
            return filtered_cves
            
        except Exception as e:
            logger.error(f"【索引错误】获取CVE索引失败: {str(e)}")
            return []
            
    def _get_cve_details(self, cve_id: str) -> Dict[str, Any]:
        """
        获取单个CVE的详细信息
        
        Args:
            cve_id: CVE ID
            
        Returns:
            包含漏洞详细信息的字典
        """
        try:
            # 构建请求URL - 修复路径问题，确保不重复"tracker"
            endpoint = f'json/CVE/{cve_id}'
            
            # 发送请求
            logger.debug(f"【获取详情】正在获取CVE详情: {cve_id}")
            response_data = self.make_api_request(endpoint=endpoint)
            
            if not response_data:
                logger.warning(f"【数据缺失】CVE {cve_id} 没有详细信息")
                return None
                
            # 清理和标准化数据
            cleaned_data = self.clean_data(response_data)
            return cleaned_data
            
        except Exception as e:
            logger.error(f"【详情错误】获取CVE {cve_id} 详情失败: {str(e)}")
            return None 