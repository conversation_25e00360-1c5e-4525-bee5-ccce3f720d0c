#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
POC与Neo4j集成示例脚本

本脚本演示了如何：
1. 使用collect_poc.py收集POC并存储到SQLite
2. 将SQLite中的所有POC批量导入Neo4j

使用方法：
$ python poc_neo4j_import_example.py --cve CVE-2021-44228 --save-neo4j

也可以直接批量导入已有数据：
$ python poc_neo4j_import_example.py --batch-import-only
"""

import argparse
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
script_dir = Path(__file__).resolve().parent
project_root = script_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入所需模块
from src.data_collection.scripts.collect_poc import main as collect_poc_main
from src.data_collection.collectors.poc_collection.sqlite2neo4j import batch_import_to_neo4j

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"{script_dir}/poc_neo4j_import.log")
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='POC收集与Neo4j导入示例')
    
    parser.add_argument('--cve', help='要处理的CVE ID，例如CVE-2021-44228')
    parser.add_argument('--db-path', default='/home/<USER>/data/poc/exploitDetailed.db', 
                        help='SQLite数据库路径')
    parser.add_argument('--save-neo4j', action='store_true', 
                        help='将POC保存到Neo4j')
    parser.add_argument('--use-llm', action='store_true', 
                        help='如果ExploitDB未找到POC，使用LLM生成')
    parser.add_argument('--only-llm', action='store_true', 
                        help='直接使用LLM生成POC，跳过ExploitDB')
    parser.add_argument('--batch-import-only', action='store_true', 
                        help='仅批量导入已有数据到Neo4j，不进行POC收集')
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 如果只是批量导入
    if args.batch_import_only:
        logger.info("开始将SQLite中的所有POC批量导入Neo4j")
        try:
            batch_import_to_neo4j(args.db_path)
            logger.info("成功将所有POC批量导入Neo4j")
            return
        except Exception as e:
            logger.error(f"批量导入POC到Neo4j时出错: {str(e)}")
            return
    
    # 如果需要收集POC
    if args.cve:
        # 设置命令行参数
        sys.argv = [
            'collect_poc.py',
            '--cve', args.cve,
            '--db-path', args.db_path
        ]
        
        # 添加可选参数
        if args.save_neo4j:
            sys.argv.append('--save-neo4j')
        if args.use_llm:
            sys.argv.append('--use-llm')
        if args.only_llm:
            sys.argv.append('--only-llm')
        
        # 调用collect_poc.py的main函数
        logger.info(f"开始收集CVE {args.cve}的POC")
        try:
            collect_poc_main()
            logger.info("POC收集完成")
        except Exception as e:
            logger.error(f"收集POC时出错: {str(e)}")
    else:
        logger.error("未指定CVE ID。请使用--cve参数指定CVE ID，或使用--batch-import-only仅导入已有数据")

if __name__ == "__main__":
    main() 