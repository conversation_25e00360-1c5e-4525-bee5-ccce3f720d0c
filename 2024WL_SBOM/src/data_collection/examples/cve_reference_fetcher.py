#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CVE引用信息获取示例

此示例展示如何：
1. 获取指定时间段内的CVE及其引用信息
2. 获取指定CVE ID的引用信息
"""

import os
import sys
import json
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from data_collection.collectors.nvd import NVDCollector
from data_collection.utils.logger import logger

class CVEReferenceFetcher:
    """CVE引用信息获取器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化CVE引用信息获取器
        
        Args:
            config_path: 配置文件路径
        """
        # 尝试多个可能的配置文件位置
        if config_path is None:
            possible_paths = [
                os.path.join(project_root, "config/sources.json"),
                os.path.join(project_root, "src/data_collection/config/sources.json"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "../config/sources.json"),
                "/home/<USER>/config/sources.json",
                "/home/<USER>/src/data_collection/config/sources.json"
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    self.config_path = path
                    logger.info(f"找到配置文件: {path}")
                    break
            else:
                raise FileNotFoundError("无法找到配置文件，请指定正确的配置文件路径")
        else:
            self.config_path = config_path
            
        self.config = self._load_config()
        self.collector = NVDCollector(self.config['sources']['NVD'])
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            logger.info(f"正在加载配置文件: {self.config_path}")
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise
            
    def get_references_by_timerange(self, 
                                  start_date: datetime, 
                                  end_date: datetime,
                                  date_type: str = "published",
                                  output_file: Optional[str] = None,
                                  save_individual: bool = False,
                                  output_dir: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内的CVE引用信息
        
        Args:
            start_date: 开始时间
            end_date: 结束时间
            date_type: 日期类型，可选 "published" 或 "modified"
            output_file: 输出文件路径（可选）
            save_individual: 是否将每个CVE保存为单独的文件
            output_dir: 单独CVE文件的保存目录
            
        Returns:
            CVE引用信息列表
        """
        # 检查日期范围是否超过120天
        date_diff = (end_date - start_date).days
        if date_diff > 120:
            logger.warning(f"日期范围超过120天({date_diff}天)，NVD API限制最多查询120天的数据，将自动截断")
            end_date = start_date + timedelta(days=120)
            
        logger.info(f"获取 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的CVE引用信息 (按{date_type}日期)")
        
        # 准备保存目录
        if save_individual:
            if output_dir is None:
                # 修复路径构建，避免重复src路径
                default_cve_dir = os.path.join("/home/<USER>", "src", "data_collection", "data", "CVE")
                output_dir = default_cve_dir
            
            # 确保目录存在
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"将单独保存CVE到目录: {output_dir}")
        
        # 构建API参数
        params = {
            'resultsPerPage': 2000,  # 一次获取更多结果
            # 'noRejected': ''  # 移除空参数，避免404错误
        }
        
        # 根据日期类型设置日期参数
        if date_type == "published":
            params['pubStartDate'] = start_date.strftime('%Y-%m-%dT00:00:00.000')
            params['pubEndDate'] = end_date.strftime('%Y-%m-%dT23:59:59.999')
        else:  # modified
            params['lastModStartDate'] = start_date.strftime('%Y-%m-%dT00:00:00.000')
            params['lastModEndDate'] = end_date.strftime('%Y-%m-%dT23:59:59.999')
        
        logger.info(f"API请求参数: {params}")
        
        try:
            # 发送API请求
            response_data = self.collector.make_api_request('', params=params)
            
            # 记录响应信息
            total_results = response_data.get('totalResults', 0)
            logger.info(f"API响应: 总结果数: {total_results}")
            
            if total_results == 0:
                logger.warning("未找到任何CVE数据")
                return []
                
            # 获取并处理所有页的数据
            all_vulnerabilities = []
            start_index = 0
            results_per_page = params['resultsPerPage']
            
            while start_index < total_results:
                # 对于第一页，我们已经有了数据
                if start_index == 0:
                    vulnerabilities = response_data.get('vulnerabilities', [])
                else:
                    # 更新起始索引
                    params['startIndex'] = start_index
                    logger.info(f"获取下一页数据: startIndex={start_index}")
                    
                    # 发送请求获取下一页
                    page_data = self.collector.make_api_request('', params=params)
                    vulnerabilities = page_data.get('vulnerabilities', [])
                
                # 检查是否获取到数据
                if not vulnerabilities:
                    logger.warning(f"页面 {start_index//results_per_page + 1} 没有数据，停止获取")
                    break
                    
                # 添加到总列表
                all_vulnerabilities.extend(vulnerabilities)
                logger.info(f"已获取 {len(all_vulnerabilities)}/{total_results} 条数据")
                
                # 更新起始索引
                start_index += len(vulnerabilities)
                
                # 检查是否已获取所有数据
                if start_index >= total_results:
                    logger.info("已获取所有数据，停止分页请求")
                    break
            
            # 处理数据
            results = []
            saved_count = 0
            
            for vuln in all_vulnerabilities:
                try:
                    vuln_data = self.collector.clean_data(vuln)
                    cve_id = vuln_data.get('id')
                    
                    if not cve_id:
                        logger.warning("发现没有CVE ID的数据，跳过")
                        continue
                        
                    cve_info = {
                        'cve_id': cve_id,
                        'published_date': vuln_data.get('published_date'),
                        'last_modified_date': vuln_data.get('last_modified_date'),
                        'descriptions': vuln_data.get('descriptions', []),
                        'references': vuln_data.get('references', [])
                    }
                    
                    results.append(cve_info)
                    
                    # 保存单独的CVE文件
                    if save_individual and output_dir:
                        # 使用CVE ID作为文件名
                        cve_filename = f"{cve_id}.json"
                        cve_file_path = os.path.join(output_dir, cve_filename)
                        
                        # 保存到文件
                        with open(cve_file_path, 'w', encoding='utf-8') as f:
                            json.dump(cve_info, f, indent=2, ensure_ascii=False)
                            
                        saved_count += 1
                        
                        # 定期记录保存进度
                        if saved_count % 10 == 0:
                            logger.info(f"已单独保存 {saved_count} 个CVE文件")
                    
                except Exception as e:
                    logger.error(f"处理CVE数据失败: {str(e)}")
                    continue
            
            logger.info(f"成功处理 {len(results)} 条CVE数据")
            if save_individual:
                logger.info(f"已单独保存 {saved_count} 个CVE文件到目录: {output_dir}")
            
            # 输出到汇总文件（如果指定）
            if output_file:
                self._save_to_file(results, output_file)
                
            return results
            
        except Exception as e:
            logger.error(f"获取CVE数据失败: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return []
        
    def get_references_by_cve_id(self, 
                                cve_id: str,
                                output_file: Optional[str] = None,
                                save_individual: bool = False,
                                output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        获取指定CVE ID的引用信息
        
        Args:
            cve_id: CVE ID（例如：CVE-2024-1234）
            output_file: 输出文件路径（可选）
            save_individual: 是否将CVE保存为单独的文件
            output_dir: CVE文件的保存目录
            
        Returns:
            CVE引用信息
        """
        logger.info(f"获取 {cve_id} 的引用信息")
        
        # 准备保存目录
        if save_individual:
            if output_dir is None:
                # 修复路径构建，避免重复src路径
                default_cve_dir = os.path.join("/home/<USER>", "src", "data_collection", "data", "CVE")
                output_dir = default_cve_dir
            
            # 确保目录存在
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"将保存CVE到目录: {output_dir}")
        
        # 构建API参数
        params = {
            'cveId': cve_id
        }
        
        # 发送API请求
        response_data = self.collector.make_api_request('', params=params)
        
        if not response_data.get('vulnerabilities'):
            logger.warning(f"未找到 {cve_id} 的信息")
            return {}
            
        # 提取第一个（也是唯一的）结果
        vuln_data = self.collector.clean_data(response_data['vulnerabilities'][0])
        
        result = {
            'cve_id': vuln_data.get('id'),
            'published_date': vuln_data.get('published_date'),
            'last_modified_date': vuln_data.get('last_modified_date'),
            'descriptions': vuln_data.get('descriptions', []),
            'references': vuln_data.get('references', [])
        }
        
        # 保存单独的CVE文件
        if save_individual and output_dir:
            # 使用CVE ID作为文件名
            cve_filename = f"{cve_id}.json"
            cve_file_path = os.path.join(output_dir, cve_filename)
            
            # 保存到文件
            with open(cve_file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
                
            logger.info(f"已保存CVE到文件: {cve_file_path}")
        
        # 输出到文件（如果指定）
        if output_file:
            self._save_to_file([result], output_file)
            
        return result
        
    def _save_to_file(self, data: List[Dict[str, Any]], output_file: str):
        """保存结果到文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"结果已保存到: {output_file}")
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            
def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CVE引用信息获取工具')
    
    # 添加通用命令行参数
    parser.add_argument('--config', help='配置文件路径（如果不指定，将自动搜索）')
    
    # 创建子命令
    subparsers = parser.add_subparsers(dest='command', help='命令')
    
    # 按时间范围查询
    time_parser = subparsers.add_parser('time', help='按时间范围查询')
    time_parser.add_argument('--start', required=True, help='开始日期 (YYYY-MM-DD)')
    time_parser.add_argument('--end', required=True, help='结束日期 (YYYY-MM-DD)')
    time_parser.add_argument('--type', choices=['published', 'modified'], default='published',
                           help='日期类型: published(发布日期) 或 modified(修改日期)，默认为published')
    time_parser.add_argument('--output', help='输出汇总文件路径')
    time_parser.add_argument('--split', action='store_true', help='将每个CVE保存为单独的文件')
    time_parser.add_argument('--dir', help='单独CVE文件的保存目录，默认为 ./src/data_collection/data/CVE')
    
    # 按CVE ID查询
    cve_parser = subparsers.add_parser('cve', help='按CVE ID查询')
    cve_parser.add_argument('--id', required=True, help='CVE ID (例如：CVE-2024-1234)')
    cve_parser.add_argument('--output', help='输出文件路径')
    cve_parser.add_argument('--split', action='store_true', help='将CVE保存为单独的文件')
    cve_parser.add_argument('--dir', help='CVE文件的保存目录，默认为 ./src/data_collection/data/CVE')
    
    args = parser.parse_args()
    
    try:
        # 创建获取器实例
        fetcher = CVEReferenceFetcher(args.config)
        
        if args.command == 'time':
            # 解析日期
            start_date = datetime.strptime(args.start, '%Y-%m-%d')
            end_date = datetime.strptime(args.end, '%Y-%m-%d')
            
            # 获取数据
            results = fetcher.get_references_by_timerange(
                start_date=start_date,
                end_date=end_date,
                date_type=args.type,
                output_file=args.output,
                save_individual=args.split,
                output_dir=args.dir
            )
            
            # 打印结果
            if not args.output and not args.split:
                print(json.dumps(results, indent=2, ensure_ascii=False))
                
        elif args.command == 'cve':
            # 获取数据
            result = fetcher.get_references_by_cve_id(
                cve_id=args.id,
                output_file=args.output,
                save_individual=args.split,
                output_dir=args.dir
            )
            
            # 打印结果
            if not args.output and not args.split:
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
        else:
            parser.print_help()
            
    except Exception as e:
        logger.error(f"执行失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
        
if __name__ == '__main__':
    main() 