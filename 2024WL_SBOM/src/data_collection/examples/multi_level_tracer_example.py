#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
多级补丁跟踪示例

展示如何使用补丁跟踪器进行多级数据收集和补丁定位。
本示例演示了如何通过多级递归爬取网页来构建完整的参考网络，从而发现高置信度的补丁。
"""

import os
import sys
import json
import logging
import argparse
from typing import List, Dict, Any
from pprint import pprint

# 添加父目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))
from src.data_collection.services.patch_tracer import PatchTracer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 不同漏洞的初始引用集合
VULNERABILITY_REFERENCES = {
    # Log4Shell
    "CVE-2021-44228": [
        "https://nvd.nist.gov/vuln/detail/CVE-2021-44228",
        "https://github.com/apache/logging-log4j2/pull/608",
        "https://issues.apache.org/jira/browse/LOG4J2-3198",
        "https://logging.apache.org/log4j/2.x/security.html"
    ],
    # Spring4Shell
    "CVE-2022-22965": [
        "https://nvd.nist.gov/vuln/detail/CVE-2022-22965",
        "https://github.com/spring-projects/spring-framework/commit/7ba31e0f11860f1c45d99fc4ae786e0b527664a8",
        "https://tanzu.vmware.com/security/cve-2022-22965"
    ],
    # Heartbleed
    "CVE-2014-0160": [
        "https://nvd.nist.gov/vuln/detail/CVE-2014-0160",
        "https://github.com/openssl/openssl/commit/731f431497f463f3a2a97236fe0187b11c44aead",
        "https://www.openssl.org/news/secadv/20140407.txt"
    ],
    # ShellShock
    "CVE-2014-6271": [
        "https://nvd.nist.gov/vuln/detail/CVE-2014-6271",
        "https://ftp.gnu.org/gnu/bash/bash-4.3-patches/bash43-025",
        "https://access.redhat.com/security/cve/cve-2014-6271"
    ]
}

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="多级补丁跟踪示例")
    parser.add_argument("--cve-id", default="CVE-2021-44228", help="要分析的CVE ID")
    parser.add_argument("--output-dir", default="data/multi_level_tracer", help="输出目录")
    parser.add_argument("--max-depth", type=int, default=3, help="最大爬取深度")
    parser.add_argument("--confidence", type=float, default=0.7, help="补丁置信度阈值")
    parser.add_argument("--github-token", help="GitHub API令牌")
    parser.add_argument("--batch", action="store_true", help="批量处理预定义的CVE列表")
    return parser.parse_args()

def analyze_cve(cve_id: str, args) -> Dict[str, Any]:
    """分析单个CVE"""
    # 获取初始引用
    initial_references = VULNERABILITY_REFERENCES.get(
        cve_id, [f"https://nvd.nist.gov/vuln/detail/{cve_id}"]
    )
    
    # 创建跟踪器
    tracer = PatchTracer(
        output_dir=args.output_dir,
        max_depth=args.max_depth,
        github_token=args.github_token,
        confidence_threshold=args.confidence
    )
    
    # 运行跟踪
    logger.info(f"开始多级跟踪 {cve_id} 的补丁...")
    result = tracer.trace(cve_id, initial_references)
    
    return result

def print_result_summary(result: Dict[str, Any], output_dir: str) -> None:
    """打印结果摘要"""
    cve_id = result["cve_id"]
    
    print("\n" + "=" * 80)
    print(f"CVE ID: {cve_id}".center(80))
    print("=" * 80)
    
    # 网络统计
    print("\n网络统计:")
    print(f"- 总节点数: {result['network_stats']['total_nodes']}")
    print(f"- 总边数: {result['network_stats']['total_edges']}")
    print(f"- 问题节点数: {result['network_stats']['issue_nodes']}")
    print(f"- 补丁节点数: {result['network_stats']['patch_nodes']}")
    
    # 补丁统计
    print("\n补丁统计:")
    print(f"- 发现的潜在补丁数量: {len(result['all_patches'])}")
    print(f"- 高置信度补丁数量: {len(result['high_confidence_patches'])}")
    
    # 高置信度补丁
    if result['high_confidence_patches']:
        print("\n高置信度补丁:")
        for i, patch in enumerate(result['high_confidence_patches'], 1):
            print(f"{i}. {patch['url']} (置信度: {patch['confidence']:.2f})")
            if 'commit_message' in patch['metadata']:
                print(f"   提交信息: {patch['metadata']['commit_message'].splitlines()[0] if patch['metadata']['commit_message'] else ''}")
    else:
        print("\n未找到高置信度补丁")
    
    # 结果路径
    print(f"\n完整结果保存在: {output_dir}/{cve_id.replace('-', '_')}/")
    print("=" * 80 + "\n")

def run_batch_analysis(args) -> None:
    """批量分析多个CVE"""
    cve_list = []
    for cve_id, references in VULNERABILITY_REFERENCES.items():
        cve_list.append({
            "cve_id": cve_id,
            "references": references
        })
    
    # 创建跟踪器
    tracer = PatchTracer(
        output_dir=args.output_dir,
        max_depth=args.max_depth,
        github_token=args.github_token,
        confidence_threshold=args.confidence
    )
    
    # 批量跟踪
    logger.info(f"开始批量跟踪 {len(cve_list)} 个CVE...")
    results = tracer.batch_trace(cve_list)
    
    # 打印摘要
    print("\n" + "=" * 80)
    print("批量跟踪结果摘要".center(80))
    print("=" * 80)
    
    for cve_id, result in results.items():
        status = result["status"]
        if status == "success":
            print(f"{cve_id}: 成功 - 发现 {result['total_patches']} 个潜在补丁，其中 {result['high_confidence_patches']} 个高置信度补丁")
        else:
            print(f"{cve_id}: 失败 - {result['error']}")
    
    print("=" * 80)
    print(f"完整结果保存在: {args.output_dir}/")
    print("=" * 80 + "\n")

def main():
    """主函数"""
    args = parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.batch:
        run_batch_analysis(args)
    else:
        # 分析单个CVE
        result = analyze_cve(args.cve_id, args)
        print_result_summary(result, args.output_dir)
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 