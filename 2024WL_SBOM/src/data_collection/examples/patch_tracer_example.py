#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
补丁跟踪器使用示例

演示如何使用补丁跟踪器来发现CVE的相关补丁。
整合了从NVD和其他来源自动获取引用链接的功能。
支持单个CVE跟踪和时间范围批量跟踪。
"""

import os
import logging
import argparse
import json
import sys
import re
import urllib3
from datetime import datetime, timedelta

# 禁用SSL验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 从服务模块导入重构后的功能
from ..services.patch_tracer import (
    PatchTracer,
    CVEReferenceFetcher,
    get_initial_references,
    prepare_references_for_tracer,
    prioritize_initial_references
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def trace_single_cve(cve_id: str, 
                   data_dir: str, 
                   depth: int, 
                   args: argparse.Namespace,
                   github_token: str = None) -> dict:
    """
    对单个CVE进行补丁跟踪
    
    Args:
        cve_id: CVE ID
        data_dir: 数据保存目录
        depth: 跟踪深度
        args: 命令行参数
        github_token: GitHub API令牌
        
    Returns:
        dict: 跟踪结果
    """
    # 获取引用链接
    logger.info(f"获取 {cve_id} 的初始引用链接")
    references_by_source = get_initial_references(
        cve_id=cve_id,
        data_dir=data_dir,
        config_path=args.config,
        use_cache=args.use_cache
    )
    
    # 打印各来源引用数量
    for source, refs in references_by_source.items():
        logger.info(f"{source} 引用数量: {len(refs)}")
    
    # 准备引用链接
    initial_references, source_mapping = prepare_references_for_tracer(references_by_source)
    
    # 应用优先级排序机制（除非用户选择不使用）
    if not args.no_priority:
        logger.info(f"对初始引用链接应用优先级排序（最低优先级: {args.min_priority}）...")
        initial_references, source_mapping = prioritize_initial_references(
            initial_references, 
            source_mapping,
            min_priority=args.min_priority
        )
        logger.info(f"优先级排序后选择了 {len(initial_references)} 个引用进行处理")
    
    # 初始化补丁跟踪器
    tracer = PatchTracer(
        output_dir=data_dir,
        max_depth=depth,
        request_delay=args.delay,
        github_token=github_token,  # 使用从配置文件或命令行获取的令牌
        confidence_threshold=args.confidence,
        verify_ssl=not args.no_verify_ssl,
        max_retries=args.retries
    )
    
    # 执行补丁跟踪
    logger.info(f"开始跟踪 {cve_id} 的补丁...")
    result = tracer.trace(
        cve_id=cve_id,
        initial_references=initial_references,
        sources=source_mapping
    )
    
    # 打印结果摘要
    print(f"\n=== {cve_id} 补丁跟踪结果摘要 ===")
    print(f"节点总数: {result['network_stats']['total_nodes']}")
    print(f"边总数: {result['network_stats']['total_edges']}")
    print(f"问题节点数: {result['network_stats']['issue_nodes']}")
    print(f"补丁节点数: {result['network_stats']['patch_nodes']}")
    print(f"发现潜在补丁数: {len(result['all_patches'])}")
    print(f"高置信度补丁数: {len(result['high_confidence_patches'])}")
    
    if result['high_confidence_patches']:
        print("\n高置信度补丁:")
        for i, patch in enumerate(result['high_confidence_patches'], 1):
            confidence = patch.get('confidence', 0)
            url = patch.get('url', '')
            print(f"{i}. [置信度: {confidence:.2f}] {url}")
    else:
        print("\n未找到高置信度补丁")
        
    print(f"\n结果保存在: {data_dir}/{cve_id.replace('-', '_')}/")
    
    return result

def trace_timerange(start_date: datetime, 
                  end_date: datetime, 
                  data_dir: str, 
                  depth: int,
                  args: argparse.Namespace,
                  github_token: str = None) -> dict:
    """
    对指定时间范围内的CVE进行批量补丁跟踪
    
    Args:
        start_date: 开始日期
        end_date: 结束日期
        data_dir: 数据保存目录
        depth: 跟踪深度
        args: 命令行参数
        github_token: GitHub API令牌
        
    Returns:
        dict: 跟踪结果汇总
    """
    logger.info(f"准备扫描 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 期间的CVE")
    
    # 初始化CVE引用信息获取器
    config_path = args.config
    if not config_path:
        # 如果未指定配置文件路径，尝试自动查找
        possible_paths = [
            os.path.join(project_root, "src/data_collection/config/sources.json"),
            os.path.join(project_root, "config/sources.json"),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "../../config/sources.json"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config/sources.json"),
            "/home/<USER>/src/data_collection/config/sources.json"
        ]
        
        for path in possible_paths:
            if path and os.path.exists(path):
                config_path = path
                logger.info(f"找到配置文件: {path}")
                break
    
    fetcher = CVEReferenceFetcher(config_path)
    
    # 获取时间范围内的CVE
    cve_dir = os.path.join(data_dir, "cve_data")
    os.makedirs(cve_dir, exist_ok=True)
    
    logger.info(f"正在获取时间范围内的CVE数据...")
    cve_list = fetcher.get_references_by_timerange(
        start_date=start_date,
        end_date=end_date,
        date_type=args.date_type,
        save_individual=True,
        output_dir=cve_dir
    )
    
    if not cve_list:
        logger.warning(f"在指定时间范围内未找到CVE，跳过跟踪")
        return {"cve_count": 0, "results": {}}
    
    logger.info(f"找到 {len(cve_list)} 个CVE，准备跟踪...")
    
    # 跟踪结果汇总
    summary = {
        "time_range": {
            "start": start_date.strftime('%Y-%m-%d'),
            "end": end_date.strftime('%Y-%m-%d'),
            "date_type": args.date_type
        },
        "cve_count": len(cve_list),
        "traced_count": 0,
        "success_count": 0,
        "has_patches_count": 0,
        "high_confidence_patches_count": 0,
        "results": {}
    }
    
    # 跟踪范围限制
    if args.max_cves and args.max_cves < len(cve_list):
        logger.info(f"由于设置了最大CVE数量限制({args.max_cves})，将只跟踪部分CVE")
        cve_list = cve_list[:args.max_cves]
        summary["cve_count"] = len(cve_list)
    
    # 按照发布时间排序，先处理最新的
    if args.newest_first:
        cve_list.sort(key=lambda x: x.get('published_date', ''), reverse=True)
        logger.info("按发布时间从新到旧排序")
    
    # 遍历CVE进行跟踪
    for i, cve_info in enumerate(cve_list, 1):
        cve_id = cve_info.get('cve_id')
        if not cve_id:
            logger.warning(f"第 {i} 个CVE没有ID，跳过")
            continue
        
        try:
            logger.info(f"[{i}/{len(cve_list)}] 开始跟踪 {cve_id}")
            
            # 为每个CVE创建单独的子目录
            cve_output_dir = os.path.join(data_dir, cve_id.replace('-', '_'))
            os.makedirs(cve_output_dir, exist_ok=True)
            
            # 跟踪这个CVE
            result = trace_single_cve(
                cve_id=cve_id, 
                data_dir=data_dir, 
                depth=depth, 
                args=args,
                github_token=github_token
            )
            
            # 更新统计信息
            summary["traced_count"] += 1
            summary["success_count"] += 1
            
            # 统计有补丁的CVE数量
            has_patches = len(result.get('all_patches', [])) > 0
            has_high_confidence = len(result.get('high_confidence_patches', [])) > 0
            
            if has_patches:
                summary["has_patches_count"] += 1
                
            if has_high_confidence:
                summary["high_confidence_patches_count"] += 1
                
            # 记录详细结果
            summary["results"][cve_id] = {
                "status": "success",
                "has_patches": has_patches,
                "has_high_confidence_patches": has_high_confidence,
                "patch_count": len(result.get('all_patches', [])),
                "high_confidence_patch_count": len(result.get('high_confidence_patches', []))
            }
            
            # 保存每个CVE的高置信度补丁
            if has_high_confidence:
                summary["results"][cve_id]["high_confidence_patches"] = [
                    {"url": p.get('url', ''), "confidence": p.get('confidence', 0)}
                    for p in result.get('high_confidence_patches', [])
                ]
            
            # 每处理10个CVE，输出一次进度报告
            if i % 10 == 0 or i == len(cve_list):
                logger.info(f"已完成 {i}/{len(cve_list)} 个CVE的跟踪")
                logger.info(f"发现补丁的CVE: {summary['has_patches_count']}, 有高置信度补丁的CVE: {summary['high_confidence_patches_count']}")
                
                # 保存临时汇总报告
                summary_file = os.path.join(data_dir, "timerange_summary.json")
                with open(summary_file, 'w', encoding='utf-8') as f:
                    json.dump(summary, f, indent=2, ensure_ascii=False)
                logger.info(f"已保存临时汇总报告到: {summary_file}")
                
        except Exception as e:
            logger.error(f"跟踪 {cve_id} 时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 记录错误信息
            summary["results"][cve_id] = {
                "status": "error",
                "error": str(e)
            }
            continue
    
    # 保存最终汇总报告
    summary_file = os.path.join(data_dir, "timerange_summary.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    logger.info(f"已保存最终汇总报告到: {summary_file}")
    
    # 输出汇总统计
    print("\n=== 时间范围跟踪结果汇总 ===")
    print(f"时间范围: {summary['time_range']['start']} 到 {summary['time_range']['end']} ({summary['time_range']['date_type']})")
    print(f"总CVE数量: {summary['cve_count']}")
    print(f"成功跟踪数量: {summary['success_count']}")
    print(f"发现补丁的CVE数量: {summary['has_patches_count']}")
    print(f"有高置信度补丁的CVE数量: {summary['high_confidence_patches_count']}")
    print(f"详细结果保存在: {summary_file}")
    
    return summary

def main():
    """
    补丁跟踪器示例的主函数
    
    此函数实现了完整的补丁跟踪流程，从命令行参数解析到结果生成：
    
    1. 命令行参数处理：
       - 解析CVE ID、跟踪深度、输出目录等参数
       - 配置日志级别和格式
       
    2. 初始引用获取阶段：
       - 从各个来源获取初始CVE引用信息
       - 按平台对引用进行分类和计数
       - 对引用进行优先级排序
       
    3. 补丁跟踪阶段：
       - 初始化PatchTracer实例
       - 配置跟踪参数（深度、延迟、超时等）
       - 运行跟踪器完成网络构建和补丁识别
       
    4. 结果处理阶段：
       - 保存跟踪结果到JSON和图像文件
       - 汇总并显示统计信息
       - 输出高置信度补丁和补丁摘要
    
    整个流程是自动化的，通过命令行参数控制行为，
    例如：
      单个CVE: python -m src.data_collection.examples.patch_tracer_example -c CVE-2021-44228 --depth 3
      时间范围: python -m src.data_collection.examples.patch_tracer_example --start-date 2023-01-01 --end-date 2023-01-31 --depth 2
    """
    parser = argparse.ArgumentParser(description="CVE补丁跟踪工具")
    
    # 模式选择（单个CVE或时间范围）
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--cve", "-c", type=str,
                      help="要分析的CVE ID，例如：CVE-2021-44832")
    group.add_argument("--start-date", type=str,
                      help="开始日期 (YYYY-MM-DD)")
    
    # 时间范围相关参数
    parser.add_argument("--end-date", type=str,
                      help="结束日期 (YYYY-MM-DD)，默认为今天")
    parser.add_argument("--date-type", choices=['published', 'modified'],
                      default='published', help="日期类型，默认为published")
    parser.add_argument("--max-cves", type=int, default=0,
                      help="最多处理的CVE数量，默认不限制")
    parser.add_argument("--newest-first", action="store_true",
                      help="优先处理最新的CVE")
    
    # 基本参数
    parser.add_argument("--data-dir", "-d", type=str, default="data/patch",
                      help="数据保存目录，默认为data/patch")
    parser.add_argument("--config", type=str, default=None,
                      help="配置文件路径，默认自动查找")
    parser.add_argument("--depth", type=int, default=3,
                      help="最大引用深度，默认为3")
    parser.add_argument("--delay", type=float, default=0.5,
                      help="请求延迟(秒)，默认为0.5秒")
    parser.add_argument("--github-token", type=str, default=None,
                      help="GitHub API令牌，默认从配置文件获取")
    parser.add_argument("--confidence", type=float, default=0.7,
                      help="补丁置信度阈值，默认0.7")
    parser.add_argument("--no-verify-ssl", action="store_true",
                      help="禁用SSL证书验证")
    parser.add_argument("--use-cache", action="store_true", default=True,
                      help="使用缓存数据（如果存在）")
    parser.add_argument("--retries", type=int, default=3,
                      help="请求重试次数，默认3次")
    
    # 添加优先级相关参数
    parser.add_argument("--no-priority", action="store_true",
                      help="禁用引用优先级排序")
    parser.add_argument("--min-priority", choices=['critical', 'high', 'medium', 'low', 'very_low'],
                      default='medium', help="最低包含的优先级(默认medium)")
    
    # 为了兼容旧的调用方式，添加别名
    parser.add_argument("--cve-id", type=str, help="要分析的CVE ID（已弃用，请使用--cve）")
    parser.add_argument("--output-dir", type=str, help="输出目录（已弃用，请使用--data-dir）")
    parser.add_argument("--max-depth", type=int, help="最大爬取深度（已弃用，请使用--depth）")
    
    args = parser.parse_args()
    
    # 检查参数
    if args.start_date and not args.end_date:
        # 如果只指定了开始日期，使用今天作为结束日期
        args.end_date = datetime.now().strftime('%Y-%m-%d')
        logger.info(f"未指定结束日期，使用今天: {args.end_date}")
    
    # 处理兼容性参数
    cve_id = args.cve if args.cve else args.cve_id
    
    data_dir = args.data_dir if args.data_dir else args.output_dir
    if not data_dir:
        data_dir = "data/patch"
    
    depth = args.depth if args.depth else args.max_depth
    if not depth:
        depth = 3
    
    # 创建数据目录
    os.makedirs(data_dir, exist_ok=True)
    
    # 尝试从配置文件加载GitHub令牌
    github_token = args.github_token
    if not github_token:
        # 如果没有指定配置文件路径，尝试自动查找
        config_paths = [
            args.config,  # 首先检查用户指定的路径
            os.path.join(project_root, "src/data_collection/config/sources.json"),
            os.path.join(project_root, "config/sources.json"),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "../../config/sources.json"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config/sources.json"),
            "/home/<USER>/src/data_collection/config/sources.json"
        ]
        
        # 尝试每个可能的配置文件路径
        for config_path in config_paths:
            if config_path and os.path.exists(config_path):
                try:
                    with open(config_path, 'r') as f:
                        config = json.load(f)
                        if 'sources' in config and 'GitHub' in config['sources']:
                            github_token = config['sources']['GitHub'].get('api_key')
                            if github_token:
                                logger.info(f"从配置文件 {config_path} 加载GitHub令牌: {github_token[:4]}...")
                                break
                except Exception as e:
                    logger.error(f"加载配置文件 {config_path} 失败: {str(e)}")
                    continue
    
    # 根据模式选择执行逻辑
    if cve_id:
        # 单个CVE模式
        cve_id = cve_id.upper()
        
        # 验证CVE ID格式
        if not re.match(r'^CVE-\d{4}-\d{4,}$', cve_id):
            print(f"错误：无效的CVE ID格式: {cve_id}，示例：CVE-2021-44832")
            return
            
        # 跟踪单个CVE
        trace_single_cve(cve_id, data_dir, depth, args, github_token)
    else:
        # 时间范围模式
        try:
            start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
            end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
            
            # 检查日期是否有效
            if start_date > end_date:
                parser.error("开始日期不能晚于结束日期")
                return
                
            # 创建时间范围子目录
            timerange_dir = os.path.join(
                data_dir, 
                f"timerange_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
            )
            os.makedirs(timerange_dir, exist_ok=True)
            
            # 跟踪时间范围内的CVE
            trace_timerange(start_date, end_date, timerange_dir, depth, args, github_token)
            
        except ValueError as e:
            parser.error(f"日期格式错误，请使用YYYY-MM-DD格式: {str(e)}")
            return

if __name__ == "__main__":
    main() 
