#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
漏洞风险评估服务模块

提供针对漏洞的风险评估功能，包括：
1. 从NVD获取CVSS评分
2. 从EPSS网站获取漏洞利用概率评分
3. 检查是否存在漏洞的POC
4. 基于多维度指标的风险评分和分类

使用示例:
```python
from src.data_collection.services.risk_assessment import RiskAssessmentService

# 初始化风险评估服务
service = RiskAssessmentService()

# 评估单个CVE
result = service.assess_cve_risk("CVE-2021-44228")
print(f"风险等级: {result['risk_level']}")
print(f"CVSS分数: {result['cvss_score']}")
print(f"EPSS分数: {result['epss_score']}")
print(f"存在POC: {result['has_poc']}")
```
"""

import os
import re
import sys
import json
import time
import logging
import requests
import sqlite3
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from urllib.parse import urljoin

# 确保可以导入项目模块
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 从项目中导入其他模块
from src.data_collection.services.patch_tracer.reference_fetcher import CVEReferenceFetcher
from src.data_collection.services.graph_store import GraphStore
from src.data_collection.utils.logger import setup_logger
from src.data_collection.collectors.nvd import NVDCollector

# 设置日志记录器
logger = logging.getLogger(__name__)

class RiskAssessmentService:
    """漏洞风险评估服务"""
    
    def __init__(self, 
                config_path: Optional[str] = None,
                graph_url: str = "bolt://localhost:7687",
                graph_user: str = "neo4j",
                graph_password: str = "password",
                poc_db_path: str = "/home/<USER>/data/poc/exploitDetailed.db",
                epss_cache_path: str = "/home/<USER>/data/epss_cache.json",
                epss_cache_ttl: int = 24, # 小时
                verify_ssl: bool = False):
        """
        初始化风险评估服务
        
        Args:
            config_path: 配置文件路径
            graph_url: Neo4j图数据库URL
            graph_user: Neo4j用户名
            graph_password: Neo4j密码
            poc_db_path: POC SQLite数据库路径
            epss_cache_path: EPSS评分缓存文件路径
            epss_cache_ttl: EPSS缓存有效期（小时）
            verify_ssl: 是否验证SSL证书
        """
        self.config_path = config_path
        self.verify_ssl = verify_ssl
        
        # 初始化NVD引用获取器
        self.reference_fetcher = CVEReferenceFetcher(config_path=config_path)
        
        # 初始化图数据库连接
        self.graph_store = GraphStore(
            uri=graph_url,
            username=graph_user,
            password=graph_password
        )
        
        # POC数据库路径
        self.poc_db_path = poc_db_path
        
        # EPSS缓存设置
        self.epss_cache_path = epss_cache_path
        self.epss_cache_ttl = epss_cache_ttl
        self.epss_cache = self._load_epss_cache()
        
        # 确保缓存目录存在
        os.makedirs(os.path.dirname(self.epss_cache_path), exist_ok=True)
        
        logger.info("风险评估服务初始化完成")
    
    def _load_epss_cache(self) -> Dict[str, Any]:
        """加载EPSS缓存"""
        if os.path.exists(self.epss_cache_path):
            try:
                with open(self.epss_cache_path, 'r') as f:
                    cache = json.load(f)
                
                # 检查缓存是否过期
                if 'timestamp' in cache:
                    cache_time = datetime.fromisoformat(cache['timestamp'])
                    if datetime.now() - cache_time > timedelta(hours=self.epss_cache_ttl):
                        logger.info(f"EPSS缓存已过期 ({cache_time.isoformat()})")
                        return {'data': {}, 'timestamp': datetime.now().isoformat()}
                    else:
                        logger.info(f"已加载EPSS缓存，包含 {len(cache.get('data', {}))} 条记录")
                        return cache
                
                return {'data': {}, 'timestamp': datetime.now().isoformat()}
            except Exception as e:
                logger.error(f"加载EPSS缓存失败: {e}")
                return {'data': {}, 'timestamp': datetime.now().isoformat()}
        else:
            logger.info("EPSS缓存文件不存在，将创建新缓存")
            return {'data': {}, 'timestamp': datetime.now().isoformat()}
    
    def _save_epss_cache(self) -> None:
        """保存EPSS缓存"""
        try:
            with open(self.epss_cache_path, 'w') as f:
                json.dump(self.epss_cache, f, indent=2)
            logger.debug(f"已保存EPSS缓存到 {self.epss_cache_path}")
        except Exception as e:
            logger.error(f"保存EPSS缓存失败: {e}")
    
    def get_cvss_score(self, cve_id: str) -> Dict[str, Any]:
        """
        从NVD获取CVE的CVSS评分信息
        
        Args:
            cve_id: CVE ID
            
        Returns:
            Dict: CVSS评分信息
        """
        logger.info(f"正在从NVD获取 {cve_id} 的CVSS评分")
        
        try:
            # 直接使用NVDCollector获取CVE信息
            from src.data_collection.collectors.nvd import NVDCollector
            
            # 检查配置文件中是否存在NVD配置
            config = {}
            if self.config_path:
                try:
                    with open(self.config_path, 'r') as f:
                        config_data = json.load(f)
                        if 'sources' in config_data and 'NVD' in config_data['sources']:
                            config = config_data['sources']['NVD']
                            logger.debug(f"已加载NVD配置: {config}")
                except Exception as e:
                    logger.error(f"加载配置文件失败: {e}")
            
            # 初始化NVD收集器
            nvd_collector = NVDCollector(config)
            
            # 构建API参数
            params = {
                'cveId': cve_id
            }
            
            # 发送API请求
            response_data = nvd_collector.make_api_request('', params=params)
            
            if not response_data.get('vulnerabilities'):
                logger.warning(f"未找到 {cve_id} 的信息")
                return {
                    'cve_id': cve_id,
                    'cvss_score': None,
                    'cvss_severity': None,
                    'cvss_vector': None,
                    'cvss_version': None,
                    'error': '未找到CVE信息'
                }
            
            # 提取第一个（也是唯一的）结果
            vuln_data = response_data['vulnerabilities'][0]
            cleaned_data = nvd_collector.clean_data(vuln_data)
            
            # 提取CVSS信息
            metrics = cleaned_data.get('metrics', {})
            
            # 首选CVSSv3分数
            cvss_v3 = metrics.get('cvss_v3', {})
            if cvss_v3:
                return {
                    'cve_id': cve_id,
                    'cvss_score': cvss_v3.get('base_score'),
                    'cvss_severity': cvss_v3.get('base_severity'),
                    'cvss_vector': cvss_v3.get('vector_string'),
                    'cvss_version': cvss_v3.get('version')
                }
            
            # 如果没有v3，尝试获取v2
            cvss_v2 = metrics.get('cvss_v2', {})
            if cvss_v2:
                return {
                    'cve_id': cve_id,
                    'cvss_score': cvss_v2.get('base_score'),
                    'cvss_severity': cvss_v2.get('severity'),
                    'cvss_vector': cvss_v2.get('vector_string'),
                    'cvss_version': '2.0'
                }
            
            # 如果没有CVSS数据
            logger.warning(f"{cve_id} 没有CVSS评分数据")
            return {
                'cve_id': cve_id,
                'cvss_score': None,
                'cvss_severity': None,
                'cvss_vector': None,
                'cvss_version': None,
                'error': '未找到CVSS评分数据'
            }
            
        except Exception as e:
            logger.error(f"获取 {cve_id} 的CVSS评分失败: {e}", exc_info=True)
            return {
                'cve_id': cve_id,
                'cvss_score': None,
                'cvss_severity': None,
                'cvss_vector': None,
                'cvss_version': None,
                'error': str(e)
            }
    
    def get_epss_score(self, cve_id: str) -> Dict[str, Any]:
        """
        从EPSS网站获取漏洞利用概率评分
        
        Args:
            cve_id: CVE ID
            
        Returns:
            Dict: EPSS评分信息
        """
        logger.info(f"获取 {cve_id} 的EPSS评分")
        
        # 首先检查缓存
        if cve_id in self.epss_cache.get('data', {}):
            logger.info(f"从缓存中获取 {cve_id} 的EPSS评分")
            return self.epss_cache['data'][cve_id]
        
        # 准备请求EPSS API
        epss_api_url = "https://api.first.org/data/v1/epss"
        params = {"cve": cve_id}
        
        try:
            # 发送请求
            response = requests.get(
                epss_api_url,
                params=params,
                verify=self.verify_ssl,
                timeout=10
            )
            
            # 检查响应
            if response.status_code == 200:
                data = response.json()
                
                # 处理EPSS响应
                if 'data' in data and data['data']:
                    epss_data = data['data'][0]
                    result = {
                        'cve_id': cve_id,
                        'epss_score': float(epss_data.get('epss', 0)),
                        'epss_percentile': float(epss_data.get('percentile', 0)),
                        'epss_date': epss_data.get('date')
                    }
                    
                    # 更新缓存
                    if 'data' not in self.epss_cache:
                        self.epss_cache['data'] = {}
                    self.epss_cache['data'][cve_id] = result
                    self._save_epss_cache()
                    
                    return result
                else:
                    logger.warning(f"EPSS API未返回 {cve_id} 的数据")
                    result = {
                        'cve_id': cve_id,
                        'epss_score': None,
                        'epss_percentile': None,
                        'epss_date': None,
                        'error': 'API未返回数据'
                    }
                    return result
            else:
                logger.error(f"EPSS API请求失败，状态码: {response.status_code}")
                return {
                    'cve_id': cve_id,
                    'epss_score': None,
                    'epss_percentile': None,
                    'epss_date': None,
                    'error': f'API请求失败，状态码: {response.status_code}'
                }
                
        except Exception as e:
            logger.error(f"获取 {cve_id} 的EPSS评分失败: {e}")
            return {
                'cve_id': cve_id,
                'epss_score': None,
                'epss_percentile': None,
                'epss_date': None,
                'error': str(e)
            }
    
    def check_poc_existence(self, cve_id: str) -> Dict[str, Any]:
        """
        检查是否存在CVE的POC
        
        Args:
            cve_id: CVE ID
            
        Returns:
            Dict: POC存在情况
        """
        logger.info(f"检查 {cve_id} 是否存在POC")
        
        result = {
            'cve_id': cve_id,
            'has_poc': False,
            'poc_count': 0,
            'poc_sources': [],
            'poc_details': []
        }
        
        # 1. 首先检查SQLite数据库
        try:
            # 连接到POC数据库
            conn = sqlite3.connect(self.poc_db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 首先检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='exploits'")
            if cursor.fetchone():
                # 查询ExploitDB表
                cursor.execute(
                    "SELECT * FROM exploits WHERE cve_id LIKE ? OR cve_id = ?", 
                    (f"%{cve_id}%", cve_id)
                )
                rows = cursor.fetchall()
                
                if rows:
                    logger.info(f"在ExploitDB中找到 {len(rows)} 个与 {cve_id} 相关的POC")
                    result['has_poc'] = True
                    result['poc_count'] += len(rows)
                    
                    if 'exploitdb' not in result['poc_sources']:
                        result['poc_sources'].append('exploitdb')
                    
                    # 添加POC详情
                    for row in rows:
                        row_dict = dict(row)
                        result['poc_details'].append({
                            'id': row_dict.get('id') or row_dict.get('edb_id'),
                            'title': row_dict.get('title'),
                            'source': 'exploitdb',
                            'url': f"https://www.exploit-db.com/exploits/{row_dict.get('id')}"
                        })
            else:
                logger.debug("POC数据库中不存在exploits表，跳过ExploitDB查询")
            
            # 检查LLM POC表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='llm_poc'")
            if cursor.fetchone():
                # 查询LLM POC表
                cursor.execute(
                    "SELECT * FROM llm_poc WHERE CVE_ID LIKE ? OR CVE_ID = ?", 
                    (f"%{cve_id}%", cve_id)
                )
                rows = cursor.fetchall()
                
                if rows:
                    logger.info(f"在LLM生成的POC中找到 {len(rows)} 个与 {cve_id} 相关的POC")
                    result['has_poc'] = True
                    result['poc_count'] += len(rows)
                    
                    if 'llm' not in result['poc_sources']:
                        result['poc_sources'].append('llm')
                    
                    # 添加POC详情
                    for row in rows:
                        row_dict = dict(row)
                        result['poc_details'].append({
                            'id': f"LLM-{row_dict.get('CVE_ID')}",
                            'title': row_dict.get('Title'),
                            'source': 'llm',
                            'date': row_dict.get('Date')
                        })
            else:
                logger.debug("POC数据库中不存在llm_poc表，跳过LLM POC查询")
                
        except sqlite3.Error as e:
            # 降级为debug级别而不是error
            logger.debug(f"查询POC数据库时出错: {e}")
        finally:
            # 关闭数据库连接
            if 'conn' in locals() and conn:
                conn.close()
        
        # 2. 检查Neo4j中是否存在POC节点
        if self.graph_store.is_connected():
            try:
                # 首先检查POC标签和HAS_POC关系是否存在
                check_exists_query = """
                CALL db.labels() YIELD label
                WITH collect(label) AS labels
                CALL db.relationshipTypes() YIELD relationshipType
                WITH labels, collect(relationshipType) AS relTypes
                RETURN 
                    'POC' IN labels AS pocLabelExists,
                    'HAS_POC' IN relTypes AS hasPocRelExists
                """
                
                with self.graph_store.driver.session() as session:
                    check_results = session.run(check_exists_query).single()
                    
                    # 只有当标签和关系类型都存在时才执行主查询
                    if check_results and check_results.get('pocLabelExists') and check_results.get('hasPocRelExists'):
                        # 查询Neo4j中的POC节点
                        cypher_query = """
                        MATCH (v:Vulnerability {id: $cve_id})-[:HAS_POC]->(p:POC)
                        RETURN p.id AS id, p.title AS title, p.source AS source, p.url AS url
                        """
                        
                        neo4j_results = session.run(cypher_query, cve_id=cve_id).data()
                        
                        if neo4j_results:
                            logger.info(f"在Neo4j中找到 {len(neo4j_results)} 个与 {cve_id} 相关的POC")
                            result['has_poc'] = True
                            
                            # 添加POC详情，避免重复
                            existing_ids = set(item['id'] for item in result['poc_details'])
                            
                            for poc in neo4j_results:
                                if poc.get('id') not in existing_ids:
                                    result['poc_count'] += 1
                                    result['poc_details'].append(poc)
                                    
                                    # 添加来源
                                    source = poc.get('source', 'unknown')
                                    if source not in result['poc_sources']:
                                        result['poc_sources'].append(source)
                    else:
                        logger.debug("Neo4j数据库中不存在POC标签或HAS_POC关系，跳过相关查询")
            
            except Exception as e:
                # 将错误降级为debug级别，不再显示为错误
                logger.debug(f"查询Neo4j数据库时出错: {e}")
        else:
            logger.debug("Neo4j数据库未连接，跳过图数据库POC检查")
        
        return result
    
    def _calculate_ald_cdf(self, t: float, mu: float = 0.0, scale: float = 1.0, kappa: float = 1.0) -> float:
        """
        计算非对称拉普拉斯分布(Asymmetric Laplace Distribution, ALD)的累积分布函数(CDF)
        
        Args:
            t: 时间变量，通常是已经过去的时间（天）
            mu: 位置参数，表示分布的中心点
            scale: 尺度参数，控制分布的分散程度
            kappa: 不对称参数，控制分布的偏斜程度
            
        Returns:
            float: ALD的CDF值，范围在[0,1]内
        """
        # 确保参数合法
        if scale <= 0:
            scale = 1.0
        if kappa <= 0:
            kappa = 1.0
            
        # 计算CDF
        if t <= mu:
            # 左半部分
            return 0.5 * math.exp((t - mu) * kappa / scale)
        else:
            # 右半部分
            return 1 - 0.5 * math.exp(-(t - mu) / (scale * kappa))
    
    def _calculate_time_based_maturity(self, 
                                     cve_id: str, 
                                     published_date: Optional[str] = None,
                                     has_poc: bool = False) -> float:
        """
        基于时间和POC状态计算漏洞利用成熟度
        
        Args:
            cve_id: CVE ID
            published_date: 漏洞发布日期（ISO格式）
            has_poc: 是否存在POC
            
        Returns:
            float: 动态利用成熟度因子，范围在[0.5,1.0]之间
        """
        # 默认成熟度范围参数
        e_min = 0.5  # 最小成熟度（新发布漏洞）
        e_max = 1.0  # 最大成熟度（已被充分利用的漏洞）
        
        # 如果存在POC，则直接返回最高成熟度
        if has_poc:
            return e_max
            
        # 如果没有发布日期，使用保守估计
        if not published_date:
            logger.warning(f"无法获取 {cve_id} 的发布日期，使用默认成熟度 0.8")
            return 0.8
            
        try:
            # 解析日期（支持Z结尾的ISO格式和普通ISO格式）
            if published_date.endswith('Z'):
                published_date = published_date[:-1]  # 移除Z后缀
            pub_date = datetime.fromisoformat(published_date)
            
            # 计算漏洞发布至今的天数
            days_since_publication = (datetime.now() - pub_date).days
            
            # 防止负值（极少数情况下可能发生）
            if days_since_publication < 0:
                days_since_publication = 0
                
            # ALD参数设置 - 可根据实际需求调整
            mu = 0.0  # 位置参数，设为0表示从发布日开始计算
            scale = 30.0  # 尺度参数，表示30天为特征尺度
            kappa = 1.5  # 不对称参数，大于1表示右偏，利用成熟度随时间快速增长后逐渐趋于稳定
            
            # 计算ALD的CDF
            ald_value = self._calculate_ald_cdf(days_since_publication, mu, scale, kappa)
            
            # 映射到成熟度范围
            maturity = e_min + (e_max - e_min) * ald_value
            
            logger.debug(f"{cve_id} 发布已 {days_since_publication} 天，动态成熟度为 {maturity:.4f}")
            return maturity
            
        except Exception as e:
            logger.error(f"计算 {cve_id} 的动态成熟度时出错: {e}")
            # 出错时使用中等偏高的成熟度作为后备值
            return 0.8
    
    def assess_cve_risk(self, cve_id: str) -> Dict[str, Any]:
        """
        评估CVE的综合风险
        
        Args:
            cve_id: CVE ID
            
        Returns:
            Dict: 风险评估结果
        """
        logger.info(f"开始评估 {cve_id} 的综合风险")
        
        # 获取各维度评分
        cvss_result = self.get_cvss_score(cve_id)
        epss_result = self.get_epss_score(cve_id)
        poc_result = self.check_poc_existence(cve_id)
        
        # 提取核心分数
        cvss_score = cvss_result.get('cvss_score')
        epss_score = epss_result.get('epss_score')
        has_poc = poc_result.get('has_poc', False)
        
        # 计算风险等级，默认值
        risk_level = "未知"
        risk_zone = "未知"
        cvss_e = None
        calculated_score = None
        
        # 处理缺失的分数
        if cvss_score is None:
            logger.warning(f"{cve_id} 缺少CVSS评分数据")
        else:
            # 转换为浮点数
            cvss_score = float(cvss_score)
            
            # 获取发布日期（从CVSS结果中提取）
            published_date = None
            if 'published_date' in cvss_result:
                published_date = cvss_result.get('published_date')
            
            # 计算动态利用成熟度因子
            e_s = self._calculate_time_based_maturity(cve_id, published_date, has_poc)
            
            # 计算CVSS_E (考虑动态成熟度)
            cvss_e = cvss_score * e_s
            
            # 分区计算
            if epss_score is not None:
                epss_score = float(epss_score)
                
                # 根据方案的四个象限区域计算
                if cvss_e >= 7 and epss_score >= 0.6:
                    risk_level = "严重"
                    risk_zone = "右上"
                elif cvss_e >= 7 and epss_score < 0.6:
                    risk_level = "高"
                    risk_zone = "右下"
                elif cvss_e < 7 and epss_score >= 0.6:
                    risk_level = "中"
                    risk_zone = "左上"
                else:  # cvss_e < 7 and epss_score < 0.6
                    risk_level = "低"
                    risk_zone = "左下"
                
                # 计算综合风险评分 (可选的加权平均)
                calculated_score = (0.6 * cvss_e) + (0.4 * epss_score * 10)  # 将EPSS缩放到0-10
            else:
                # 只有CVSS评分时的简化风险等级
                if cvss_e >= 9:
                    risk_level = "严重"
                elif cvss_e >= 7:
                    risk_level = "高"
                elif cvss_e >= 4:
                    risk_level = "中"
                else:
                    risk_level = "低"
                
                calculated_score = cvss_e
                risk_zone = "右下" if cvss_e >= 7 else "左下"  # 假设低EPSS
                
        # 整合结果
        result = {
            'cve_id': cve_id,
            'assessment_time': datetime.now().isoformat(),
            
            # 各维度数据
            'cvss_score': cvss_score,
            'cvss_severity': cvss_result.get('cvss_severity'),
            'cvss_vector': cvss_result.get('cvss_vector'),
            'cvss_version': cvss_result.get('cvss_version'),
            'published_date': cvss_result.get('published_date'),
            
            'epss_score': epss_score,
            'epss_percentile': epss_result.get('epss_percentile'),
            
            'has_poc': has_poc,
            'poc_count': poc_result.get('poc_count', 0),
            'poc_sources': poc_result.get('poc_sources', []),
            
            # 计算结果
            'maturity_factor': e_s if 'e_s' in locals() else None,  # 动态成熟度因子
            'cvss_e': cvss_e,  # 考虑动态成熟度的CVSS
            'risk_score': calculated_score,  # 计算的风险评分
            'risk_level': risk_level,  # 风险等级
            'risk_zone': risk_zone,  # 风险区域
            
            # 处置建议
            'recommendation': self._get_recommendation(risk_level)
        }
        
        logger.info(f"{cve_id} 风险评估完成: 风险等级={risk_level}, 风险评分={calculated_score}")
        return result
    
    def _get_recommendation(self, risk_level: str) -> str:
        """根据风险等级生成处置建议"""
        recommendations = {
            "严重": "立即修复此漏洞，它具有高技术风险且被广泛利用",
            "高": "安排修复计划，此漏洞具有重要技术影响",
            "中": "持续关注此漏洞的利用情况变化，适时安排修复",
            "低": "可以在常规维护中处理此漏洞",
            "未知": "请获取更多信息后再评估此漏洞"
        }
        return recommendations.get(risk_level, "请进一步评估此漏洞")
    
    def close(self):
        """关闭连接和资源"""
        if hasattr(self, 'graph_store') and self.graph_store:
            self.graph_store.close()
            logger.info("已关闭图数据库连接")
