#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""服务类，用于封装 POC 收集、生成和存储的逻辑"""

import os
import sys
import sqlite3
import logging
import random
import time
import json
import traceback
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

# 动态添加项目根目录，以便在不同位置运行时能找到模块
# 这在使用 main.py 调用时可能不是必需的，但增加健壮性
try:
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
except NameError: # __file__ might not be defined in some contexts
    # Fallback or log a warning if necessary
    pass

# 导入依赖的服务和工具
from src.data_collection.services.playwright_exploit_db_collector import PlaywrightExploitDBCollector
from src.data_collection.services.generetors.poc_llmgenerator import PocGenerator

# 检查 Neo4j 依赖
try:
    from neo4j import GraphDatabase, Driver
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    # 记录一次警告即可
    logging.getLogger(__name__).warning("Neo4j 驱动未安装，Neo4j 相关功能将不可用。")

logger = logging.getLogger(__name__)

class PocCollectionService:
    """处理单个 CVE 的 POC 收集、生成和存储流程"""

    def __init__(self, args: Any):
        """
        初始化服务

        参数:
            args: 包含配置的对象，通常是 main.py 解析后的命令行参数 Namespace
        """
        logger.info("初始化 PocCollectionService...")
        self.args = args
        self.verbose = getattr(args, 'verbose', False)
        self.poc_db_path = getattr(args, 'poc_db_path', '/home/<USER>/data/poc/exploitDetailed.db')
        self.json_output_dir = getattr(args, 'poc_json_output_dir', '/home/<USER>/data/poc/json')
        self.save_to_json = getattr(args, 'save_poc_to_json', False)
        self.save_to_neo4j = getattr(args, 'save_poc_to_neo4j', False)
        self.use_llm = getattr(args, 'use_llm_for_poc', False)
        self.force_fetch = getattr(args, 'force_poc_fetch', False)
        self.timeout = getattr(args, 'poc_timeout', 60)
        self.delay = getattr(args, 'poc_delay', 0)
        self.use_nvd_api = getattr(args, 'use_nvd_api', True) # 从 collect_poc 继承
        self.unique_llm_poc = getattr(args, 'unique_llm_poc', True) # 控制LLM生成的POC是否使用唯一标识

        # 配置代理
        self.proxies = self._configure_proxies()
        self.playwright_proxies = {'server': self.proxies.get('http') or self.proxies.get('https')} if self.proxies else None

        # 初始化依赖的收集器和生成器
        self.playwright_collector = PlaywrightExploitDBCollector(
            proxies=self.playwright_proxies,
            timeout=self.timeout * 1000, # Playwright 需要毫秒
            verbose=self.verbose
        )
        # LLM 生成器可能需要 API Key 等，假设它从环境变量或默认配置加载
        self.llm_generator = PocGenerator()

        # 确保 POC 数据库存在
        self._ensure_poc_sqlite_db()
        
        # 初始化 Neo4j 驱动 (如果需要且可用)
        self.neo4j_driver: Optional[Driver] = None
        if self.save_to_neo4j and NEO4J_AVAILABLE:
            self._initialize_neo4j_driver()

        logger.info("PocCollectionService 初始化完成")

    def _configure_proxies(self) -> Dict:
        """根据参数配置代理字典"""
        proxies = {}
        http_proxy = getattr(self.args, 'proxy', None)
        socks_proxy = getattr(self.args, 'socks_proxy', None)
        
        if http_proxy:
            proxies['http'] = http_proxy
            proxies['https'] = http_proxy # requests 通常 http 和 https 用同一个
            logger.info(f"使用 HTTP/HTTPS 代理: {http_proxy}")
        if socks_proxy:
            # 注意: requests 需要 pip install requests[socks]
            # Playwright 的代理格式可能不支持 socks，这里主要为 LLM/NVD 请求准备
            proxies['http'] = socks_proxy 
            proxies['https'] = socks_proxy
            logger.info(f"使用 SOCKS 代理: {socks_proxy}")
        return proxies

    def _initialize_neo4j_driver(self): 
        """初始化 Neo4j 驱动程序""" 
        if not NEO4J_AVAILABLE: 
            logger.warning("尝试初始化 Neo4j 驱动，但依赖不可用") 
            return 
        try: 
            uri = os.getenv("NEO4J_URI", "bolt://localhost:7687") 
            username = os.getenv("NEO4J_USER", "neo4j") 
            password = os.getenv("NEO4J_PASSWORD", "password") 
            self.neo4j_driver = GraphDatabase.driver(uri, auth=(username, password)) 
            # 测试连接 (可选) 
            self.neo4j_driver.verify_connectivity() 
            logger.info(f"Neo4j 驱动已初始化并连接到 {uri}") 
        except Exception as e: 
            logger.error(f"初始化 Neo4j 驱动失败: {e}") 
            self.neo4j_driver = None # 标记为不可用 
            self.save_to_neo4j = False # 禁用保存功能

    def close_neo4j_driver(self): 
        """关闭 Neo4j 驱动程序""" 
        if self.neo4j_driver: 
            try: 
                self.neo4j_driver.close() 
                logger.info("Neo4j 驱动已关闭") 
            except Exception as e: 
                logger.error(f"关闭 Neo4j 驱动时出错: {e}") 
            self.neo4j_driver = None

    # --- POC 处理核心逻辑 ---
    def _try_get_poc_from_llm_sqlite(self, cve_id: str) -> List[Dict]:
        """尝试从 SQLite 获取 LLM 生成的 POC"""
        conn = None
        try:
            conn = sqlite3.connect(self.poc_db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM llm_poc WHERE CVE_ID LIKE ? OR CVE_ID = ?", (f"%{cve_id}%", cve_id))
            
            rows = cursor.fetchall()
            if rows:
                logger.info(f"从 SQLite LLM 缓存找到 {len(rows)} 个与 {cve_id} 相关的 POC")
                
                result = []
                for row in rows:
                    # 将 sqlite3.Row 转换为字典
                    poc_data = dict(row)
                    # 规范化字段名称，保持一致性
                    poc_data['cve_id'] = poc_data.get('CVE_ID')
                    poc_data['poc_code'] = poc_data.get('POC')
                    poc_data['edb_id'] = f"LLM-{poc_data['cve_id']}"  # 移除时间戳部分，不再使用唯一标识
                    poc_data['source'] = 'llm'
                    poc_data['title'] = poc_data.get('Title')
                    poc_data['date'] = poc_data.get('Date')
                    poc_data['author'] = poc_data.get('Author')
                    poc_data['type'] = poc_data.get('Type')
                    poc_data['platform'] = poc_data.get('Platform')
                    result.append(poc_data)
                
                return result
            else:
                logger.debug(f"在 SQLite LLM 缓存中未找到 {cve_id} 的 POC")
                return []
        except sqlite3.Error as e:
            logger.error(f"从 SQLite LLM 表中获取 POC 时出错: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def process_cve(self, cve_id: str) -> bool:
        """
        处理单个 CVE ID，获取/生成 POC 并保存。

        参数:
            cve_id: CVE ID

        返回:
            是否至少成功处理了一个 POC
        """
        logger.info(f"开始处理 CVE: {cve_id}")
        overall_success = False
        all_poc_data: List[Dict] = []  # 存储本次处理的所有POC
        
        # 1. 先从缓存中获取所有现有POC
        cache_pocs_from_exploitdb = self._try_get_poc_from_sqlite(cve_id) 
        cache_pocs_from_llm = self._try_get_poc_from_llm_sqlite(cve_id)
        
        # 记录已知的POC数量
        if cache_pocs_from_exploitdb:
            logger.info(f"从ExploitDB缓存中获取到 {len(cache_pocs_from_exploitdb)} 个POC")
            all_poc_data.extend(cache_pocs_from_exploitdb)
            overall_success = True
            
        if cache_pocs_from_llm:
            logger.info(f"从LLM缓存中获取到 {len(cache_pocs_from_llm)} 个POC")
            all_poc_data.extend(cache_pocs_from_llm)
            overall_success = True
        
        # 2. 如果强制获取或没有从缓存中获取到POC，尝试从网络获取
        if self.force_fetch or not all_poc_data:
            logger.info(f"尝试使用Playwright从ExploitDB获取 {cve_id} 的所有POC")
            
            # 尝试使用Playwright从ExploitDB获取
            exploitdb_pocs = self.playwright_collector.fetch_poc_by_cve(cve_id)
            
            if exploitdb_pocs:
                logger.info(f"Playwright找到 {len(exploitdb_pocs)} 个POC")
                
                new_pocs_count = 0
                for poc in exploitdb_pocs:
                    if poc and poc.get('edb_id'):  # 简单验证
                        # 检查是否已经存在相同的POC
                        is_duplicate = False
                        for existing_poc in all_poc_data:
                            if existing_poc.get('edb_id') == poc.get('edb_id'):
                                is_duplicate = True
                                break
                        
                        if not is_duplicate:
                            all_poc_data.append(poc)
                            new_pocs_count += 1
                            
                            # 保存到SQLite
                            self._save_poc_to_sqlite(poc)
                            
                            # 如果启用，保存到JSON
                            if self.save_to_json:
                                self._save_poc_to_json(poc, "playwright_exploitdb")
                            
                            overall_success = True
                        else:
                            logger.debug(f"跳过重复POC: EDB-ID {poc.get('edb_id')}")
                    else:
                        logger.warning(f"从Playwright结果中过滤掉无效的POC数据项: {poc}")
                
                logger.info(f"新增了 {new_pocs_count} 个独特的POC")
            else:
                logger.warning(f"使用Playwright未能从ExploitDB获取到 {cve_id} 的任何POC")
        
        # 3. 如果启用LLM且没有获取到任何POC，尝试使用LLM生成
        if not all_poc_data and self.use_llm:
            logger.info(f"尝试使用LLM为 {cve_id} 生成POC")
            try:
                # 获取漏洞详情
                cve_details = None
                if self.use_nvd_api:
                    logger.info(f"从NVD API获取 {cve_id} 的详细信息...")
                    # 从NVD API获取详情
                
                # 生成POC
                llm_poc = self._generate_poc_with_llm(cve_id, cve_details)
                
                if llm_poc:
                    logger.info(f"LLM成功为 {cve_id} 生成POC")
                    
                    # 构建POC数据结构
                    timestamp = int(datetime.now().timestamp())
                    llm_poc_data = {
                        'cve_id': cve_id,
                        'edb_id': f"LLM-{cve_id}-{timestamp}",  # 添加时间戳确保唯一
                        'title': f"LLM Generated POC for {cve_id}",
                        'poc_code': llm_poc,
                        'date': datetime.now().strftime('%Y-%m-%d'),
                        'author': 'LLM Generator',
                        'type': 'LLM Generated',
                        'platform': 'Multiple',
                        'source': 'llm'
                    }
                    
                    # 保存LLM生成的POC
                    self._save_llm_poc_to_sqlite(llm_poc_data)
                    
                    if self.save_to_json:
                        self._save_poc_to_json(llm_poc_data, "llm_generated")
                    
                    all_poc_data.append(llm_poc_data)
                    overall_success = True
                else:
                    logger.warning(f"LLM未能为 {cve_id} 生成POC")
            except Exception as e:
                logger.error(f"使用LLM生成 {cve_id} 的POC时出错: {e}", exc_info=True)
        
        # 4. 如果获取到了POC并需要保存到Neo4j，执行保存
        if all_poc_data and self.save_to_neo4j:
            logger.info(f"将 {len(all_poc_data)} 个 {cve_id} 的POC保存到Neo4j")
            if self._save_exploitdb_poc_list_to_neo4j(all_poc_data):
                logger.info(f"成功将 {cve_id} 的POC保存到Neo4j")
            else:
                logger.error(f"保存 {cve_id} 的POC到Neo4j时出错")
        
        # 处理延迟
        if self.delay > 0:
            delay_time = self.delay * random.uniform(0.8, 1.2)
            logger.info(f"添加随机延迟 {delay_time:.2f} 秒")
            time.sleep(delay_time)
        
        # 返回是否成功处理
        if overall_success:
            logger.info(f"成功处理 {cve_id} 的POC，共 {len(all_poc_data)} 个")
        else:
            logger.warning(f"未能成功处理 {cve_id} 的任何POC")
        
        return overall_success

    # --- 内部辅助方法 (迁移自 collect_poc.py) ---

    def _ensure_poc_sqlite_db(self) -> None:
        """确保 POC SQLite 数据库和表存在"""
        try:
            db_dir = os.path.dirname(self.poc_db_path)
            if db_dir:
                os.makedirs(db_dir, exist_ok=True)
            
            conn = sqlite3.connect(self.poc_db_path)
            cursor = conn.cursor()
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS exploitDetail (
                    EDB_ID INTEGER PRIMARY KEY,
                    Date TEXT, V INTEGER, Title TEXT, Type TEXT,
                    Platform TEXT, Author TEXT, CVE TEXT, POC TEXT
                )''')
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS llm_poc (
                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                    CVE_ID TEXT UNIQUE, Date TEXT, Title TEXT, Type TEXT,
                    Platform TEXT, Author TEXT, POC TEXT,
                    Severity TEXT, Score REAL, Description TEXT
                )''')
            conn.commit()
            conn.close()
            logger.debug(f"已确保 POC 数据库 {self.poc_db_path} 和表存在")
        except sqlite3.Error as e:
            logger.error(f"初始化 POC SQLite 数据库 {self.poc_db_path} 出错: {e}")
            # 也许应该抛出异常?

    def _save_poc_to_sqlite(self, poc_data: Dict) -> bool:
        """将单个 ExploitDB POC 保存到 SQLite 的 exploitDetail 表"""
        if not poc_data or not poc_data.get('edb_id') or not poc_data.get('cve_id'):
            logger.warning("尝试保存无效或不完整的 ExploitDB POC 数据到 SQLite")
            return False
            
        edb_id = poc_data['edb_id']
        conn = None
        try:
            conn = sqlite3.connect(self.poc_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT EDB_ID FROM exploitDetail WHERE EDB_ID = ?", (edb_id,))
            if cursor.fetchone():
                logger.debug(f"更新 SQLite 中已存在的 EDB-ID {edb_id}")
                cursor.execute("""
                    UPDATE exploitDetail SET Date = ?, Title = ?, Type = ?, 
                    Platform = ?, Author = ?, CVE = ?, POC = ? WHERE EDB_ID = ?
                """, (
                    poc_data.get('date', ''), poc_data.get('title', ''), poc_data.get('type', ''),
                    poc_data.get('platform', ''), poc_data.get('author', ''), poc_data.get('cve_id', ''),
                    poc_data.get('poc_code', ''), edb_id
                ))
            else:
                logger.debug(f"插入新的 EDB-ID {edb_id} 到 SQLite")
                cursor.execute("""
                    INSERT INTO exploitDetail (EDB_ID, Date, V, Title, Type, Platform, Author, CVE, POC)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    edb_id, poc_data.get('date', ''), 1, poc_data.get('title', ''), poc_data.get('type', ''),
                    poc_data.get('platform', ''), poc_data.get('author', ''), poc_data.get('cve_id', ''),
                    poc_data.get('poc_code', '')
                ))
            conn.commit()
            return True
        except sqlite3.Error as e:
            logger.error(f"保存 EDB POC (ID: {edb_id}) 到 SQLite {self.poc_db_path} 时出错: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def _try_get_poc_from_sqlite(self, cve_id: str) -> List[Dict]:
        """尝试从SQLite获取POC"""
        conn = None
        try:
            conn = sqlite3.connect(self.poc_db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 修改查询，移除LIMIT 1，返回所有匹配的POC
            cursor.execute("SELECT * FROM exploitDetail WHERE CVE LIKE ? OR CVE = ?", (f"%{cve_id}%", cve_id))
            
            rows = cursor.fetchall()
            if rows:
                logger.info(f"从SQLite缓存找到 {len(rows)} 个与 {cve_id} 相关的POC")
                
                result = []
                for row in rows:
                    # 将sqlite3.Row转换为字典
                    poc_data = dict(row)
                    # 规范化字段名称
                    poc_data['cve_id'] = cve_id
                    poc_data['edb_id'] = poc_data.get('EDB_ID')
                    poc_data['poc_code'] = poc_data.get('POC')
                    poc_data['title'] = poc_data.get('Title')
                    poc_data['date'] = poc_data.get('Date')
                    poc_data['author'] = poc_data.get('Author')
                    poc_data['type'] = poc_data.get('Type')
                    poc_data['platform'] = poc_data.get('Platform')
                    poc_data['source'] = 'exploitdb'
                    result.append(poc_data)
                    
                return result
            else:
                logger.debug(f"在SQLite缓存中未找到 {cve_id} 的POC")
                return []
        except sqlite3.Error as e:
            logger.error(f"从SQLite获取POC时出错: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def _save_llm_poc_to_sqlite(self, poc_data: Dict) -> bool:
        """将 LLM 生成的 POC 保存到 SQLite 的 llm_poc 表"""
        if not poc_data or not poc_data.get('cve_id'):
            logger.warning("尝试保存无效或不完整的 LLM POC 数据到 SQLite")
            return False
        
        cve_id = poc_data['cve_id']
        conn = None
        try:
            conn = sqlite3.connect(self.poc_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT CVE_ID FROM llm_poc WHERE CVE_ID = ?", (cve_id,))
            if cursor.fetchone():
                logger.debug(f"更新 SQLite 中已存在的 LLM POC for {cve_id}")
                cursor.execute("""
                    UPDATE llm_poc SET Date = ?, Title = ?, Type = ?, Platform = ?, Author = ?, 
                    POC = ?, Severity = ?, Score = ?, Description = ? WHERE CVE_ID = ?
                """, (
                    poc_data.get('date', datetime.now().isoformat()), poc_data.get('title', ''), poc_data.get('type', 'Generated'),
                    poc_data.get('platform', ''), poc_data.get('author', 'LLM'), poc_data.get('poc_code', ''),
                    poc_data.get('severity', ''), poc_data.get('score', 0.0), poc_data.get('description', ''),
                    cve_id
                ))
            else:
                logger.debug(f"插入新的 LLM POC for {cve_id} 到 SQLite")
                cursor.execute("""
                    INSERT INTO llm_poc (CVE_ID, Date, Title, Type, Platform, Author, POC, Severity, Score, Description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    cve_id, poc_data.get('date', datetime.now().isoformat()), poc_data.get('title', ''), poc_data.get('type', 'Generated'),
                    poc_data.get('platform', ''), poc_data.get('author', 'LLM'), poc_data.get('poc_code', ''),
                    poc_data.get('severity', ''), poc_data.get('score', 0.0), poc_data.get('description', '')
                ))
            conn.commit()
            return True
        except sqlite3.Error as e:
            logger.error(f"保存 LLM POC ({cve_id}) 到 SQLite {self.poc_db_path} 时出错: {e}")
            return False
        finally:
            if conn:
                conn.close()

    def _get_vulnerability_details(self, cve_id: str) -> Dict[str, Any]:
        """
        从漏洞数据库获取CVE详细信息
        
        参数:
            cve_id: CVE ID
            
        返回:
            包含CVE详细信息的字典
        """
        try:
            conn = sqlite3.connect('/home/<USER>/data/vulnerabilities.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 查询漏洞基本信息
            cursor.execute("""
                SELECT vuln_id, title, description, published_date, last_modified_date, 
                       severity, status, cvss_v3, cvss_v2
                FROM vulnerabilities
                WHERE vuln_id = ?
            """, (cve_id,))
            
            row = cursor.fetchone()
            if not row:
                logger.warning(f"在漏洞数据库中未找到 {cve_id} 的详细信息")
                return {}
                
            vulnerability = dict(row)
            
            # 查询参考链接
            cursor.execute("""
                SELECT r.url, r.source, r.type, r.tags
                FROM references r
                JOIN vulnerability_reference vr ON r.id = vr.reference_id
                JOIN vulnerabilities v ON vr.vulnerability_id = v.id
                WHERE v.vuln_id = ?
            """, (cve_id,))
            
            references = [dict(ref) for ref in cursor.fetchall()]
            vulnerability['references'] = references
            
            # 查询受影响软件包
            cursor.execute("""
                SELECT p.name, p.ecosystem, p.platform, p.versions, p.affected_versions, p.fixed_versions
                FROM packages p
                JOIN vulnerability_package vp ON p.id = vp.package_id
                JOIN vulnerabilities v ON vp.vulnerability_id = v.id
                WHERE v.vuln_id = ?
            """, (cve_id,))
            
            affected_packages = [dict(pkg) for pkg in cursor.fetchall()]
            vulnerability['affected_packages'] = affected_packages
            
            logger.info(f"成功从漏洞数据库获取 {cve_id} 的详细信息")
            return vulnerability
            
        except sqlite3.Error as e:
            logger.error(f"从漏洞数据库获取 {cve_id} 详细信息时出错: {e}")
            return {}
        finally:
            if 'conn' in locals():
                conn.close()

    def _save_exploitdb_poc_list_to_neo4j(self, exploitdb_poc_list: List[Dict]) -> bool:
        """将一批 ExploitDB POC 数据批量保存到 Neo4j"""
        if not self.neo4j_driver or not NEO4J_AVAILABLE:
            logger.error("Neo4j 驱动不可用，无法保存")
            return False
        if not exploitdb_poc_list:
            return True # 没有数据无需保存
            
        cve_id_example = exploitdb_poc_list[0].get('cve_id', exploitdb_poc_list[0].get('CVE_ID', '未知CVE'))
        logger.info(f"批量将 {len(exploitdb_poc_list)} 个 {cve_id_example} 的 POC 保存到 Neo4j")
        
        try:
            # 第一步：保存基本的漏洞和POC信息
            self._save_basic_poc_info_to_neo4j(exploitdb_poc_list)
            
            # 第二步：处理参考链接和软件包关系（如果有相关数据）
            cve_references = {}
            cve_packages = {}
            
            # 收集每个CVE的参考链接和软件包
            for poc_data in exploitdb_poc_list:
                cve_id = poc_data.get('cve_id') or poc_data.get('CVE_ID') or poc_data.get('CVE')
                if not cve_id:
                    continue
                    
                # 获取CVE详细信息（如果缓存中没有）
                vuln_details = self._get_vulnerability_details(cve_id)
                if not vuln_details:
                    continue
                
                # 收集参考链接
                if 'references' in vuln_details and vuln_details['references']:
                    refs = [ref for ref in vuln_details['references'] if ref.get('url')]
                    if refs:
                        cve_references[cve_id] = refs
                
                # 收集受影响软件包
                if 'affected_packages' in vuln_details and vuln_details['affected_packages']:
                    pkgs = [pkg for pkg in vuln_details['affected_packages'] if pkg.get('name')]
                    if pkgs:
                        cve_packages[cve_id] = pkgs
            
            # 保存参考链接
            if cve_references:
                self._save_references_to_neo4j(cve_references)
            
            # 保存软件包关系
            if cve_packages:
                self._save_packages_to_neo4j(cve_packages)
            
            return True
        except Exception as e:
            logger.error(f"批量保存到 Neo4j 时出错 ({cve_id_example}): {e}")
            logger.debug(f"异常详情: {traceback.format_exc()}")
            return False
            
    def _save_basic_poc_info_to_neo4j(self, exploitdb_poc_list: List[Dict]) -> bool:
        """保存基本的漏洞和POC信息到Neo4j"""
        if not self.neo4j_driver:
            return False
            
        try:
            with self.neo4j_driver.session() as session:
                query = """
                UNWIND $poc_batch AS poc
                
                // 创建漏洞节点
                MERGE (v:Vulnerability {cve_id: poc.cve_id})
                ON CREATE SET 
                    v.title = poc.vuln_title,
                    v.description = poc.vuln_description,
                    v.severity = poc.vuln_severity,
                    v.published_date = poc.vuln_published_date,
                    v.last_modified_date = poc.vuln_last_modified_date,
                    v.status = poc.vuln_status,
                    v.cvss_v3 = poc.vuln_cvss_v3,
                    v.cvss_v2 = poc.vuln_cvss_v2
                ON MATCH SET
                    v.title = CASE WHEN poc.vuln_title IS NOT NULL THEN poc.vuln_title ELSE v.title END,
                    v.description = CASE WHEN poc.vuln_description IS NOT NULL THEN poc.vuln_description ELSE v.description END,
                    v.severity = CASE WHEN poc.vuln_severity IS NOT NULL THEN poc.vuln_severity ELSE v.severity END
                
                // 创建并关联漏洞利用节点
                MERGE (e:Exploit {source: poc.source, edb_id: poc.edb_id})
                ON CREATE SET 
                    e.code = poc.poc_code, 
                    e.author = poc.author, 
                    e.date = poc.date, 
                    e.title = poc.title, 
                    e.type = poc.type, 
                    e.platform = poc.platform, 
                    e.cve_id = poc.cve_id
                ON MATCH SET 
                    e.code = poc.poc_code, 
                    e.title = poc.title, 
                    e.date = poc.date
                
                MERGE (v)-[r:HAS_POC]->(e)
                
                RETURN count(*) AS processed_count
                """
                
                poc_batch_params = []
                
                # 获取每个POC相关的CVE详细信息
                cve_detail_cache = {}  # 缓存已查询的CVE信息
                
                for poc_data in exploitdb_poc_list:
                    # 标准化字段名，确保无论数据源，字段名总是一致的
                    cve_id = poc_data.get('cve_id') or poc_data.get('CVE_ID') or poc_data.get('CVE')
                    edb_id = poc_data.get('edb_id') or poc_data.get('EDB_ID') or f"LLM-{cve_id}"
                    poc_code = poc_data.get('poc_code') or poc_data.get('POC')
                    
                    if not cve_id or not edb_id:
                        logger.warning(f"跳过缺少 cve_id 或 edb_id 的 POC 数据: {edb_id}")
                        continue
                        
                    source = poc_data.get('source', 'exploitdb')  # 默认为exploitdb，LLM生成的会指定为llm
                    
                    # 获取CVE详细信息（如果缓存中没有）
                    if cve_id not in cve_detail_cache:
                        cve_detail_cache[cve_id] = self._get_vulnerability_details(cve_id)
                    
                    vuln_details = cve_detail_cache[cve_id]
                    
                    # 添加日志记录，查看获取到的漏洞详情
                    if vuln_details:
                        logger.info(f"从数据库获取到 {cve_id} 的漏洞详情: 标题: {vuln_details.get('title', '无')}，严重程度: {vuln_details.get('severity', '无')}")
                        if 'references' in vuln_details:
                            logger.info(f"获取到 {len(vuln_details['references'])} 个参考链接")
                        if 'affected_packages' in vuln_details:
                            logger.info(f"获取到 {len(vuln_details['affected_packages'])} 个受影响的包")
                    else:
                        logger.warning(f"未能从数据库获取到 {cve_id} 的漏洞详情")
                    
                    # 处理从本地数据库中获取的CVE详细信息
                    param = {
                        'cve_id': cve_id,
                        'edb_id': str(edb_id),
                        'title': poc_data.get('title', ''),
                        'poc_code': poc_code,
                        'author': poc_data.get('author', ''),
                        'date': poc_data.get('date', ''),
                        'type': poc_data.get('type', ''),
                        'platform': poc_data.get('platform', ''),
                        'source': source,
                        
                        # 漏洞详细信息
                        'vuln_title': vuln_details.get('title'),
                        'vuln_description': vuln_details.get('description'),
                        'vuln_severity': vuln_details.get('severity'),
                        'vuln_published_date': vuln_details.get('published_date'),
                        'vuln_last_modified_date': vuln_details.get('last_modified_date'),
                        'vuln_status': vuln_details.get('status')
                    }
                    
                    # 处理复杂的JSON对象，Neo4j不能直接存储
                    if 'cvss_v3' in vuln_details and vuln_details['cvss_v3']:
                        try:
                            param['vuln_cvss_v3'] = json.dumps(vuln_details['cvss_v3'])
                        except:
                            param['vuln_cvss_v3'] = str(vuln_details['cvss_v3'])
                            
                    if 'cvss_v2' in vuln_details and vuln_details['cvss_v2']:
                        try:
                            param['vuln_cvss_v2'] = json.dumps(vuln_details['cvss_v2'])
                        except:
                            param['vuln_cvss_v2'] = str(vuln_details['cvss_v2'])
                    
                    # 处理日期格式，确保是字符串
                    if 'published_date' in vuln_details and vuln_details['published_date']:
                        if not isinstance(vuln_details['published_date'], str):
                            param['vuln_published_date'] = str(vuln_details['published_date'])
                            
                    if 'last_modified_date' in vuln_details and vuln_details['last_modified_date']:
                        if not isinstance(vuln_details['last_modified_date'], str):
                            param['vuln_last_modified_date'] = str(vuln_details['last_modified_date'])
                    
                    poc_batch_params.append(param)
                
                if not poc_batch_params:
                    logger.warning(f"没有有效的 POC 数据可以发送到 Neo4j")
                    return False

                result = session.run(query, poc_batch=poc_batch_params)
                summary = result.consume()
                logger.info(
                    f"Neo4j 基本信息保存完成: "
                    f"创建节点={summary.counters.nodes_created}, 创建关系={summary.counters.relationships_created}, "
                    f"设置属性={summary.counters.properties_set}"
                )
                return True
        except Exception as e:
            logger.error(f"保存基本信息到 Neo4j 时出错: {e}")
            return False

    def _save_references_to_neo4j(self, cve_references: Dict[str, List[Dict]]) -> bool:
        """保存参考链接到Neo4j"""
        if not self.neo4j_driver:
            return False
        
        try:
            with self.neo4j_driver.session() as session:
                for cve_id, refs in cve_references.items():
                    for ref in refs:
                        if not ref.get('url'):
                            continue
                            
                        query = """
                        MATCH (v:Vulnerability {cve_id: $cve_id})
                        MERGE (r:Reference {url: $url})
                        ON CREATE SET
                            r.source = $source,
                            r.type = $type,
                            r.tags = $tags
                        MERGE (v)-[:HAS_REFERENCE]->(r)
                        """
                        
                        session.run(query, {
                            'cve_id': cve_id,
                            'url': ref.get('url'),
                            'source': ref.get('source'),
                            'type': ref.get('type'),
                            'tags': ref.get('tags')
                        })
                        
                logger.info(f"成功保存 {len(cve_references)} 个CVE的参考链接到Neo4j")
                return True
        except Exception as e:
            logger.error(f"保存参考链接到Neo4j时出错: {e}")
            return False
            
    def _save_packages_to_neo4j(self, cve_packages: Dict[str, List[Dict]]) -> bool:
        """保存软件包信息到Neo4j"""
        if not self.neo4j_driver:
            return False
        
        try:
            with self.neo4j_driver.session() as session:
                for cve_id, pkgs in cve_packages.items():
                    for pkg in pkgs:
                        if not pkg.get('name'):
                            continue
                            
                        query = """
                        MATCH (v:Vulnerability {cve_id: $cve_id})
                        MERGE (p:Package {name: $name, ecosystem: $ecosystem})
                        ON CREATE SET
                            p.platform = $platform,
                            p.affected_versions = $affected_versions,
                            p.fixed_versions = $fixed_versions
                        MERGE (v)-[:AFFECTS]->(p)
                        """
                        
                        session.run(query, {
                            'cve_id': cve_id,
                            'name': pkg.get('name'),
                            'ecosystem': pkg.get('ecosystem'),
                            'platform': pkg.get('platform'),
                            'affected_versions': json.dumps(pkg.get('affected_versions', [])) if pkg.get('affected_versions') else None,
                            'fixed_versions': json.dumps(pkg.get('fixed_versions', [])) if pkg.get('fixed_versions') else None
                        })
                        
                logger.info(f"成功保存 {len(cve_packages)} 个CVE的软件包信息到Neo4j")
                return True
        except Exception as e:
            logger.error(f"保存软件包信息到Neo4j时出错: {e}")
            return False

    def _save_poc_to_json(self, poc_data: Dict, poc_source: str) -> str:
        """将单个 POC 数据保存到 JSON 文件"""
        if not poc_data:
            logger.warning("尝试保存空的 POC 数据到 JSON")
            return ""
            
        cve_id = poc_data.get('cve_id', 'UNKNOWN_CVE')
        # 使用 poc_source 的第一部分作为子目录 (e.g., 'playwright', 'llm', 'sqlite')
        source_dir = poc_source.split('_')[0] 
        output_dir = os.path.join(self.json_output_dir, source_dir)
        os.makedirs(output_dir, exist_ok=True)

        filename_cve = cve_id.replace('-', '_').replace('/','_')
        edb_id = poc_data.get('edb_id')
        filename_suffix = ""
        if poc_source == "llm":
             filename_suffix = "_LLM"
        elif edb_id:
             filename_suffix = f"_EDB_{edb_id}"
             
        file_path = os.path.join(output_dir, f"{filename_cve}{filename_suffix}.json")

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(poc_data, f, indent=2, ensure_ascii=False)
            logger.info(f"POC ({poc_source}) 数据已保存到 JSON 文件: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"保存 POC 数据到 JSON 文件 {file_path} 时出错: {str(e)}")
            return ""
            
    def _generate_poc_with_llm(self, cve_id: str, cve_details: Optional[Dict] = None) -> Optional[str]:
        """
        使用LLM为指定的CVE生成POC代码
        
        参数:
            cve_id: CVE ID
            cve_details: 可选的CVE详情数据，如果不提供，会尝试从NVD获取
            
        返回:
            生成的POC代码或None
        """
        logger.info(f"使用LLM为 {cve_id} 生成POC")
        
        try:
            # 如果未提供CVE详情且使用NVD API，尝试获取详情
            if not cve_details and self.use_nvd_api:
                logger.info(f"从NVD API获取 {cve_id} 详情")
                cve_details = self.llm_generator.get_cve_details(cve_id)
            
            # 如果依然无法获取详情，创建最小详情集
            if not cve_details:
                logger.warning(f"无法获取 {cve_id} 的详情，将使用最小信息集")
                cve_details = {
                    'id': cve_id,
                    'description': f"Vulnerability details for {cve_id}",
                    'severity': "UNKNOWN",
                    'base_score': 0.0,
                    'references': [],
                    'published': datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                    'last_modified': datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                    'weaknesses': []
                }
            
            # 生成POC代码
            logger.info(f"开始为 {cve_id} 生成POC代码")
            poc_code = self.llm_generator.generate_poc(cve_details)
            
            if poc_code:
                logger.info(f"成功为 {cve_id} 生成POC代码")
                return poc_code
            else:
                logger.error(f"为 {cve_id} 生成POC代码失败")
                return None
        
        except Exception as e:
            logger.error(f"在LLM生成POC过程中发生错误: {e}", exc_info=True)
            return None 