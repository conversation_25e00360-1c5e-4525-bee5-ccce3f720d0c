#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
漏洞数据采集服务模块

负责从多个数据源采集漏洞数据，支持：
1. 多源数据并行采集
2. 增量更新
3. 错误重试
4. 数据持久化
5. 图数据库存储
"""

import os
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor

from ..models.database import Database
from ..models.graph import NodeType, RelationType, VulnerabilityNode, ComponentNode, VersionNode, AffectsRelation, HasVersionRelation
from ..services.graph_store import GraphStore
from ..collectors.base import BaseVulnerabilityCollector
from ..collectors.nvd import NVDCollector
from ..collectors.github import GitHubCollector
from ..collectors.redhat import RedHatCollector
from ..collectors.debian import DebianCollector
from ..utils.logger import logger
from ..utils.file import FileHandler
from ..models.entities import Vulnerability, Reference, Package, Version

class CollectorService:
    """漏洞数据采集服务"""
    
    def __init__(self,
                config_path: str = "config/sources.json",
                db_url: str = "sqlite:///data/vulnerabilities.db",
                graph_url: str = "bolt://localhost:7687",
                graph_user: str = "neo4j",
                graph_password: str = "password",
                max_workers: int = 4):
        """
        初始化采集服务
        
        Args:
            config_path: 配置文件路径
            db_url: 数据库连接URL
            graph_url: 图数据库连接URL
            graph_user: 图数据库用户名
            graph_password: 图数据库密码
            max_workers: 最大工作线程数
        """
        self.config_path = config_path
        self.db = Database(db_url)
        self.max_workers = max_workers
        self.collectors: Dict[str, BaseVulnerabilityCollector] = {}
        
        # 初始化图数据库
        try:
            self.graph_store = GraphStore(graph_url, graph_user, graph_password)
            logger.info("图数据库连接成功")
        except Exception as e:
            logger.warning(f"图数据库连接失败: {str(e)}")
            logger.info("继续运行，但不会将数据写入图数据库")
            self.graph_store = None
        
        # 加载配置
        self.load_config()
        
        # 初始化数据库
        self.db.create_tables()
        
    def load_config(self):
        """加载数据源配置"""
        try:
            config = FileHandler.read_json(self.config_path)
            sources_config = config.get('sources', {})
            
            # 初始化采集器
            if 'NVD' in sources_config:
                self.collectors['nvd'] = NVDCollector(sources_config['NVD'])
            if 'GitHub' in sources_config:
                self.collectors['github'] = GitHubCollector(sources_config['GitHub'])
            if 'RedHat' in sources_config:
                self.collectors['redhat'] = RedHatCollector(sources_config['RedHat'])
            if 'Debian' in sources_config:
                self.collectors['debian'] = DebianCollector(sources_config['Debian'])
                
            logger.info(f"Loaded collectors: {list(self.collectors.keys())}")
            
        except Exception as e:
            logger.error(f"Failed to load config: {str(e)}")
            raise
    
    async def collect_from_source(self,
                               source: str,
                               collector: BaseVulnerabilityCollector,
                               start_date: Optional[datetime] = None,
                               end_date: Optional[datetime] = None) -> int:
        """
        从单个数据源采集漏洞数据
        
        Args:
            source: 数据源名称
            collector: 采集器实例
            start_date: 开始日期，如果不指定则采集所有数据
            end_date: 结束日期，如果不指定则采集到当前日期
            
        Returns:
            成功采集的漏洞数量
        """
        try:
            logger.info(f"【采集开始】{source.upper()} 数据源")
            
            # 记录采集范围
            if start_date and end_date:
                logger.info(f"【采集范围】时间区间: {start_date.strftime('%Y-%m-%d %H:%M:%S')} 到 {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
            elif start_date:
                logger.info(f"【采集范围】开始日期: {start_date.strftime('%Y-%m-%d %H:%M:%S')} 至今")
            elif end_date:
                logger.info(f"【采集范围】截止日期: {end_date.strftime('%Y-%m-%d %H:%M:%S')}")
            else:
                logger.info("【采集范围】全部可用数据")
            
            # 采集开始时间
            collect_start_time = datetime.now()
            logger.info(f"【采集状态】正在从 {source.upper()} 获取漏洞数据...")
            
            # 获取漏洞数据 (同步调用)
            raw_vulns = collector.fetch_data(start_date, end_date)
            
            # 采集结束时间
            collect_end_time = datetime.now()
            collect_duration = (collect_end_time - collect_start_time).total_seconds()
            
            if not raw_vulns:
                logger.warning(f"【采集结果】从 {source.upper()} 未采集到漏洞数据")
                return 0
                
            count = len(raw_vulns)
            logger.info(f"【采集完成】从 {source.upper()} 采集到 {count} 条漏洞数据，耗时: {collect_duration:.1f}秒，平均速度: {count/collect_duration:.2f}条/秒")
            
            # 开始处理数据
            logger.info(f"【处理开始】开始处理 {source.upper()} 的 {count} 条漏洞数据...")
            process_start_time = datetime.now()
            
            # 批量处理数据，避免一次处理过多数据导致内存问题
            batch_size = 100  # 每批处理100条记录
            processed_count = 0
            success_count = 0
            db_success = 0
            graph_success = 0
            
            for i in range(0, count, batch_size):
                batch = raw_vulns[i:i+batch_size]
                batch_len = len(batch)
                batch_num = i//batch_size + 1
                total_batches = (count + batch_size - 1) // batch_size
                
                logger.info(f"【批次处理】{source.upper()} 第 {batch_num}/{total_batches} 批数据 ({batch_len} 条，总进度: {i}/{count}，{i/count*100:.1f}%)")
                batch_start_time = datetime.now()
                
                # 批次统计
                batch_processed = 0
                batch_success = 0
                batch_db_success = 0
                batch_graph_success = 0
                
                # 保存到数据库
                for idx, raw_vuln in enumerate(batch):
                    try:
                        # 预处理原始数据，确保datetime对象被序列化
                        serializable_raw_vuln = {}
                        for key, value in raw_vuln.items():
                            if isinstance(value, datetime):
                                serializable_raw_vuln[key] = value.isoformat()
                            else:
                                serializable_raw_vuln[key] = value
                        
                        # 将原始字典转换为Vulnerability对象
                        # 1. 创建空的引用列表
                        references = []
                        if 'references' in serializable_raw_vuln and isinstance(serializable_raw_vuln['references'], list):
                            for ref in serializable_raw_vuln['references']:
                                if isinstance(ref, dict):
                                    references.append(Reference(
                                        url=ref.get('url', ''),
                                        source=ref.get('source', 'unknown'),
                                        type=ref.get('type', 'other'),
                                        tags=ref.get('tags', [])
                                    ))
                        
                        # 2. 创建空的受影响包列表
                        affected_packages = []
                        if 'affected_packages' in serializable_raw_vuln and isinstance(serializable_raw_vuln['affected_packages'], list):
                            for pkg in serializable_raw_vuln['affected_packages']:
                                if isinstance(pkg, dict):
                                    # 确保字段不为空
                                    pkg_name = pkg.get('name', '')
                                    if not pkg_name:
                                        continue
                                        
                                    affected_packages.append(Package(
                                        name=pkg_name,
                                        ecosystem=pkg.get('ecosystem', ''),
                                        platform=pkg.get('platform', ''),
                                        versions=[],
                                        affected_versions=pkg.get('vulnerable_version_range', '').split(',') if pkg.get('vulnerable_version_range') else [],
                                        fixed_versions=[pkg.get('first_patched_version', '')] if pkg.get('first_patched_version') else []
                                    ))
                        
                        # 3. 处理日期字段
                        published_date = None
                        if serializable_raw_vuln.get('published_date'):
                            try:
                                if isinstance(serializable_raw_vuln['published_date'], str):
                                    published_date = datetime.fromisoformat(serializable_raw_vuln['published_date'].rstrip('Z'))
                                else:
                                    published_date = serializable_raw_vuln['published_date']
                            except Exception as e:
                                logger.warning(f"【日期解析】无法解析发布日期: {serializable_raw_vuln.get('published_date')} - {str(e)}")
                        
                        updated_date = None
                        if serializable_raw_vuln.get('updated_date'):
                            try:
                                if isinstance(serializable_raw_vuln['updated_date'], str):
                                    updated_date = datetime.fromisoformat(serializable_raw_vuln['updated_date'].rstrip('Z'))
                                else:
                                    updated_date = serializable_raw_vuln['updated_date']
                            except Exception as e:
                                logger.warning(f"【日期解析】无法解析更新日期: {serializable_raw_vuln.get('updated_date')} - {str(e)}")
                        
                        # 确保ID不为空
                        vuln_id = serializable_raw_vuln.get('id')
                        if not vuln_id:
                            logger.warning(f"【数据检查】跳过没有ID的漏洞记录")
                            continue
                        
                        # 直接打印漏洞ID以确保看到信息
                        print(f"\n处理漏洞ID: {vuln_id} (来源: {source})")
                            
                        # 4. 创建漏洞对象
                        vuln = Vulnerability(
                            id=vuln_id,
                            source=serializable_raw_vuln.get('source', source),  # 使用指定的source或记录中的source
                            title=serializable_raw_vuln.get('summary', ''),
                            description=serializable_raw_vuln.get('description', ''),
                            published_date=published_date,
                            last_modified_date=updated_date,
                            severity=serializable_raw_vuln.get('severity', ''),
                            status='confirmed',
                            affected_packages=affected_packages,
                            references=references,
                            patches=[],
                            notes=[],
                            raw_data=serializable_raw_vuln
                        )
                        
                        batch_processed += 1
                        
                        # 5. 保存到关系型数据库
                        try:
                            self.db.add_vulnerability(vuln)
                            batch_success += 1
                            batch_db_success += 1
                            
                            # 直接打印到控制台确保看到信息
                            print(f"✅ 漏洞 {vuln_id} 已成功保存到数据库")
                            
                            # 只有在漏洞ID有价值时才输出详细信息
                            logger.info(f"【数据保存】{vuln.source} 漏洞 '{vuln_id}' 已保存到数据库")
                        except Exception as e:
                            error_msg = f"【数据入库】保存漏洞 {vuln_id} 到数据库失败: {str(e)}"
                            logger.error(error_msg)
                            print(f"❌ {error_msg}")
                        
                        # 6. 保存到图数据库
                        if self.graph_store:
                            try:
                                # 1. 创建漏洞节点
                                cvss_v3_score = None
                                if 'cvss_v3' in serializable_raw_vuln and isinstance(serializable_raw_vuln['cvss_v3'], dict):
                                    cvss_v3_score = serializable_raw_vuln['cvss_v3'].get('base_score')
                                
                                cvss_v2_score = None
                                if 'cvss_v2' in serializable_raw_vuln and isinstance(serializable_raw_vuln['cvss_v2'], dict):
                                    cvss_v2_score = serializable_raw_vuln['cvss_v2'].get('base_score')
                                
                                vuln_node = VulnerabilityNode(
                                    vuln_id=vuln.id,
                                    source=vuln.source,
                                    title=vuln.title,
                                    description=vuln.description,
                                    severity=vuln.severity,
                                    published_date=published_date,
                                    last_modified_date=updated_date,
                                    cvss_v3_score=cvss_v3_score,
                                    cvss_v2_score=cvss_v2_score
                                )
                                self.graph_store.create_node(vuln_node)
                                
                                # 2. 创建组件节点和关系
                                for pkg in affected_packages:
                                    if not pkg.name:
                                        continue
                                    
                                    # 创建组件节点
                                    component_node = ComponentNode(
                                        name=pkg.name,
                                        ecosystem=pkg.ecosystem,
                                        platform=pkg.platform
                                    )
                                    self.graph_store.create_node(component_node)
                                    
                                    # 创建影响关系
                                    affects_relation = AffectsRelation(
                                        vuln_id=vuln.id,
                                        component_id=pkg.name,
                                        affected_versions=pkg.affected_versions,
                                        fixed_versions=pkg.fixed_versions,
                                        severity=vuln.severity
                                    )
                                    self.graph_store.create_relation(affects_relation)
                                
                                batch_graph_success += 1
                                logger.debug(f"【图数据库】成功保存漏洞 {vuln_id} 到图数据库")
                            except Exception as e:
                                logger.error(f"【图数据库】保存漏洞 {vuln_id} 到图数据库失败: {str(e)}")
                    
                    except Exception as e:
                        logger.error(f"【处理错误】处理来自 {source.upper()} 的第 {i+idx+1}/{count} 条漏洞数据时出错: {str(e)}")
                    
                    processed_count += 1
                    
                    # 每10条或每批次结束时输出进度
                    if (idx+1) % 10 == 0 or idx+1 == batch_len:
                        percent_batch = (idx+1) / batch_len * 100
                        percent_total = processed_count / count * 100
                        logger.info(f"【处理进度】批次内: {idx+1}/{batch_len} ({percent_batch:.1f}%)，总进度: {processed_count}/{count} ({percent_total:.1f}%)")
                
                # 批次处理完成后的统计
                batch_duration = (datetime.now() - batch_start_time).total_seconds()
                success_count += batch_success
                db_success += batch_db_success
                graph_success += batch_graph_success
                
                logger.info(f"【批次完成】第 {batch_num}/{total_batches} 批处理完成，耗时: {batch_duration:.1f}秒")
                logger.info(f"【批次统计】处理: {batch_processed}/{batch_len}，成功: {batch_success}，数据库成功: {batch_db_success}，图数据库成功: {batch_graph_success}")
                
                # 每批次间添加一点延迟，避免系统负载过高
                if i + batch_size < count:
                    logger.info("【批次间隔】等待1秒后继续下一批处理...")
                    await asyncio.sleep(1)
            
            # 处理结束时间和耗时统计
            process_end_time = datetime.now()
            process_duration = (process_end_time - process_start_time).total_seconds()
            total_duration = (process_end_time - collect_start_time).total_seconds()
            
            # 总体统计
            logger.info(f"【处理完成】{source.upper()} 数据处理完成，处理 {processed_count}/{count} 条数据")
            logger.info(f"【成功统计】总成功: {success_count}，数据库成功: {db_success}，图数据库成功: {graph_success}")
            logger.info(f"【时间统计】采集耗时: {collect_duration:.1f}秒，处理耗时: {process_duration:.1f}秒，总耗时: {total_duration:.1f}秒")
            logger.info(f"【效率统计】平均处理速度: {processed_count/process_duration:.2f}条/秒")
            
            return success_count
        except Exception as e:
            logger.error(f"【采集异常】从 {source.upper()} 采集数据过程中发生异常: {str(e)}")
            return 0
    
    async def collect_all(self,
                       start_date: Optional[datetime] = None,
                       end_date: Optional[datetime] = None,
                       sources: Optional[List[str]] = None) -> Dict[str, int]:
        """
        从多个数据源并行采集数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            sources: 数据源列表，如果为None则采集所有已配置的数据源
            
        Returns:
            各数据源采集结果统计，键为数据源名，值为采集的漏洞数量
        """
        results = {}
        tasks = []
        selected_sources = sources if sources else list(self.collectors.keys())

        # 创建目录（如果不存在）
        os.makedirs("data", exist_ok=True)
        
        # 设置最大并行任务数，防止服务器过载
        sem = asyncio.Semaphore(min(self.max_workers, len(selected_sources)))
        
        # 包装采集任务，添加信号量和错误处理
        async def collect_with_semaphore(source: str):
            try:
                async with sem:
                    if source not in self.collectors:
                        logger.warning(f"未找到数据源: {source}")
                        return source, 0
                    
                    # 使用最长超时时间，确保有足够时间完成
                    timeout = max(300, self.collectors[source].timeout * 3)  
                    
                    # 设置超时
                    try:
                        # 使用asyncio.wait_for执行任务并设置超时
                        collector_result = await asyncio.wait_for(
                            self.collect_from_source(source, self.collectors[source], start_date, end_date),
                            timeout=timeout
                        )
                        return source, collector_result
                    except asyncio.TimeoutError:
                        logger.error(f"从数据源 {source} 采集数据超时 ({timeout}秒)")
                        return source, 0
            except Exception as e:
                logger.error(f"从数据源 {source} 采集数据发生异常: {str(e)}")
                return source, 0
        
        # 创建所有采集任务
        for source in selected_sources:
            if source in self.collectors:
                task = asyncio.create_task(collect_with_semaphore(source))
                tasks.append(task)
            else:
                logger.warning(f"未找到数据源: {source}")
                results[source] = 0
        
        # 等待所有任务完成，即使部分任务失败
        if tasks:
            # 使用gather收集所有完成的任务结果
            completed_tasks = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果，过滤掉异常
            for result in completed_tasks:
                if isinstance(result, Exception):
                    logger.error(f"采集任务异常: {str(result)}")
                elif isinstance(result, tuple) and len(result) == 2:
                    source, count = result
                    results[source] = count
        
        return results
    
    def run_incremental(self,
                      days: int = 7,
                      sources: Optional[List[str]] = None,
                      start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None) -> Dict[str, int]:
        """
        运行增量更新
        
        Args:
            days: 更新最近几天的数据
            sources: 指定的数据源列表
            start_date: 指定的开始日期（如果提供，优先于days参数）
            end_date: 指定的结束日期（如果提供，优先于days参数）
            
        Returns:
            各数据源采集的漏洞数量
        """
        # 如果提供了start_date和end_date，则使用指定的日期范围
        if start_date is None and end_date is None:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
        # 如果只提供了start_date但没有提供end_date
        elif start_date is not None and end_date is None:
            end_date = datetime.now()
        # 如果只提供了end_date但没有提供start_date
        elif start_date is None and end_date is not None:
            start_date = end_date - timedelta(days=days)
        
        # 运行异步采集任务
        loop = asyncio.get_event_loop()
        stats = loop.run_until_complete(
            self.collect_all(start_date, end_date, sources)
        )
        
        return stats
    
    def run_full(self,
               start_date: Optional[datetime] = None,
               end_date: Optional[datetime] = None,
               sources: Optional[List[str]] = None) -> Dict[str, int]:
        """
        运行完整的漏洞数据采集（可以按数据源和时间范围过滤）
        
        Args:
            start_date: 开始日期，如果不指定，则采集所有数据
            end_date: 结束日期，如果不指定，则采集到当前日期
            sources: 要采集的数据源列表，如果不指定，则采集所有数据源
            
        Returns:
            各数据源采集结果统计，键为数据源名，值为采集的漏洞数量
        """
        logger.info("开始完整数据采集")
        
        # 如果没有指定结束日期，则默认为当前日期
        if not end_date:
            end_date = datetime.now()
        
        # 如果没有指定开始日期，保持为None以便采集器采集所有数据
        
        # 设置要采集的数据源
        selected_collectors = {}
        if sources:
            for src in sources:
                if src in self.collectors:
                    selected_collectors[src] = self.collectors[src]
        else:
            selected_collectors = self.collectors
        
        if not selected_collectors:
            logger.warning("没有找到指定的数据源")
            return {}
        
        # 为大规模采集分割时间范围
        if start_date and end_date:
            # 计算时间间隔
            delta = end_date - start_date
            
            # 如果时间范围超过90天，按季度分批采集
            if delta.days > 90:
                logger.info(f"时间范围较大 ({delta.days} 天)，将按季度分批采集")
                
                # 保存总采集结果
                total_results = {src: 0 for src in selected_collectors.keys()}
                
                # 按季度分批采集
                current_start = start_date
                while current_start < end_date:
                    # 计算当前批次的结束日期
                    current_end = current_start + timedelta(days=90)
                    if current_end > end_date:
                        current_end = end_date
                    
                    logger.info(f"开始采集时间段: {current_start.isoformat()} 到 {current_end.isoformat()}")
                    
                    try:
                        # 为每个批次运行异步采集
                        batch_results = asyncio.run(self.collect_all(current_start, current_end, list(selected_collectors.keys())))
                        
                        # 累加结果
                        for src, count in batch_results.items():
                            total_results[src] += count
                        
                        logger.info(f"时间段 {current_start.isoformat()} 到 {current_end.isoformat()} 采集完成，共 {sum(batch_results.values())} 条漏洞")
                    except Exception as e:
                        logger.error(f"时间段 {current_start.isoformat()} 到 {current_end.isoformat()} 采集失败: {str(e)}")
                        # 继续下一个批次
                    
                    # 更新下一个批次的开始日期
                    current_start = current_end
                    
                    # 添加间隔，防止API限制
                    logger.info("等待10秒后继续下一批次采集...")
                    asyncio.run(asyncio.sleep(10))
                
                return total_results
        
        # 对于小规模时间范围或未指定时间范围的情况，直接采集
        try:
            results = asyncio.run(self.collect_all(start_date, end_date, list(selected_collectors.keys())))
            logger.info(f"完整数据采集完成，共 {sum(results.values())} 条漏洞")
            return results
        except Exception as e:
            logger.error(f"完整数据采集失败: {str(e)}")
            return {}

"""
使用示例：

1. 基本用法:
from services.collector import CollectorService

# 创建采集服务实例
service = CollectorService()

# 运行增量更新（最近7天的数据）
stats = service.run_incremental()
print(f"Collected vulnerabilities: {stats}")

2. 完整更新:
from datetime import datetime

# 指定日期范围
start_date = datetime(2024, 1, 1)
end_date = datetime(2024, 1, 31)

# 运行完整更新
stats = service.run_full(start_date, end_date)

3. 指定数据源:
# 只从NVD和GitHub采集数据
stats = service.run_incremental(sources=['nvd', 'github'])

4. 配置文件示例 (config/sources.json):
{
    "nvd": {
        "api_key": "your-nvd-api-key",
        "delay_between_requests": 6,
        "max_retries": 3,
        "timeout": 30
    },
    "github": {
        "api_key": "your-github-token",
        "delay_between_requests": 1,
        "max_retries": 3,
        "timeout": 30
    },
    "redhat": {
        "delay_between_requests": 1,
        "max_retries": 3,
        "timeout": 30
    },
    "debian": {
        "releases": ["buster", "bullseye", "bookworm"],
        "delay_between_requests": 1,
        "max_retries": 3,
        "timeout": 30
    }
}
"""

def main():
    """主函数，作为命令行入口点"""
    import argparse
    from datetime import datetime, timedelta
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='漏洞数据采集服务')
    
    # 配置参数
    parser.add_argument('--config', type=str, default='config/sources.json',
                        help='配置文件路径 (默认: config/sources.json)')
    parser.add_argument('--db-url', type=str, default='sqlite:///data/vulnerabilities.db',
                        help='数据库连接URL (默认: sqlite:///data/vulnerabilities.db)')
    
    # 数据源参数
    parser.add_argument('--sources', nargs='+', 
                        choices=['nvd', 'github', 'redhat', 'debian', 'all'],
                        default=['all'],
                        help='要采集的数据源 (默认: all)')
    
    # 日期范围参数
    parser.add_argument('--start-date', type=str,
                        help='开始日期 (格式: YYYY-MM-DD，默认: 7天前)')
    parser.add_argument('--end-date', type=str,
                        help='结束日期 (格式: YYYY-MM-DD，默认: 当前日期)')
    parser.add_argument('--days', type=int, default=7,
                        help='采集最近几天的数据 (默认: 7)')
    
    # 模式参数
    parser.add_argument('--full', action='store_true',
                        help='完整更新模式 (默认: 增量更新)')
    
    args = parser.parse_args()
    
    # 处理数据源参数
    if 'all' in args.sources:
        sources = None  # 使用所有可用数据源
    else:
        sources = args.sources
    
    # 处理日期参数
    end_date = None
    start_date = None
    
    if args.end_date:
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
    
    if args.start_date:
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
    
    # 创建服务实例
    service = CollectorService(
        config_path=args.config,
        db_url=args.db_url
    )
    
    # 运行采集
    if args.full:
        # 完整更新
        logger.info(f"运行完整更新，开始日期: {start_date}，结束日期: {end_date}，数据源: {sources or '所有'}")
        stats = service.run_full(start_date, end_date, sources)
    else:
        # 增量更新
        logger.info(f"运行增量更新，最近 {args.days} 天，数据源: {sources or '所有'}")
        stats = service.run_incremental(args.days, sources, start_date, end_date)
    
    # 打印结果
    logger.info("采集结果统计:")
    for source, count in stats.items():
        logger.info(f"  {source}: {count} 条漏洞")
    
    total = sum(stats.values())
    logger.info(f"总计: {total} 条漏洞")

if __name__ == '__main__':
    main() 