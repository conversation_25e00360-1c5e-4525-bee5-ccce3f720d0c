#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j图数据库存储服务模块

提供图数据库的存储和查询功能:
1. 节点的创建、更新和删除
2. 关系的创建、更新和删除
3. 图查询和遍历
4. 批量数据导入
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import neo4j
from neo4j import GraphDatabase
from neo4j.exceptions import Neo4jError

from ..models.graph import (
    GraphNode, GraphRelation, # 图节点和关系
    VulnerabilityNode, ComponentNode, VersionNode, PatchNode, # 漏洞、组件、版本、补丁节点
    AffectsRelation, HasVersionRelation, FixedByRelation, DependsOnRelation, # 漏洞影响组件关系、组件包含版本关系、漏洞被补丁修复关系、组件依赖关系
    NodeType, RelationType # 节点类型、关系类型
)
from ..utils.logger import logger

class GraphStore:
    """图数据库存储服务"""
    
    def __init__(self,
                uri: str = "bolt://localhost:7687", # Neo4j数据库URI
                username: str = "neo4j", # 用户名
                password: str = "password", # 密码
                database: str = "neo4j", # 数据库名
                test_mode: bool = False, # 测试模式（不实际连接数据库）
                connection_timeout: int = 5):  # 添加连接超时和测试模式选项
        """
        初始化图数据库连接
        
        Args:
            uri: Neo4j数据库URI
            username: 用户名
            password: 密码
            database: 数据库名
            test_mode: 测试模式（不实际连接数据库）
            connection_timeout: 连接超时（秒）
        """
        self.uri = uri
        self.username = username
        self.password = password
        self.database = database
        self.test_mode = test_mode
        
        if not test_mode:
            try:
                # 设置连接超时
                self.driver = GraphDatabase.driver(
                    uri, 
                    auth=(username, password),
                    connection_timeout=connection_timeout
                )
                
                # 验证连接
                self.driver.verify_connectivity()
                logger.info("Successfully connected to Neo4j database")
            except Exception as e:
                logger.error(f"Failed to connect to Neo4j: {str(e)}")
                # 在初始化失败时不抛出异常，而是设置driver为None
                self.driver = None
        else:
            logger.info("Running in test mode, no actual Neo4j connection will be made")
            self.driver = None
    
    def is_connected(self) -> bool:
        """
        检查是否连接到Neo4j
        
        Returns:
            是否已连接
        """
        if self.test_mode:
            return True
            
        if self.driver is None:
            return False
            
        try:
            self.driver.verify_connectivity()
            return True
        except:
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.driver is not None:
            self.driver.close()
    
    def create_node(self, node: GraphNode) -> bool:
        """
        创建节点
        
        Args:
            node: 节点对象
            
        Returns:
            是否创建成功
        """
        if self.test_mode:
            logger.info(f"Test mode: Would create node {node.id} of type {node.node_type.value}")
            return True
            
        if self.driver is None:
            logger.error("No connection to Neo4j database")
            return False
            
        query = (
            f"CREATE (n:{node.node_type.value} {{id: $id}}) "
            "SET n += $properties "
            "RETURN n"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(
                    query,
                    id=node.id,
                    properties=node.properties
                )
                return result.single() is not None
        except Neo4jError as e:
            logger.error(f"Failed to create node: {str(e)}")
            return False
    
    def update_node(self, node: GraphNode) -> bool:
        """
        更新节点
        
        Args:
            node: 节点对象
            
        Returns:
            是否更新成功
        """
        if self.test_mode:
            logger.info(f"Test mode: Would update node {node.id} of type {node.node_type.value}")
            return True
            
        if self.driver is None:
            logger.error("No connection to Neo4j database")
            return False
            
        query = (
            f"MATCH (n:{node.node_type.value} {{id: $id}}) "
            "SET n += $properties "
            "RETURN n"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(
                    query,
                    id=node.id,
                    properties=node.properties
                )
                return result.single() is not None
        except Neo4jError as e:
            logger.error(f"Failed to update node: {str(e)}")
            return False
    
    def delete_node(self, node_type: NodeType, node_id: str) -> bool:
        """
        删除节点
        
        Args:
            node_type: 节点类型
            node_id: 节点ID
            
        Returns:
            是否删除成功
        """
        if self.test_mode:
            logger.info(f"Test mode: Would delete node {node_id} of type {node_type.value}")
            return True
            
        if self.driver is None:
            logger.error("No connection to Neo4j database")
            return False
            
        query = (
            f"MATCH (n:{node_type.value} {{id: $id}}) "
            "DETACH DELETE n"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                session.run(query, id=node_id)
                return True
        except Neo4jError as e:
            logger.error(f"Failed to delete node: {str(e)}")
            return False
    
    def create_relation(self, relation: GraphRelation) -> bool:
        """
        创建关系
        
        Args:
            relation: 关系对象
            
        Returns:
            是否创建成功
        """
        query = (
            "MATCH (a {id: $source_id}), (b {id: $target_id}) "
            f"CREATE (a)-[r:{relation.relation_type.value}]->(b) "
            "SET r += $properties "
            "RETURN r"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(
                    query,
                    source_id=relation.source_id,
                    target_id=relation.target_id,
                    properties=relation.properties
                )
                return result.single() is not None
        except Neo4jError as e:
            logger.error(f"Failed to create relation: {str(e)}")
            return False
    
    def update_relation(self, relation: GraphRelation) -> bool:
        """
        更新关系
        
        Args:
            relation: 关系对象
            
        Returns:
            是否更新成功
        """
        query = (
            "MATCH (a {id: $source_id})-[r:"
            f"{relation.relation_type.value}]->(b {{id: $target_id}}) "
            "SET r += $properties "
            "RETURN r"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(
                    query,
                    source_id=relation.source_id,
                    target_id=relation.target_id,
                    properties=relation.properties
                )
                return result.single() is not None
        except Neo4jError as e:
            logger.error(f"Failed to update relation: {str(e)}")
            return False
    
    def delete_relation(self,
                      relation_type: RelationType,
                      source_id: str,
                      target_id: str) -> bool:
        """
        删除关系
        
        Args:
            relation_type: 关系类型
            source_id: 源节点ID
            target_id: 目标节点ID
            
        Returns:
            是否删除成功
        """
        query = (
            "MATCH (a {id: $source_id})"
            f"-[r:{relation_type.value}]->"
            "(b {id: $target_id}) "
            "DELETE r"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                session.run(
                    query,
                    source_id=source_id,
                    target_id=target_id
                )
                return True
        except Neo4jError as e:
            logger.error(f"Failed to delete relation: {str(e)}")
            return False
    
    def get_node(self,
                node_type: NodeType,
                node_id: str) -> Optional[Dict[str, Any]]:
        """
        获取节点
        
        Args:
            node_type: 节点类型
            node_id: 节点ID
            
        Returns:
            节点数据
        """
        query = (
            f"MATCH (n:{node_type.value} {{id: $id}}) "
            "RETURN n"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(query, id=node_id)
                record = result.single()
                
                if record:
                    node = record["n"]
                    return {
                        "id": node_id,
                        "type": node_type.value,
                        "properties": dict(node)
                    }
                return None
        except Neo4jError as e:
            logger.error(f"Failed to get node: {str(e)}")
            return None
    
    def get_nodes(self,
                node_type: Optional[NodeType] = None,
                properties: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        获取节点列表
        
        Args:
            node_type: 节点类型
            properties: 属性过滤条件
            
        Returns:
            节点列表
        """
        if node_type:
            query = f"MATCH (n:{node_type.value})"
        else:
            query = "MATCH (n)"
            
        where_clauses = []
        params = {}
        
        if properties:
            for key, value in properties.items():
                where_clauses.append(f"n.{key} = ${key}")
                params[key] = value
                
        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)
            
        query += " RETURN n"
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(query, **params)
                
                nodes = []
                for record in result:
                    node = record["n"]
                    nodes.append({
                        "id": node["id"],
                        "type": node.element_id.split(":")[0],
                        "properties": dict(node)
                    })
                    
                return nodes
        except Neo4jError as e:
            logger.error(f"Failed to get nodes: {str(e)}")
            return []
    
    def get_relation(self,
                   relation_type: RelationType,
                   source_id: str,
                   target_id: str) -> Optional[Dict[str, Any]]:
        """
        获取关系
        
        Args:
            relation_type: 关系类型
            source_id: 源节点ID
            target_id: 目标节点ID
            
        Returns:
            关系数据
        """
        query = (
            "MATCH (a {id: $source_id})"
            f"-[r:{relation_type.value}]->"
            "(b {id: $target_id}) "
            "RETURN a, r, b"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(
                    query,
                    source_id=source_id,
                    target_id=target_id
                )
                record = result.single()
                
                if record:
                    return {
                        "source": {
                            "id": source_id,
                            "type": record["a"].element_id.split(":")[0],
                            "properties": dict(record["a"])
                        },
                        "relation": {
                            "type": relation_type.value,
                            "properties": dict(record["r"])
                        },
                        "target": {
                            "id": target_id,
                            "type": record["b"].element_id.split(":")[0],
                            "properties": dict(record["b"])
                        }
                    }
                return None
        except Neo4jError as e:
            logger.error(f"Failed to get relation: {str(e)}")
            return None
    
    def get_relations(self,
                    relation_type: Optional[RelationType] = None, # 关系类型
                    source_id: Optional[str] = None, # 源节点ID
                    target_id: Optional[str] = None, # 目标节点ID
                    properties: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        获取关系列表
        
        Args:
            relation_type: 关系类型
            source_id: 源节点ID
            target_id: 目标节点ID
            properties: 属性过滤条件
            
        Returns:
            关系列表
        """
        query_parts = ["MATCH (a)"]
        
        if relation_type:
            query_parts.append(f"-[r:{relation_type.value}]->")
        else:
            query_parts.append("-[r]->")
            
        query_parts.append("(b)")
        
        where_clauses = []
        params = {}
        
        if source_id:
            where_clauses.append("a.id = $source_id")
            params["source_id"] = source_id
            
        if target_id:
            where_clauses.append("b.id = $target_id")
            params["target_id"] = target_id
            
        if properties:
            for key, value in properties.items():
                where_clauses.append(f"r.{key} = ${key}")
                params[key] = value
                
        if where_clauses:
            query_parts.append("WHERE " + " AND ".join(where_clauses))
            
        query_parts.append("RETURN a, r, b")
        
        query = " ".join(query_parts)
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(query, **params)
                
                relations = []
                for record in result:
                    relations.append({
                        "source": {
                            "id": record["a"]["id"],
                            "type": record["a"].element_id.split(":")[0],
                            "properties": dict(record["a"])
                        },
                        "relation": {
                            "type": record["r"].type,
                            "properties": dict(record["r"])
                        },
                        "target": {
                            "id": record["b"]["id"],
                            "type": record["b"].element_id.split(":")[0],
                            "properties": dict(record["b"])
                        }
                    })
                    
                return relations
        except Neo4jError as e:
            logger.error(f"Failed to get relations: {str(e)}")
            return []
    
    def find_paths(self,
                 start_id: str, # 起始节点ID
                 end_id: str, # 目标节点ID
                 max_depth: int = 5) -> List[Dict[str, Any]]:
        """
        查找路径
        
        Args:
            start_id: 起始节点ID
            end_id: 目标节点ID
            max_depth: 最大深度
            
        Returns:
            路径列表
        """
        # 使用Neo4j的最短路径算法
        query = (
            "MATCH (start {id: $start_id}), (end {id: $end_id}), "
            f"p = shortestPath((start)-[*1..{max_depth}]->(end)) "
            "RETURN p"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(
                    query,
                    start_id=start_id,
                    end_id=end_id
                )
                
                paths = []
                for record in result:
                    path = record["p"]
                    
                    # 解析路径中的节点和关系
                    nodes = []
                    for node in path.nodes:
                        nodes.append({
                            "id": node["id"],
                            "type": node.element_id.split(":")[0],
                            "properties": dict(node)
                        })
                        
                    relations = []
                    for rel in path.relationships:
                        relations.append({
                            "source_id": rel.start_node["id"],
                            "target_id": rel.end_node["id"],
                            "type": rel.type,
                            "properties": dict(rel)
                        })
                        
                    paths.append({
                        "length": len(relations),
                        "nodes": nodes,
                        "relations": relations
                    })
                    
                return paths
        except Neo4jError as e:
            logger.error(f"Failed to find paths: {str(e)}")
            return []
    
    def find_all_paths(self,
                     start_id: str, # 起始节点ID
                     end_id: str, # 目标节点ID
                     max_depth: int = 5, # 最大深度
                     limit: int = 10) -> List[Dict[str, Any]]:
        """
        查找所有路径
        
        Args:
            start_id: 起始节点ID
            end_id: 目标节点ID
            max_depth: 最大深度
            limit: 最大返回路径数
            
        Returns:
            路径列表
        """
        # 使用Neo4j的allShortestPaths算法
        query = (
            "MATCH (start {id: $start_id}), (end {id: $end_id}), "
            f"p = allShortestPaths((start)-[*1..{max_depth}]->(end)) "
            f"RETURN p LIMIT {limit}"
        )
        
        try:
            with self.driver.session(database=self.database) as session:
                result = session.run(
                    query,
                    start_id=start_id,
                    end_id=end_id
                )
                
                paths = []
                for record in result:
                    path = record["p"]
                    
                    # 解析路径中的节点和关系
                    nodes = []
                    for node in path.nodes:
                        nodes.append({
                            "id": node["id"],
                            "type": node.element_id.split(":")[0],
                            "properties": dict(node)
                        })
                        
                    relations = []
                    for rel in path.relationships:
                        relations.append({
                            "source_id": rel.start_node["id"],
                            "target_id": rel.end_node["id"],
                            "type": rel.type,
                            "properties": dict(rel)
                        })
                        
                    paths.append({
                        "length": len(relations),
                        "nodes": nodes,
                        "relations": relations
                    })
                    
                return paths
        except Neo4jError as e:
            logger.error(f"Failed to find all paths: {str(e)}")
            return []
    
    def batch_create_nodes(self, nodes: List[GraphNode]) -> int:
        """
        批量创建节点
        
        Args:
            nodes: 节点对象列表
            
        Returns:
            成功创建的节点数
        """
        success_count = 0
        
        # 使用事务批量创建节点
        with self.driver.session(database=self.database) as session:
            tx = session.begin_transaction()
            
            try:
                for node in nodes:
                    query = (
                        f"CREATE (n:{node.node_type.value} {{id: $id}}) " # 创建节点
                        "SET n += $properties " # 设置节点属性
                        "RETURN n" # 返回创建的节点
                    )
                    
                    result = tx.run(
                        query,
                        id=node.id, # 节点ID
                        properties=node.properties # 节点属性
                    )
                    
                    if result.single():
                        success_count += 1
                
                tx.commit() # 提交事务
                logger.info(f"Successfully created {success_count}/{len(nodes)} nodes") # 成功创建的节点数
            except Exception as e:
                tx.rollback() # 回滚事务
                logger.error(f"Failed to batch create nodes: {str(e)}") # 失败创建的节点数
        
        return success_count # 返回成功创建的节点数 
    
    def batch_create_relations(self, relations: List[GraphRelation]) -> int:
        """
        批量创建关系
        
        Args:
            relations: 关系对象列表
            
        Returns:
            成功创建的关系数
        """
        success_count = 0
        
        # 使用事务批量创建关系
        with self.driver.session(database=self.database) as session:
            tx = session.begin_transaction()
            
            try:
                for relation in relations:
                    query = (
                        "MATCH (a {id: $source_id}), (b {id: $target_id}) " # 匹配源节点和目标节点
                        f"CREATE (a)-[r:{relation.relation_type.value}]->(b) " # 创建关系
                        "SET r += $properties " # 设置关系属性
                        "RETURN r" # 返回创建的关系
                    )
                    
                    result = tx.run(
                        query,
                        source_id=relation.source_id, # 源节点ID
                        target_id=relation.target_id, # 目标节点ID
                        properties=relation.properties # 关系属性
                    )
                    
                    if result.single():
                        success_count += 1
                
                tx.commit() # 提交事务
                logger.info(f"Successfully created {success_count}/{len(relations)} relations") # 成功创建的关系数
            except Exception as e:
                tx.rollback() # 回滚事务
                logger.error(f"Failed to batch create relations: {str(e)}") # 失败创建的关系数
        
        return success_count 