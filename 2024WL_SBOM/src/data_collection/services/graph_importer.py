#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图数据库导入服务模块

提供将采集的漏洞数据转换为图数据库实体和关系的功能:
1. 漏洞数据转换为图节点和关系
2. 批量导入到Neo4j图数据库
3. 处理不同数据源的特定格式
4. 增量更新与全量导入支持
"""

import os
import re
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
import sqlite3
from tqdm import tqdm

from ..models.graph import (
    GraphNode, GraphRelation,
    VulnerabilityNode, ComponentNode, VersionNode, PatchNode,
    AffectsRelation, HasVersionRelation, FixedByRelation, DependsOnRelation,
    NodeType, RelationType
)
from ..models.entities import Vulnerability, Package, Reference
from .graph_store import GraphStore
from ..utils.logger import logger

class GraphImporter:
    """图数据库导入服务"""
    
    def __init__(self,
                db_url: str = "sqlite:///data/vulnerabilities.db",
                graph_store: Optional[GraphStore] = None,
                batch_size: int = 100):
        """
        初始化图数据导入服务
        
        Args:
            db_url: 关系数据库连接URL
            graph_store: 图数据库存储服务实例
            batch_size: 批量导入大小
        """
        self.db_url = db_url
        self.graph_store = graph_store
        self.batch_size = batch_size
        self.conn = None
        
        # 连接SQLite数据库
        if db_url.startswith("sqlite:///"):
            db_path = db_url[len("sqlite:///"):]
            if not os.path.exists(db_path):
                logger.error(f"SQLite数据库文件不存在: {db_path}")
            else:
                try:
                    self.conn = sqlite3.connect(db_path)
                    self.conn.row_factory = sqlite3.Row
                    logger.info(f"已连接到SQLite数据库: {db_path}")
                except Exception as e:
                    logger.error(f"连接SQLite数据库失败: {str(e)}")
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
    
    def import_all_vulnerabilities(self) -> Dict[str, int]:
        """
        导入所有漏洞数据到图数据库
        
        Returns:
            导入统计信息
        """
        if not self.conn:
            return {"error": "数据库连接失败"}
            
        if not self.graph_store or not self.graph_store.is_connected():
            return {"error": "图数据库连接失败"}
        
        stats = {
            "vulnerabilities": 0,
            "components": 0,
            "versions": 0,
            "patches": 0,
            "affects_relations": 0,
            "has_version_relations": 0,
            "fixed_by_relations": 0,
            "has_weakness_relations": 0
        }
        
        # 获取所有漏洞
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM vulnerabilities")
        rows = cursor.fetchall()
        
        # 建立组件和版本索引，避免重复创建
        component_index = set()
        version_index = set()
        
        # 准备批量导入数据
        vuln_nodes = []
        component_nodes = []
        version_nodes = []
        patch_nodes = []
        affects_relations = []
        has_version_relations = []
        fixed_by_relations = []
        has_weakness_relations = []
        
        # 处理每个漏洞
        for row in tqdm(rows, desc="处理漏洞数据"):
            vuln_id = row["id"]
            data = json.loads(row["data"])
            
            # 1. 创建漏洞节点
            vuln_node = self._create_vulnerability_node(data)
            vuln_nodes.append(vuln_node)
            stats["vulnerabilities"] += 1
            
            # 2. 处理受影响的组件和版本
            components, versions, affects = self._process_affected_components(vuln_id, data)
            
            # 添加组件节点（去重）
            for comp in components:
                if comp.id not in component_index:
                    component_nodes.append(comp)
                    component_index.add(comp.id)
                    stats["components"] += 1
            
            # 添加版本节点（去重）
            for ver in versions:
                ver_id = ver.id
                if ver_id not in version_index:
                    version_nodes.append(ver)
                    version_index.add(ver_id)
                    stats["versions"] += 1
                    
                    # 添加组件-版本关系
                    comp_id = ver_id.split("@")[0]
                    has_version = HasVersionRelation(comp_id, ver_id)
                    has_version_relations.append(has_version)
                    stats["has_version_relations"] += 1
            
            # 添加漏洞-组件关系
            affects_relations.extend(affects)
            stats["affects_relations"] += len(affects)
            
            # 3. 处理补丁和修复关系
            patches, fixed_by = self._process_patches(vuln_id, data)
            patch_nodes.extend(patches)
            fixed_by_relations.extend(fixed_by)
            stats["patches"] += len(patches)
            stats["fixed_by_relations"] += len(fixed_by)
            
            # 4. 处理CWE弱点关系
            weaknesses = self._process_weaknesses(vuln_id, data)
            has_weakness_relations.extend(weaknesses)
            stats["has_weakness_relations"] += len(weaknesses)
            
            # 批量导入（当达到批量大小时）
            if len(vuln_nodes) >= self.batch_size:
                self._batch_import_nodes_and_relations(
                    vuln_nodes, component_nodes, version_nodes, patch_nodes,
                    affects_relations, has_version_relations, fixed_by_relations, has_weakness_relations
                )
                
                # 清空已导入的批次
                vuln_nodes = []
                component_nodes = []
                version_nodes = []
                patch_nodes = []
                affects_relations = []
                has_version_relations = []
                fixed_by_relations = []
                has_weakness_relations = []
        
        # 导入剩余的数据
        if vuln_nodes:
            self._batch_import_nodes_and_relations(
                vuln_nodes, component_nodes, version_nodes, patch_nodes,
                affects_relations, has_version_relations, fixed_by_relations, has_weakness_relations
            )
        
        return stats
    
    def import_vulnerabilities_by_source(self, source: str) -> Dict[str, int]:
        """
        按数据源导入漏洞数据
        
        Args:
            source: 数据源名称（如nvd, github等）
            
        Returns:
            导入统计信息
        """
        if not self.conn:
            return {"error": "数据库连接失败"}
            
        if not self.graph_store or not self.graph_store.is_connected():
            return {"error": "图数据库连接失败"}
        
        # 根据源筛选漏洞
        cursor = self.conn.cursor()
        cursor.execute("SELECT * FROM vulnerabilities WHERE source = ?", (source,))
        rows = cursor.fetchall()
        
        if not rows:
            return {"error": f"未找到来源为 {source} 的漏洞数据"}
        
        # 调用通用导入方法处理数据
        stats = self._import_vulnerabilities(rows)
        return stats
    
    def import_vulnerabilities_by_date_range(self, 
                                         start_date: datetime, 
                                         end_date: datetime) -> Dict[str, int]:
        """
        按日期范围导入漏洞数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            导入统计信息
        """
        if not self.conn:
            return {"error": "数据库连接失败"}
            
        if not self.graph_store or not self.graph_store.is_connected():
            return {"error": "图数据库连接失败"}
        
        # 转换日期格式
        start_str = start_date.strftime("%Y-%m-%dT%H:%M:%S")
        end_str = end_date.strftime("%Y-%m-%dT%H:%M:%S")
        
        # 按日期范围筛选漏洞
        cursor = self.conn.cursor()
        cursor.execute(
            "SELECT * FROM vulnerabilities WHERE published_date >= ? AND published_date <= ?",
            (start_str, end_str)
        )
        rows = cursor.fetchall()
        
        if not rows:
            return {"error": f"未找到发布日期在 {start_date} 至 {end_date} 之间的漏洞数据"}
        
        # 调用通用导入方法处理数据
        stats = self._import_vulnerabilities(rows)
        return stats
    
    def _import_vulnerabilities(self, rows: List[sqlite3.Row]) -> Dict[str, int]:
        """
        导入漏洞数据的通用方法
        
        Args:
            rows: 数据库查询结果
            
        Returns:
            导入统计信息
        """
        stats = {
            "vulnerabilities": 0,
            "components": 0,
            "versions": 0,
            "patches": 0,
            "affects_relations": 0,
            "has_version_relations": 0,
            "fixed_by_relations": 0,
            "has_weakness_relations": 0
        }
        
        # 建立组件和版本索引，避免重复创建
        component_index = set()
        version_index = set()
        
        # 准备批量导入数据
        vuln_nodes = []
        component_nodes = []
        version_nodes = []
        patch_nodes = []
        affects_relations = []
        has_version_relations = []
        fixed_by_relations = []
        has_weakness_relations = []
        
        # 处理每个漏洞
        for row in tqdm(rows, desc="处理漏洞数据"):
            vuln_id = row["id"]
            data = json.loads(row["data"])
            
            # 1. 创建漏洞节点
            vuln_node = self._create_vulnerability_node(data)
            vuln_nodes.append(vuln_node)
            stats["vulnerabilities"] += 1
            
            # 2. 处理受影响的组件和版本
            components, versions, affects = self._process_affected_components(vuln_id, data)
            
            # 添加组件节点（去重）
            for comp in components:
                if comp.id not in component_index:
                    component_nodes.append(comp)
                    component_index.add(comp.id)
                    stats["components"] += 1
            
            # 添加版本节点（去重）
            for ver in versions:
                ver_id = ver.id
                if ver_id not in version_index:
                    version_nodes.append(ver)
                    version_index.add(ver_id)
                    stats["versions"] += 1
                    
                    # 添加组件-版本关系
                    comp_id = ver_id.split("@")[0]
                    has_version = HasVersionRelation(comp_id, ver_id)
                    has_version_relations.append(has_version)
                    stats["has_version_relations"] += 1
            
            # 添加漏洞-组件关系
            affects_relations.extend(affects)
            stats["affects_relations"] += len(affects)
            
            # 3. 处理补丁和修复关系
            patches, fixed_by = self._process_patches(vuln_id, data)
            patch_nodes.extend(patches)
            fixed_by_relations.extend(fixed_by)
            stats["patches"] += len(patches)
            stats["fixed_by_relations"] += len(fixed_by)
            
            # 4. 处理CWE弱点关系
            weaknesses = self._process_weaknesses(vuln_id, data)
            has_weakness_relations.extend(weaknesses)
            stats["has_weakness_relations"] += len(weaknesses)
            
            # 批量导入（当达到批量大小时）
            if len(vuln_nodes) >= self.batch_size:
                self._batch_import_nodes_and_relations(
                    vuln_nodes, component_nodes, version_nodes, patch_nodes,
                    affects_relations, has_version_relations, fixed_by_relations, has_weakness_relations
                )
                
                # 清空已导入的批次
                vuln_nodes = []
                component_nodes = []
                version_nodes = []
                patch_nodes = []
                affects_relations = []
                has_version_relations = []
                fixed_by_relations = []
                has_weakness_relations = []
        
        # 导入剩余的数据
        if vuln_nodes:
            self._batch_import_nodes_and_relations(
                vuln_nodes, component_nodes, version_nodes, patch_nodes,
                affects_relations, has_version_relations, fixed_by_relations, has_weakness_relations
            )
        
        return stats
    
    def _batch_import_nodes_and_relations(self,
                                       vuln_nodes: List[VulnerabilityNode],
                                       component_nodes: List[ComponentNode],
                                       version_nodes: List[VersionNode],
                                       patch_nodes: List[PatchNode],
                                       affects_relations: List[AffectsRelation],
                                       has_version_relations: List[HasVersionRelation],
                                       fixed_by_relations: List[FixedByRelation],
                                       has_weakness_relations: List[GraphRelation]):
        """
        批量导入节点和关系
        
        Args:
            vuln_nodes: 漏洞节点列表
            component_nodes: 组件节点列表
            version_nodes: 版本节点列表
            patch_nodes: 补丁节点列表
            affects_relations: 影响关系列表
            has_version_relations: 版本关系列表
            fixed_by_relations: 修复关系列表
            has_weakness_relations: 弱点关系列表
        """
        # 批量导入所有节点
        all_nodes = []
        all_nodes.extend(vuln_nodes)
        all_nodes.extend(component_nodes)
        all_nodes.extend(version_nodes)
        all_nodes.extend(patch_nodes)
        
        if all_nodes:
            self.graph_store.batch_create_nodes(all_nodes)
        
        # 批量导入所有关系
        all_relations = []
        all_relations.extend(affects_relations)
        all_relations.extend(has_version_relations)
        all_relations.extend(fixed_by_relations)
        all_relations.extend(has_weakness_relations)
        
        if all_relations:
            self.graph_store.batch_create_relations(all_relations)
    
    def _create_vulnerability_node(self, data: Dict[str, Any]) -> VulnerabilityNode:
        """
        从JSON数据创建漏洞节点
        
        Args:
            data: 漏洞数据
            
        Returns:
            漏洞节点
        """
        vuln_id = data.get("id")
        
        # 提取CVSS评分
        cvss_v3_score = None
        cvss_v2_score = None
        
        if data.get("cvss_v3") and isinstance(data["cvss_v3"], dict):
            cvss_v3_score = data["cvss_v3"].get("base_score")
        
        if data.get("cvss_v2") and isinstance(data["cvss_v2"], dict):
            cvss_v2_score = data["cvss_v2"].get("base_score")
        
        # 创建漏洞节点
        return VulnerabilityNode(
            vuln_id=vuln_id,
            source=data.get("source"),
            title=data.get("title") or vuln_id,
            description=data.get("description"),
            severity=data.get("severity"),
            published_date=self._format_date(data.get("published_date")),
            last_modified_date=self._format_date(data.get("last_modified_date")),
            cvss_v3_score=cvss_v3_score,
            cvss_v2_score=cvss_v2_score
        )
    
    def _process_affected_components(self, 
                                 vuln_id: str,
                                 data: Dict[str, Any]) -> Tuple[List[ComponentNode], List[VersionNode], List[AffectsRelation]]:
        """
        处理受影响的组件和版本
        
        Args:
            vuln_id: 漏洞ID
            data: 漏洞数据
            
        Returns:
            组件节点列表, 版本节点列表, 影响关系列表
        """
        components = []
        versions = []
        affects_relations = []
        
        # 处理NVD格式的漏洞
        if data.get("source") == "NVD" and "configurations" in data:
            return self._process_nvd_configurations(vuln_id, data)
        
        # 处理普通格式的漏洞
        affected_packages = data.get("affected_packages", [])
        for pkg in affected_packages:
            comp_name = pkg.get("name")
            if not comp_name:
                continue
                
            # 创建组件节点
            component = ComponentNode(
                name=comp_name,
                ecosystem=pkg.get("ecosystem"),
                platform=pkg.get("platform")
            )
            components.append(component)
            
            # 创建影响关系
            relation = AffectsRelation(
                vuln_id=vuln_id,
                component_id=comp_name,
                affected_versions=pkg.get("affected_versions", []),
                fixed_versions=pkg.get("fixed_versions", []),
                severity=data.get("severity")
            )
            affects_relations.append(relation)
            
            # 处理版本信息
            for ver_info in pkg.get("versions", []):
                version = ver_info.get("version")
                if not version:
                    continue
                    
                ver_node = VersionNode(
                    component_name=comp_name,
                    version=version,
                    is_vulnerable=ver_info.get("status") == "affected"
                )
                versions.append(ver_node)
        
        return components, versions, affects_relations
    
    def _process_nvd_configurations(self, 
                               vuln_id: str,
                               data: Dict[str, Any]) -> Tuple[List[ComponentNode], List[VersionNode], List[AffectsRelation]]:
        """
        处理NVD格式的配置信息
        
        Args:
            vuln_id: 漏洞ID
            data: 漏洞数据
            
        Returns:
            组件节点列表, 版本节点列表, 影响关系列表
        """
        components = []
        versions = []
        affects_relations = []
        
        # 提取severity信息
        severity = data.get("severity")
        if not severity and data.get("cvss_v3"):
            severity = data["cvss_v3"].get("base_severity")
            
        # 处理NVD配置
        if "cve" in data and "configurations" in data["cve"]:
            configs = data["cve"]["configurations"]
            
            # 遍历所有配置节点
            for config in configs:
                nodes = config.get("nodes", [])
                for node in nodes:
                    cpe_matches = node.get("cpeMatch", [])
                    
                    for cpe_match in cpe_matches:
                        if not cpe_match.get("vulnerable", False):
                            continue
                            
                        # 解析CPE字符串
                        cpe = cpe_match.get("criteria", "")
                        if not cpe or not cpe.startswith("cpe:"):
                            continue
                            
                        # 提取组件信息
                        comp_info = self._parse_cpe_string(cpe)
                        if not comp_info:
                            continue
                            
                        vendor, product = comp_info
                        comp_name = f"{vendor}:{product}" if vendor else product
                        
                        # 创建组件节点
                        component = ComponentNode(
                            name=comp_name,
                            ecosystem="cpe"
                        )
                        components.append(component)
                        
                        # 确定受影响版本范围
                        affected_versions = []
                        
                        # 处理版本范围
                        start_version = None
                        end_version = None
                        
                        if "versionStartIncluding" in cpe_match:
                            start_version = cpe_match["versionStartIncluding"]
                            affected_versions.append(f">={start_version}")
                            
                        if "versionEndExcluding" in cpe_match:
                            end_version = cpe_match["versionEndExcluding"]
                            affected_versions.append(f"<{end_version}")
                            
                        if "versionEndIncluding" in cpe_match:
                            end_version = cpe_match["versionEndIncluding"]
                            affected_versions.append(f"<={end_version}")
                        
                        # 如果没有明确的版本范围，则假设所有版本都受影响
                        if not affected_versions:
                            affected_versions = ["*"]
                        
                        # 创建影响关系
                        relation = AffectsRelation(
                            vuln_id=vuln_id,
                            component_id=comp_name,
                            affected_versions=affected_versions,
                            severity=severity
                        )
                        affects_relations.append(relation)
                        
                        # 创建版本节点（如果有明确的版本）
                        if start_version:
                            start_ver_node = VersionNode(
                                component_name=comp_name,
                                version=start_version,
                                is_vulnerable=True
                            )
                            versions.append(start_ver_node)
                            
                        if end_version:
                            end_ver_node = VersionNode(
                                component_name=comp_name,
                                version=end_version,
                                is_vulnerable=False
                            )
                            versions.append(end_ver_node)
        
        return components, versions, affects_relations
    
    def _process_patches(self, 
                     vuln_id: str,
                     data: Dict[str, Any]) -> Tuple[List[PatchNode], List[FixedByRelation]]:
        """
        处理补丁和修复关系
        
        Args:
            vuln_id: 漏洞ID
            data: 漏洞数据
            
        Returns:
            补丁节点列表, 修复关系列表
        """
        patches = []
        fixed_by_relations = []
        
        # 从引用中提取补丁信息
        references = data.get("references", [])
        patch_count = 0
        
        for ref in references:
            if not isinstance(ref, dict):
                continue
                
            url = ref.get("url")
            if not url:
                continue
                
            # 检查是否是补丁
            is_patch = False
            tags = ref.get("tags", [])
            
            if isinstance(tags, list) and ("Patch" in tags or "patch" in tags or "Fix" in tags or "fix" in tags):
                is_patch = True
            elif "github.com" in url and ("/commit/" in url or "/pull/" in url or "/compare/" in url):
                is_patch = True
            
            if is_patch:
                patch_id = f"patch-{vuln_id}-{patch_count}"
                patch_count += 1
                
                # 创建补丁节点
                patch = PatchNode(
                    patch_id=patch_id,
                    url=url,
                    description=ref.get("source", "")
                )
                patches.append(patch)
                
                # 创建修复关系
                relation = FixedByRelation(
                    vuln_id=vuln_id,
                    patch_id=patch_id
                )
                fixed_by_relations.append(relation)
        
        return patches, fixed_by_relations
    
    def _process_weaknesses(self, vuln_id: str, data: Dict[str, Any]) -> List[GraphRelation]:
        """
        处理CWE弱点关系
        
        Args:
            vuln_id: 漏洞ID
            data: 漏洞数据
            
        Returns:
            弱点关系列表
        """
        weakness_relations = []
        
        # 处理NVD格式
        if data.get("source") == "NVD" and "cve" in data and "weaknesses" in data["cve"]:
            weaknesses = data["cve"]["weaknesses"]
            
            for weakness_info in weaknesses:
                descriptions = weakness_info.get("description", [])
                
                for desc in descriptions:
                    cwe_id = desc.get("value", "")
                    if cwe_id and cwe_id.startswith("CWE-"):
                        # 创建HAS_WEAKNESS关系
                        relation = GraphRelation(
                            source_id=vuln_id,
                            target_id=cwe_id,
                            relation_type=RelationType.HAS_WEAKNESS
                        )
                        weakness_relations.append(relation)
        
        # 处理标准格式
        if "cwe_ids" in data and isinstance(data["cwe_ids"], list):
            for cwe_id in data["cwe_ids"]:
                if cwe_id and isinstance(cwe_id, str):
                    # 标准化CWE ID格式
                    if not cwe_id.startswith("CWE-"):
                        match = re.search(r'(\d+)', cwe_id)
                        if match:
                            cwe_id = f"CWE-{match.group(1)}"
                        else:
                            continue
                    
                    # 创建HAS_WEAKNESS关系
                    relation = GraphRelation(
                        source_id=vuln_id,
                        target_id=cwe_id,
                        relation_type=RelationType.HAS_WEAKNESS
                    )
                    weakness_relations.append(relation)
        
        return weakness_relations
    
    def _parse_cpe_string(self, cpe: str) -> Optional[Tuple[str, str]]:
        """
        解析CPE字符串，提取供应商和产品信息
        
        Args:
            cpe: CPE字符串
            
        Returns:
            (供应商, 产品)元组，如果解析失败则返回None
        """
        # CPE 2.3格式: cpe:2.3:part:vendor:product:version:update:edition:language:...
        parts = cpe.split(":")
        
        if len(parts) >= 5:
            vendor = parts[3]
            product = parts[4]
            
            # 规范化名称
            vendor = vendor if vendor != "*" else ""
            product = product if product != "*" else ""
            
            if not product:
                return None
                
            return vendor, product
            
        return None
    
    def _format_date(self, date_str: Optional[str]) -> Optional[str]:
        """
        格式化日期字符串
        
        Args:
            date_str: 日期字符串
            
        Returns:
            格式化后的日期字符串
        """
        if not date_str:
            return None
            
        # 已经是字符串格式，直接返回
        if isinstance(date_str, str):
            return date_str
            
        # 如果是datetime对象，转换为ISO格式字符串
        if isinstance(date_str, datetime):
            return date_str.isoformat()
            
        return str(date_str) 