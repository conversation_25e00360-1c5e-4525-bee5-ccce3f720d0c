#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
引用获取和处理模块

负责获取和处理CVE引用信息，包括从NVD和其他来源获取引用链接，
以及对引用进行分类、优先级排序和准备工作。
"""

import os
import json
import logging
import requests
import re
from typing import List, Dict, Set, Any, Optional, Tuple
from urllib.parse import urlparse
from datetime import datetime, timedelta

from ...collectors.nvd import NVDCollector
from ...collectors.github import GitHubCollector

logger = logging.getLogger(__name__)

class CVEReferenceFetcher:
    """CVE引用信息获取器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化CVE引用信息获取器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        
        # 如果未提供配置文件路径，尝试查找
        if not self.config_path:
            # 尝试获取项目根目录
            try:
                import sys
                project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            except:
                project_root = os.getcwd()
                
            possible_paths = [
                os.path.join(project_root, "src/data_collection/config/sources.json"),
                os.path.join(project_root, "config/sources.json"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "../../../config/sources.json"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config/sources.json"),
                "/home/<USER>/src/data_collection/config/sources.json"
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    self.config_path = path
                    logger.info(f"找到配置文件: {path}")
                    break
        
        if not self.config_path or not os.path.exists(self.config_path):
            raise FileNotFoundError("无法找到配置文件，请指定正确的配置文件路径")
        
        self.config = self._load_config()
        
        # 确保NVD配置存在
        if 'sources' not in self.config or 'NVD' not in self.config['sources']:
            raise ValueError("配置文件中缺少NVD源配置")
            
        self.collector = NVDCollector(self.config['sources']['NVD'])
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            logger.info(f"正在加载配置文件: {self.config_path}")
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise
            
    def get_references_by_cve_id(self, 
                               cve_id: str,
                               save_json: bool = True,
                               output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        获取指定CVE ID的引用信息
        
        Args:
            cve_id: CVE ID（例如：CVE-2024-1234）
            save_json: 是否保存JSON文件
            output_dir: 保存目录
            
        Returns:
            Dict: CVE引用信息
        """
        logger.info(f"获取 {cve_id} 的引用信息")
        
        # 准备保存目录
        if save_json and output_dir:
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"将保存CVE到目录: {output_dir}")
        
        # 构建API参数
        params = {
            'cveId': cve_id
        }
        
        # 发送API请求
        response_data = self.collector.make_api_request('', params=params)
        
        if not response_data.get('vulnerabilities'):
            logger.warning(f"未找到 {cve_id} 的信息")
            return {}
            
        # 提取第一个（也是唯一的）结果
        vuln_data = self.collector.clean_data(response_data['vulnerabilities'][0])
        
        result = {
            'cve_id': vuln_data.get('id'),
            'published_date': vuln_data.get('published_date'),
            'last_modified_date': vuln_data.get('last_modified_date'),
            'descriptions': vuln_data.get('descriptions', []),
            'references': vuln_data.get('references', [])
        }
        
        # 保存JSON文件
        if save_json and output_dir:
            cve_filename = f"{cve_id}.json"
            cve_file_path = os.path.join(output_dir, cve_filename)
            
            with open(cve_file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
                
            logger.info(f"已保存CVE信息到文件: {cve_file_path}")
            
        return result

    def get_github_information_for_cve(self, cve_id: str, github_collector: GitHubCollector) -> List[Dict[str, Any]]:
        """
        使用GitHub API获取与CVE相关的安全公告信息
        
        Args:
            cve_id: CVE ID
            github_collector: GitHub采集器实例
            
        Returns:
            List[Dict]: 与CVE相关的安全公告信息列表
        """
        try:
            # 测试连接
            if not github_collector.test_connection():
                logger.warning("GitHub API连接测试失败，可能是令牌无效或网络问题")
                return []
            
            logger.info(f"正在从GitHub API获取与 {cve_id} 相关的安全公告...")
            
            # 构建GraphQL查询，搜索含有指定CVE ID的安全公告
            query = """
            query FindByCve($cveId: String!) {
              securityAdvisories(first: 10, orderBy: {field: PUBLISHED_AT, direction: DESC}) {
                nodes {
                  ghsaId
                  summary
                  description
                  severity
                  identifiers {
                    type
                    value
                  }
                  references {
                    url
                  }
                  vulnerabilities(first: 5) {
                    nodes {
                      package {
                        ecosystem
                        name
                      }
                    }
                  }
                }
                pageInfo {
                  hasNextPage
                  endCursor
                }
              }
              search(query: $cveId, type: ADVISORY, first: 10) {
                nodes {
                  ... on SecurityAdvisory {
                    ghsaId
                    summary
                    severity
                    identifiers {
                      type
                      value
                    }
                    references {
                      url
                    }
                  }
                }
              }
            }
            """
            
            # 准备变量
            variables = {
                "cveId": cve_id  # 例如 "CVE-2021-44228"
            }
            
            # 发送GraphQL查询 - 修复API URL问题
            headers = {'Authorization': f'Bearer {github_collector.api_key}',
                      'Accept': 'application/vnd.github.v4+json'}
            
            # 获取verify_ssl设置，如果不存在则默认为False
            verify_ssl = getattr(github_collector, 'verify_ssl', False)
            logger.info(f"使用SSL验证: {verify_ssl}")
            
            # 直接使用正确的URL，不加末尾斜杠
            response = requests.post(
                'https://api.github.com/graphql',
                headers=headers,
                json={'query': query, 'variables': variables},
                verify=verify_ssl,
                timeout=github_collector.timeout
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"GitHub API返回错误状态码: {response.status_code}")
                logger.error(f"错误信息: {response.text}")
                return []
            
            # 解析响应数据
            response_data = response.json()
            
            if not response_data or 'data' not in response_data:
                logger.warning("GitHub API未返回有效数据")
                return []
            
            # 提取安全公告数据
            advisories = []
            try:
                # 处理securityAdvisories部分
                if ('data' in response_data and 
                    'securityAdvisories' in response_data['data'] and 
                    'nodes' in response_data['data']['securityAdvisories']):
                    
                    for node in response_data['data']['securityAdvisories']['nodes']:
                        # 检查这个安全公告是否与指定的CVE ID相关
                        is_relevant = False
                        for identifier in node.get('identifiers', []):
                            if identifier.get('type') == 'CVE' and identifier.get('value') == cve_id:
                                is_relevant = True
                                break
                        
                        if is_relevant:
                            # 处理这个安全公告
                            advisory = github_collector.clean_data(node)
                            if advisory:
                                advisories.append(advisory)
                                logger.info(f"找到与 {cve_id} 相关的GitHub安全公告: {advisory.get('id')}")
                
                # 处理search部分
                if ('data' in response_data and 
                    'search' in response_data['data'] and 
                    'nodes' in response_data['data']['search']):
                    
                    for node in response_data['data']['search']['nodes']:
                        if not node:  # 空节点跳过
                            continue
                            
                        # 在search结果中，已经是通过CVE ID过滤过的，可以直接处理
                        advisory = {}
                        advisory['id'] = node.get('ghsaId', '')
                        advisory['summary'] = node.get('summary', '')
                        advisory['severity'] = node.get('severity', '').lower()
                        
                        # 收集引用
                        references = []
                        for ref in node.get('references', []):
                            url = ref.get('url', '')
                            if url:
                                references.append({'url': url, 'type': 'advisory'})
                        advisory['references'] = references
                        
                        # 收集CVE ID
                        cve_ids = []
                        for identifier in node.get('identifiers', []):
                            if identifier.get('type') == 'CVE':
                                cve_val = identifier.get('value', '')
                                if cve_val:
                                    cve_ids.append(cve_val)
                        advisory['cve_ids'] = cve_ids
                        
                        # 只添加包含搜索CVE ID的记录
                        if cve_id in advisory.get('cve_ids', []):
                            advisories.append(advisory)
                            logger.info(f"在搜索结果中找到与 {cve_id} 相关的GitHub安全公告: {advisory.get('id')}")
                        
            except Exception as e:
                logger.error(f"解析GitHub安全公告数据时出错: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
            
            logger.info(f"从GitHub API获取到 {len(advisories)} 个与 {cve_id} 相关的安全公告")
            return advisories
        
        except Exception as e:
            logger.error(f"查询GitHub API时出错: {str(e)}")
            return []

    def get_references_by_timerange(self, 
                                  start_date: datetime, 
                                  end_date: datetime,
                                  date_type: str = "published",
                                  output_file: Optional[str] = None,
                                  save_individual: bool = False,
                                  output_dir: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内的CVE引用信息
        
        Args:
            start_date: 开始时间
            end_date: 结束时间
            date_type: 日期类型，可选 "published" 或 "modified"
            output_file: 输出文件路径（可选）
            save_individual: 是否将每个CVE保存为单独的文件
            output_dir: 单独CVE文件的保存目录
            
        Returns:
            CVE引用信息列表
        """
        # 检查日期范围是否超过120天
        date_diff = (end_date - start_date).days
        if date_diff > 120:
            logger.warning(f"日期范围超过120天({date_diff}天)，NVD API限制最多查询120天的数据，将自动截断")
            end_date = start_date + timedelta(days=120)
            
        logger.info(f"获取 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的CVE引用信息 (按{date_type}日期)")
        
        # 准备保存目录
        if save_individual:
            if output_dir is None:
                # 修复路径构建，避免重复src路径
                default_cve_dir = os.path.join("/home/<USER>", "src", "data_collection", "data", "CVE")
                output_dir = default_cve_dir
            
            # 确保目录存在
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"将单独保存CVE到目录: {output_dir}")
        
        # 构建API参数 - 智能策略：优先尝试时间范围参数，失败则降级到分页获取
        params = {
            'resultsPerPage': 2000,  # 每页最大结果数
            'noRejected': '',        # 排除被拒绝的CVE
            'startIndex': 0          # 从第一页开始
        }

        # 方案1：不使用API密钥，直接使用时间范围参数（已验证可行）
        use_date_filter = True
        if date_type == "published":
            params['pubStartDate'] = start_date.strftime('%Y-%m-%dT00:00:00.000Z')
            params['pubEndDate'] = end_date.strftime('%Y-%m-%dT23:59:59.999Z')
            logger.info(f"🎯 使用pubStartDate/pubEndDate参数（无API密钥模式）")
        else:
            params['lastModStartDate'] = start_date.strftime('%Y-%m-%dT00:00:00.000Z')
            params['lastModEndDate'] = end_date.strftime('%Y-%m-%dT23:59:59.999Z')
            logger.info(f"🎯 使用lastModStartDate/lastModEndDate参数（无API密钥模式）")

        logger.info(f"📊 API请求详情:")
        logger.info(f"  - 基础URL: {self.collector.url}")
        logger.info(f"  - 参数: {params}")
        logger.info(f"  - 目标时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        logger.info(f"  - 策略: 不使用API密钥 + 时间范围过滤（方案1）")

        try:
            # 发送API请求
            logger.info("🌐 正在发送NVD API请求...")
            response_data = self.collector.make_api_request('', params=params)

            # 记录响应信息
            total_results = response_data.get('totalResults', 0)
            logger.info(f"✅ API响应成功: 总结果数: {total_results}")

            if total_results == 0:
                logger.warning("⚠️ 未找到任何CVE数据")
                return []

            logger.info(f"📈 计划获取策略:")
            logger.info(f"  - 总CVE数量: {total_results:,}")
            logger.info(f"  - 每页数量: {params['resultsPerPage']}")
            logger.info(f"  - 预计页数: {(total_results + params['resultsPerPage'] - 1) // params['resultsPerPage']}")
            logger.info(f"  - 将在本地过滤时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
                
            # 获取并处理所有页的数据
            all_vulnerabilities = []
            start_index = 0
            results_per_page = params['resultsPerPage']
            
            while start_index < total_results:
                # 对于第一页，我们已经有了数据
                if start_index == 0:
                    vulnerabilities = response_data.get('vulnerabilities', [])
                else:
                    # 更新起始索引
                    params['startIndex'] = start_index
                    logger.info(f"获取下一页数据: startIndex={start_index}")
                    
                    # 发送请求获取下一页 - 添加重试机制
                    max_retries = 3
                    retry_delay = 5
                    for attempt in range(max_retries):
                        try:
                            page_data = self.collector.make_api_request('', params=params)
                            vulnerabilities = page_data.get('vulnerabilities', [])
                            break
                        except Exception as e:
                            if attempt < max_retries - 1:
                                logger.warning(f"API请求失败(尝试 {attempt+1}/{max_retries}): {str(e)}")
                                logger.info(f"等待 {retry_delay} 秒后重试...")
                                import time
                                time.sleep(retry_delay)
                                retry_delay *= 2  # 指数退避
                            else:
                                logger.error(f"API请求最终失败: {str(e)}")
                                raise
                
                # 检查是否获取到数据
                if not vulnerabilities:
                    logger.warning(f"页面 {start_index//results_per_page + 1} 没有数据，停止获取")
                    break
                    
                # 添加到总列表
                all_vulnerabilities.extend(vulnerabilities)
                logger.info(f"已获取 {len(all_vulnerabilities)}/{total_results} 条数据")
                
                # 更新起始索引
                start_index += len(vulnerabilities)
                
                # 检查是否已获取所有数据
                if start_index >= total_results:
                    logger.info("已获取所有数据，停止分页请求")
                    break
            
            # 处理数据
            results = []
            saved_count = 0
            
            # 按日期过滤CVE
            filtered_vulnerabilities = []
            for vuln in all_vulnerabilities:
                try:
                    vuln_data = self.collector.clean_data(vuln)
                    cve_id = vuln_data.get('id')
                    
                    if not cve_id:
                        logger.warning("发现没有CVE ID的数据，跳过")
                        continue
                    
                    # 本地日期过滤
                    if not self._is_cve_in_date_range(vuln_data, start_date, end_date, date_type):
                        continue
                    
                    filtered_vulnerabilities.append(vuln)
                except Exception as e:
                    logger.error(f"处理CVE数据失败: {str(e)}")
                    continue
            
            logger.info(f"日期过滤后剩余 {len(filtered_vulnerabilities)} 个CVE (原始: {len(all_vulnerabilities)})")
            
            for vuln in filtered_vulnerabilities:
                try:
                    vuln_data = self.collector.clean_data(vuln)
                    cve_id = vuln_data.get('id')
                        
                    cve_info = {
                        'cve_id': cve_id,
                        'published_date': vuln_data.get('published_date'),
                        'last_modified_date': vuln_data.get('last_modified_date'),
                        'descriptions': vuln_data.get('descriptions', []),
                        'references': vuln_data.get('references', [])
                    }
                    
                    results.append(cve_info)
                    
                    # 保存单独的CVE文件
                    if save_individual and output_dir:
                        # 使用CVE ID作为文件名
                        cve_filename = f"{cve_id}.json"
                        cve_file_path = os.path.join(output_dir, cve_filename)
                        
                        # 保存到文件
                        with open(cve_file_path, 'w', encoding='utf-8') as f:
                            json.dump(cve_info, f, indent=2, ensure_ascii=False)
                            
                        saved_count += 1
                        
                        # 定期记录保存进度
                        if saved_count % 10 == 0:
                            logger.info(f"已单独保存 {saved_count} 个CVE文件")
                    
                except Exception as e:
                    logger.error(f"处理CVE数据失败: {str(e)}")
                    continue
            
            logger.info(f"成功处理 {len(results)} 条CVE数据")
            if save_individual:
                logger.info(f"已单独保存 {saved_count} 个CVE文件到目录: {output_dir}")
            
            # 输出到汇总文件（如果指定）
            if output_file:
                self._save_to_file(results, output_file)
                
            return results
            
        except Exception as e:
            logger.error(f"❌ 获取CVE数据失败: {str(e)}")
            logger.error(f"🔍 错误类型: {type(e).__name__}")

            # 检查是否是HTTP错误
            if hasattr(e, 'response'):
                logger.error(f"📊 HTTP状态码: {e.response.status_code}")
                logger.error(f"🌐 请求URL: {e.response.url}")
                if hasattr(e.response, 'text'):
                    logger.error(f"📝 响应内容: {e.response.text[:500]}")

            import traceback
            logger.error("📋 完整错误堆栈:")
            logger.error(traceback.format_exc())
            return []
    
    def _is_cve_in_date_range(self, vuln_data: Dict[str, Any], start_date: datetime, end_date: datetime, date_type: str = "published") -> bool:
        """
        检查CVE是否在指定日期范围内
        
        Args:
            vuln_data: CVE数据
            start_date: 开始日期
            end_date: 结束日期  
            date_type: 日期类型 ("published" 或 "modified")
            
        Returns:
            bool: True如果在范围内，False否则
        """
        try:
            # 获取相应的日期字段
            if date_type == "published":
                date_str = vuln_data.get('published_date') or vuln_data.get('published')
            else:  # modified
                date_str = vuln_data.get('last_modified_date') or vuln_data.get('lastModified')
            
            if not date_str:
                return False
            
            # 解析日期字符串
            if isinstance(date_str, str):
                # 处理不同的日期格式
                if 'T' in date_str:
                    # ISO格式: 2023-08-23T00:00:00.000Z
                    cve_date = datetime.fromisoformat(date_str.replace('Z', '+00:00').replace('.000', ''))
                else:
                    # 简单格式: 2023-08-23
                    cve_date = datetime.strptime(date_str[:10], '%Y-%m-%d')
            elif isinstance(date_str, datetime):
                cve_date = date_str
            else:
                return False
            
            # 去除时区信息进行比较
            if cve_date.tzinfo:
                cve_date = cve_date.replace(tzinfo=None)
            
            # 检查是否在范围内
            return start_date <= cve_date <= end_date
            
        except Exception as e:
            logger.warning(f"日期解析失败: {date_str}, 错误: {str(e)}")
            return False
            
    def _save_to_file(self, data: List[Dict[str, Any]], output_file: str) -> None:
        """保存数据到文件"""
        try:
            # 确保目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                
            # 保存到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"成功保存数据到文件: {output_file}")
        except Exception as e:
            logger.error(f"保存数据到文件失败: {str(e)}")

def prepare_references_for_tracer(references_by_source: Dict[str, List[str]]) -> tuple:
    """
    为跟踪器准备引用链接和来源信息
    
    Args:
        references_by_source: 按来源分类的引用链接
        
    Returns:
        tuple: (初始引用链接列表, 来源列表)
    """
    initial_references = []
    sources = []
    
    # 整合所有引用链接，保留原始来源关系
    for source, refs in references_by_source.items():
        for url in refs:
            initial_references.append(url)
            sources.append(source)
            
    return initial_references, sources

def get_initial_references(cve_id: str, data_dir: str, config_path: Optional[str] = None, use_cache: bool = True) -> Dict[str, List[str]]:
    """
    从多个来源获取初始引用链接，并进行去重和分类处理
    
    此函数是补丁跟踪的起点，负责获取和处理初始引用链接：
    
    1. 数据获取：
       - 优先检查本地缓存文件
       - 如果没有缓存或不使用缓存，从NVD API获取数据
       - 去重引用链接，避免重复处理
       
    2. 引用分类：
       - 将引用按照来源平台分类（NVD、GitHub、GitLab等）
       - 平台识别使用简单的关键词匹配机制
       - 关键词匹配允许识别子域名上的平台实例（如gitlab.gnome.org归为GITLAB）
       
    3. 引用优先级排序：
       - 根据引用的可信度和重要性分配优先级
       - 高优先级的引用会在后续分析中得到更多关注
    
    返回的引用按来源分组，便于后续处理和统计。
    
    Args:
        cve_id: CVE ID，如"CVE-2021-44228"
        data_dir: 数据保存目录
        config_path: 配置文件路径
        use_cache: 是否使用缓存文件(如果存在)
        
    Returns:
        Dict: 按来源分类的引用链接字典（每个来源内的链接已去重）
    """
    # 创建CVE专属目录
    cve_data_dir = os.path.join(data_dir, cve_id.replace('-', '_'))
    os.makedirs(cve_data_dir, exist_ok=True)
    
    # 初始化各来源的引用集合（使用集合自动去重）
    references_by_source = {
        "NVD": set(),
        "GITHUB": set(),
        "APACHE": set(),
        "REDHAT": set(),
        "DEBIAN": set(),
        "OTHER": set(),
        "GITLAB": set()
    }
    
    # 检查是否已有缓存文件
    cve_cache_file = os.path.join(cve_data_dir, f"{cve_id}.json")
    if use_cache and os.path.exists(cve_cache_file):
        try:
            logger.info(f"找到缓存文件: {cve_cache_file}，直接加载")
            with open(cve_cache_file, 'r', encoding='utf-8') as f:
                nvd_data = json.load(f)
            
            logger.info(f"使用缓存数据处理引用链接")
        except Exception as e:
            logger.error(f"读取缓存文件失败: {str(e)}")
            # 缓存文件读取失败，继续请求API
            nvd_data = None
    else:
        nvd_data = None
        
    # 如果没有缓存数据，从NVD获取数据
    if nvd_data is None:
        # 从NVD获取引用链接
        try:
            logger.info(f"从NVD获取 {cve_id} 的引用信息")
            
            # 如果未指定配置文件路径，尝试自动查找
            if not config_path:
                try:
                    import sys
                    project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                except:
                    project_root = os.getcwd()
                    
                possible_config_paths = [
                    os.path.join(project_root, "src/data_collection/config/sources.json"),
                    os.path.join(project_root, "config/sources.json"),
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), "../../../config/sources.json"),
                    os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config/sources.json"),
                    "/home/<USER>/src/data_collection/config/sources.json"
                ]
                
                for path in possible_config_paths:
                    if os.path.exists(path):
                        config_path = path
                        logger.info(f"自动找到配置文件: {config_path}")
                        break
                        
                if not config_path:
                    logger.warning("无法找到配置文件，尝试使用默认设置")
            
            fetcher = CVEReferenceFetcher(config_path)
            
            # 获取并清洗引用数据（去重）
            original_nvd_data = fetcher.get_references_by_cve_id(
                cve_id=cve_id,
                save_json=False,  # 先不保存，我们会在去重后保存
                output_dir=cve_data_dir
            )
            
            # 如果成功获取数据，进行引用去重
            if original_nvd_data and 'references' in original_nvd_data:
                # 使用字典去重引用（以URL为键）
                unique_refs = {}
                for ref in original_nvd_data['references']:
                    url = ref.get('url', '')
                    if url and url not in unique_refs:
                        unique_refs[url] = ref
                
                # 替换原始引用列表为去重后的列表
                original_nvd_data['references'] = list(unique_refs.values())
                
                # 确保包含必要的CVE元数据
                if 'published_date' not in original_nvd_data and 'published' in original_nvd_data:
                    original_nvd_data['published_date'] = original_nvd_data['published']
                    
                # 保存去重后的数据
                with open(cve_cache_file, 'w', encoding='utf-8') as f:
                    json.dump(original_nvd_data, f, indent=2, ensure_ascii=False)
                
                logger.info(f"已保存去重后的CVE信息到文件: {cve_cache_file}")
                logger.info(f"原始引用数: {len(original_nvd_data.get('references', []))}, 去重后: {len(unique_refs)}")
                
                nvd_data = original_nvd_data
            else:
                logger.warning(f"没有获取到CVE引用数据")
                nvd_data = {'references': []}
                
        except Exception as e:
            logger.error(f"从NVD获取引用信息失败: {str(e)}")
            # 确保至少有NVD链接
            references_by_source["NVD"].add(f"https://nvd.nist.gov/vuln/detail/{cve_id}")
            # 转换集合为列表
            return {k: list(v) for k, v in references_by_source.items()}
    
    # 提取引用链接并按来源分类
    for ref in nvd_data.get('references', []):
        url = ref.get('url', '')
        if not url:
            continue
            
        # 根据URL归类来源（使用集合自动去重）
        # 禁用GitHub分类，将GitHub链接归类到OTHER
        if "gitlab" in url:
            references_by_source["GITLAB"].add(url)
        elif "apache" in url:
            references_by_source["APACHE"].add(url)
        elif "redhat" in url:
            references_by_source["REDHAT"].add(url)
        elif "debian" in url:
            references_by_source["DEBIAN"].add(url)
        elif "nvd.nist.gov" in url:
            references_by_source["NVD"].add(url)
        else:
            # 将GitHub、UBUNTU和MITRE链接放入OTHER类别（GitHub已禁用）
            references_by_source["OTHER"].add(url)
            
    # 确保NVD至少有一个引用
    if not references_by_source["NVD"]:
        references_by_source["NVD"].add(f"https://nvd.nist.gov/vuln/detail/{cve_id}")
    
    # 从GitHub API获取其他相关链接
    try:
        if config_path:
            # 加载配置
            with open(config_path, 'r') as f:
                config = json.load(f)
                
            # 如果配置了GitHub API令牌
            if 'sources' in config and 'GitHub' in config['sources']:
                logger.info(f"尝试使用GitHub API获取 {cve_id} 的相关信息")
                
                # 确保fetcher已初始化
                if 'fetcher' not in locals():
                    fetcher = CVEReferenceFetcher(config_path)
                
                # 初始化GitHub采集器并配置verify_ssl参数
                github_config = config['sources']['GitHub'].copy()
                github_config['verify_ssl'] = use_cache  # 与use_cache相同逻辑，默认为True
                github_collector = GitHubCollector(github_config)
                
                # 搜索GitHub安全公告获取与CVE相关的信息
                github_data = fetcher.get_github_information_for_cve(cve_id, github_collector)
                
                if github_data:
                    # 处理获取到的GitHub信息，提取URL添加到引用集合
                    for item in github_data:
                        # 添加安全公告URL
                        ghsa_id = item.get('id')
                        if ghsa_id:
                            advisory_url = f"https://github.com/advisories/{ghsa_id}"
                            references_by_source["GITHUB"].add(advisory_url)
                        
                        # 添加参考链接
                        for ref in item.get('references', []):
                            ref_url = ref.get('url', '')
                            if ref_url and "github.com" in ref_url:
                                references_by_source["GITHUB"].add(ref_url)
                        
                        # 如果有受影响的包，添加其仓库链接
                        for package in item.get('affected_packages', []):
                            if package.get('ecosystem') == 'GITHUB':
                                package_name = package.get('name', '')
                                if package_name and '/' in package_name:
                                    repo_url = f"https://github.com/{package_name}"
                                    references_by_source["GITHUB"].add(repo_url)
                    
                    logger.info(f"使用GitHub API添加了 {len(references_by_source['GITHUB'])} 个GitHub引用链接")
                else:
                    logger.info(f"未在GitHub找到与 {cve_id} 相关的安全公告")
            else:
                logger.info("未配置GitHub API，跳过GitHub数据获取")
    except Exception as e:
        logger.error(f"从GitHub API获取信息失败: {str(e)}")
    
    # 转换集合为列表
    return {k: list(v) for k, v in references_by_source.items()}

def prioritize_initial_references(initial_references: List[str], sources: List[str], min_priority: str = 'very_low') -> tuple:
    """
    对初始引用链接按照重要性进行优先级排序，同时保留来源信息
    
    Args:
        initial_references: 初始引用链接列表
        sources: 初始引用来源列表
        min_priority: 最低优先级级别('critical', 'high', 'medium', 'low', 'very_low')
        
    Returns:
        tuple: (优先级排序后的引用链接列表, 对应的来源列表)
    """
    if not initial_references:
        return [], []
    
    # 将URL与来源配对
    url_source_pairs = list(zip(initial_references, sources))
    
    # 创建优先级分组
    priority_groups = {
        'critical': [],  # 最高优先级: 补丁提交、PR
        'high': [],      # 高优先级: 项目官方资源
        'medium': [],    # 中等优先级: issue链接
        'low': [],       # 低优先级: 一般链接
        'very_low': []   # 最低优先级: 一般不太相关链接
    }
    
    # 定义URL模式及其优先级
    # 1. 提交/补丁模式 (最高优先级)
    patch_patterns = [
        r'github\.com/[^/]+/[^/]+/commit/[0-9a-f]{7,40}',
        r'github\.com/[^/]+/[^/]+/pull/\d+/commits/[0-9a-f]{7,40}',
        r'github\.com/[^/]+/[^/]+/pull/\d+/commit/[0-9a-f]{7,40}',
        r'gitlab\.com/[^/]+/[^/]+/commit/[0-9a-f]{7,40}',
        r'/svn/[^/]+/trunk/[^/]+',
        r'/gitea.+/commit/[0-9a-f]{7,40}',
        r'^https?://(?!github).+/git/.+/commit/[0-9a-f]',
        r'\.diff$',
        r'\.patch$'
    ]
    
    # 2. 项目PR/issue模式 (高优先级)
    high_patterns = [
        r'github\.com/[^/]+/[^/]+/pull/\d+$',
        r'github\.com/[^/]+/[^/]+/pull/\d+[?#]',
        r'github\.com/[^/]+/[^/]+/issues/\d+$',
        r'github\.com/[^/]+/[^/]+/issues/\d+[?#]',
        r'gitlab\.com/[^/]+/[^/]+/merge_requests/\d+',
        r'gitlab\.com/[^/]+/[^/]+/issues/\d+',
        r'jira\..*?/browse/[A-Z]+-\d+',
        r'issues\.apache\.org/jira/browse/[A-Z]+-\d+',
        r'bugs\..*?/show_bug\.cgi\?id=\d+'
    ]
    
    # 3. 特定安全平台链接 (高优先级)
    security_platforms = [
        'apache.org/security',
        'github.com/advisories',
        'nvd.nist.gov/vuln/detail',
        'cve.mitre.org/cgi-bin/cvename.cgi',
        'access.redhat.com/security',
        'security.debian.org',
        'ubuntu.com/security/notices',
        'kb.cert.org/vuls/id/',  # CERT漏洞公告
        '/vuls/id/'             # 其他CERT漏洞公告格式
    ]
    
    # 4. 中等优先级
    medium_patterns = [
        r'github\.com/[^/]+/[^/]+$',  # GitHub仓库
        r'github\.com/[^/]+/[^/]+[?#]',
        r'lists\..*?/archives/',  # 邮件列表存档
        r'mail-archive\.com',
        r'security\..*?/advisories',
        r'/advisory/',
        r'/cve/',
        r'/vuln/',
        r'/security/'
    ]
    
    # 5. 低优先级 (其他一般链接)
    low_patterns = [
        r'blog\..*?',
        r'article',
        r'news',
        r'forum',
        r'community',
        r'twitter\.com',
        r'\.pdf$'
    ]
    
    # 6. 最低优先级 (通常不相关)
    very_low_patterns = [
        r'download',
        r'asset',
        r'static',
        r'support\..*?',
        r'help\..*?',
        r'doc\..*?',
        r'docs\..*?',
        r'/doc/',
        r'/docs/'
    ]
    
    # 分组URL
    for url, source in url_source_pairs:
        # 默认为低优先级
        assigned_group = 'low'
        
        # 检查是否匹配最高优先级模式
        for pattern in patch_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                assigned_group = 'critical'
                break
                
        # 如果不是最高优先级，检查其他优先级
        if assigned_group != 'critical':
            # 检查高优先级模式
            for pattern in high_patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    assigned_group = 'high'
                    break
                    
            # 检查安全平台
            if assigned_group != 'high':
                for platform in security_platforms:
                    if platform in url:
                        assigned_group = 'high'
                        break
            
            # 检查中等优先级模式
            if assigned_group != 'high':
                for pattern in medium_patterns:
                    if re.search(pattern, url, re.IGNORECASE):
                        assigned_group = 'medium'
                        break
            
            # 检查低优先级模式
            if assigned_group == 'low':
                for pattern in low_patterns:
                    if re.search(pattern, url, re.IGNORECASE):
                        assigned_group = 'low'
                        break
                        
            # 检查最低优先级模式
            for pattern in very_low_patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    assigned_group = 'very_low'
                    break
        
        # 源代码平台优先级提升
        if source in ["GITHUB", "GITLAB", "APACHE"] and assigned_group not in ['critical', 'high']:
            assigned_group = 'medium'  # 提升到至少中等优先级
        
        # 基于来源的特殊提升
        if source == "GITHUB" and "commit" in url:
            assigned_group = 'critical'  # GitHub提交是最高优先级
        
        # 确保保存URL和原始来源的关联关系
        priority_groups[assigned_group].append((url, source))
    
    # 组合结果，按照优先级顺序
    prioritized_references = []
    prioritized_sources = []
    
    # 优先级级别从高到低
    priority_levels = ['critical', 'high', 'medium', 'low', 'very_low']
    
    # 找到最低优先级的索引
    try:
        min_priority_index = priority_levels.index(min_priority)
    except ValueError:
        # 如果指定的优先级无效，使用全部优先级
        min_priority_index = len(priority_levels) - 1
        logger.warning(f"无效的最低优先级: {min_priority}，使用所有优先级")
    
    # 只包含指定优先级及更高的链接
    included_priorities = priority_levels[:min_priority_index + 1]
    
    for group in included_priorities:
        for url, source in priority_groups[group]:
            prioritized_references.append(url)
            # 保留原始来源信息，确保节点构建时能正确关联
            prioritized_sources.append(source)
    
    # 记录统计信息
    all_stats = {group: len(items) for group, items in priority_groups.items()}
    included_stats = {group: len(items) for group, items in priority_groups.items() if group in included_priorities}
    
    logger.info(f"引用优先级分组统计(全部): {all_stats}")
    logger.info(f"引用优先级分组统计(已包含): {included_stats}")
    logger.info(f"总计: 全部{sum(all_stats.values())}个链接中选用了{sum(included_stats.values())}个优先级为{'/'.join(included_priorities)}的链接")
    
    # 输出详细的引用来源信息
    source_counts = {}
    for s in prioritized_sources:
        source_counts[s] = source_counts.get(s, 0) + 1
    
    logger.info(f"选中的引用按来源分布: {source_counts}")
    
    return prioritized_references, prioritized_sources

# 这里将添加其他函数:
# 1. get_initial_references函数
# 2. get_github_information_for_cve函数
# 3. prepare_references_for_tracer函数
# 4. prioritize_initial_references函数
