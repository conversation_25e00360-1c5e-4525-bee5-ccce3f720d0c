#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
补丁跟踪器模块 - 用于从CVE引用中发现高置信度补丁

此模块提供了一套工具，用于从CVE引用中构建引用网络，并识别高置信度的补丁。
主要组件包括:
- ReferenceNode: 引用网络中的节点
- ReferenceNetwork: 引用网络图结构
- ReferenceAnalyzer: 引用解析和网络构建
- PatchIdentifier: 补丁识别和评分
- PatchTracer: 主控制器

使用示例:
```python
from data_collection.services.patch_tracer import PatchTracer

tracer = PatchTracer()
result = tracer.trace("CVE-2021-44228", [
    "https://nvd.nist.gov/vuln/detail/CVE-2021-44228",
    "https://github.com/apache/logging-log4j2/pull/608"
])
print(f"找到 {len(result['high_confidence_patches'])} 个高置信度补丁")
```
"""

from .tracer import PatchTracer
from .reference_node import ReferenceNode, NodeType
from .reference_network import ReferenceNetwork, Edge
from .reference_analyzer import ReferenceAnalyzer
from .patch_identifier import PatchIdentifier, PatchCandidate
from .reference_fetcher import (
    CVEReferenceFetcher,
    get_initial_references,
    prepare_references_for_tracer,
    prioritize_initial_references
)
from .utils import (
    normalize_url,
    extract_github_repo_from_url,
    extract_commit_id_from_url,
    extract_issue_id_from_url,
    compute_url_hash,
    filter_urls,
    save_json,
    load_json
)

# 设置默认日志级别
import logging
logging.getLogger(__name__).setLevel(logging.INFO)

__all__ = [
    'ReferenceNode',
    'NodeType',
    'ReferenceNetwork',
    'Edge',
    'ReferenceAnalyzer',
    'PatchIdentifier',
    'PatchCandidate',
    'PatchTracer',
    'normalize_url',
    'extract_github_repo_from_url',
    'extract_commit_id_from_url',
    'extract_issue_id_from_url',
    'compute_url_hash',
    'filter_urls',
    'save_json',
    'load_json'
] 