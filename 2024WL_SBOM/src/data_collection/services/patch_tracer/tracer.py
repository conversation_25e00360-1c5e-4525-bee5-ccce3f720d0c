#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
补丁跟踪器主控制器

负责协调补丁跟踪器的整体工作流程，包括引用网络构建、补丁识别和结果输出。
"""

import os
import json
import logging
import time
import argparse
import re
import difflib
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import urllib.parse

from .reference_node import ReferenceNode, NodeType
from .reference_network import ReferenceNetwork, Edge
from .reference_analyzer import ReferenceAnalyzer
from .patch_identifier import PatchIdentifier, PatchCandidate
from .utils import save_json, load_json, normalize_url, extract_github_repo_from_url
# 导入两个用于增强URL识别的函数
from .utils import extract_commit_hash_from_url, find_github_repo_by_commit_hash
import requests

logger = logging.getLogger(__name__)

# 函数装饰器，输出函数运行时间
def log(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        logger.info(f'开始执行 {func.__name__}()')
        ret = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f'结束执行 {func.__name__}()，耗时 {end_time - start_time:.2f} 秒')
        return ret
    return wrapper

class PatchTracer:
    """
    补丁跟踪器主类
    
    负责协调整个补丁跟踪过程，包括创建引用网络、识别补丁和生成报告
    """
    
    def __init__(self, 
                 output_dir: str = "/home/<USER>/data",
                 max_depth: int = 2,
                 request_delay: float = 1.0,
                 timeout: int = 30,
                 github_token: Optional[str] = None,
                 confidence_threshold: float = 0.7,
                 verify_ssl: bool = False,
                 max_retries: int = 8):
        """
        初始化补丁跟踪器
        
        Args:
            output_dir: 输出目录
            max_depth: 最大抓取深度
            request_delay: 请求延迟(秒)
            timeout: 请求超时(秒)
            github_token: GitHub API令牌
            confidence_threshold: 置信度阈值
            verify_ssl: 是否验证SSL证书
            max_retries: 最大重试次数
        """
        self.output_dir = output_dir
        self.max_depth = max_depth
        self.request_delay = request_delay
        self.timeout = timeout
        self.github_token = github_token
        self.confidence_threshold = confidence_threshold
        self.verify_ssl = verify_ssl
        self.max_retries = max_retries
        
        # 记录GitHub令牌信息
        if github_token:
            logger.info(f"已配置GitHub API令牌 ({github_token[:4]}...)")
        else:
            logger.info("未配置GitHub API令牌，某些GitHub资源可能无法访问")
        
        # 创建分析器
        self.reference_analyzer = ReferenceAnalyzer(
            max_depth=max_depth,
            request_delay=request_delay,
            timeout=timeout,
            verify_ssl=verify_ssl,
            max_retries=max_retries
        )
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
    
    @log
    def trace(self, cve_id: str, initial_references: List[str], sources: Optional[List[str]] = None, 
              language: Optional[str] = None, skip_url_check: bool = False) -> Dict[str, Any]:
        """
        跟踪CVE的补丁引用，完成整个CVE补丁发现流程
        
        此方法是补丁跟踪的主要入口，执行完整的补丁发现流程：
        
        1. 准备阶段：
           - 规范化初始引用URL
           - 获取CVE元数据（发布日期、受影响CPE）
        
        2. 构建引用网络阶段：
           - 使用ReferenceAnalyzer创建基于初始引用的网络图
           - 爬取相关链接并建立节点间的关联关系
           - 识别不同类型的节点（源、补丁、问题等）
        
        3. 补丁识别阶段：
           - 使用PatchIdentifier分析网络图
           - 识别潜在的补丁节点
           - 计算每个补丁节点的置信度分数
        
        4. 结果验证阶段：
           - 筛选高置信度补丁
           - 验证补丁URL的可访问性
        
        5. 结果汇总阶段：
           - 生成补丁摘要
           - 构建包含网络统计和补丁信息的完整结果
        
        整个过程在网络构建和补丁识别时会考虑平台特性、节点关系和内容相关性，
        以提供尽可能准确的补丁列表。
        
        Args:
            cve_id: CVE ID，如"CVE-2021-44228"
            initial_references: 初始引用URL列表
            sources: 引用来源列表(可选)
            language: 按编程语言筛选补丁(可选)，如"java,python,c++"
            skip_url_check: 是否跳过URL可访问性检查
            
        Returns:
            Dict: 跟踪结果，包含网络统计、所有补丁和高置信度补丁
        """
        logger.info(f"开始跟踪 {cve_id} 的补丁引用")
        
        # 规范化初始引用URL
        normalized_references = [normalize_url(url) for url in initial_references]
        
        # 获取CVE信息（发布日期和受影响的CPE）
        cve_published_date = None
        cve_affected_cpes = []
        try:
            # 尝试从NVD获取数据（如果可能）
            # 这里简化处理，仅在本地检查是否已有缓存的CVE信息
            cve_dir = cve_id.replace('-', '_')
            cve_cache_file = os.path.join(self.output_dir, cve_dir, f"{cve_id}.json")
            
            if os.path.exists(cve_cache_file):
                # 加载缓存的CVE数据
                try:
                    with open(cve_cache_file, 'r', encoding='utf-8') as f:
                        cve_data = json.load(f)
                        cve_published_date = cve_data.get('published_date')
                        
                        # 尝试提取CPE信息
                        if 'configurations' in cve_data:
                            for config in cve_data.get('configurations', []):
                                for node in config.get('nodes', []):
                                    for cpe_match in node.get('cpe_match', []):
                                        if cpe_match.get('vulnerable', True):
                                            cve_affected_cpes.append(cpe_match.get('cpe_name', ''))
                except Exception as e:
                    logger.error(f"加载CVE缓存数据失败: {str(e)}")
        except Exception as e:
            logger.warning(f"获取CVE信息失败: {str(e)}")
        
        # 步骤1: 构建引用网络
        logger.info("步骤1: 构建引用网络")
        reference_network = self.reference_analyzer.create_reference_network(
            cve_id, normalized_references, sources,
            cve_published_date=cve_published_date,
            cve_affected_cpes=cve_affected_cpes
        )
        
        # 步骤2: 识别补丁
        logger.info("步骤2: 识别潜在补丁")
        patch_identifier = PatchIdentifier(reference_network, self.github_token)
        patch_candidates = patch_identifier.identify_patches()
        
        # 步骤3: 获取高置信度补丁
        logger.info("步骤3: 筛选高置信度补丁")
        high_confidence_patches = patch_identifier.get_high_confidence_patches(
            self.confidence_threshold
        )
        
        # 步骤3.5: 验证高置信度补丁URL是否可访问
        logger.info("步骤3.5: 验证高置信度补丁URL可访问性")
        high_confidence_patch_dicts = [
            {
                "url": p.node.url,
                "confidence": p.confidence,
                "type": p.node.node_type.value,
                "source": p.node.source,
                "metadata": p.metadata
            }
            for p in high_confidence_patches
        ]
        if skip_url_check:
            logger.info("跳过URL可访问性检查，保留所有高置信度补丁")
            verified_high_confidence_patches = high_confidence_patch_dicts
        else:
            verified_high_confidence_patches = self._verify_patch_accessibility(
                high_confidence_patch_dicts, self.reference_analyzer
            )
        
        # 等价补丁搜索将在语言筛选完成后执行，确保使用最终的高置信度补丁集合
        
        # 步骤4: 生成补丁摘要
        logger.info("步骤4: 生成补丁摘要")
        patch_summary = patch_identifier.summarize_patch_candidates(high_confidence_patches)
        
        # 筛选补丁列表（如果指定了语言）
        all_patches_dicts = [
            {
                "url": p.node.url,
                "confidence": p.confidence,
                "type": p.node.node_type.value,
                "source": p.node.source,
                "metadata": p.metadata
            }
            for p in patch_candidates
        ]
        
        # 如果指定了语言，进行筛选
        if language:
            # 分割多个语言并转换为小写
            languages = [lang.strip().lower() for lang in language.split(',')]
            logger.info(f"根据语言筛选补丁: {', '.join(languages)}")
            
            # 筛选补丁
            filtered_all_patches = []
            filtered_high_confidence_patches = []
            
            for patch in all_patches_dicts:
                repo_language = self._get_repository_language(patch['url'])
                
                if repo_language:
                    logger.debug(f"补丁 {patch['url']} 的仓库语言: {', '.join(repo_language.keys())}")
                else:
                    logger.debug(f"补丁 {patch['url']} 无法获取仓库语言信息")
                
                # 检查是否匹配所需语言
                if repo_language:
                    # repo_language是一个字典，键是语言名称，值是代码量
                    repo_langs = [lang.lower() for lang in repo_language.keys()]
                    if any(lang in repo_langs for lang in languages):
                        logger.debug(f"补丁 {patch['url']} 匹配语言: {language}")
                        filtered_all_patches.append(patch)
                        
                        # 同时检查是否为高置信度补丁
                        if patch in verified_high_confidence_patches:
                            filtered_high_confidence_patches.append(patch)
                    else:
                        logger.debug(f"补丁 {patch['url']} 不匹配语言: {language}")
            
            # 更新补丁列表
            logger.info(f"语言筛选后剩余 {len(filtered_all_patches)}/{len(all_patches_dicts)} 个补丁")
            logger.info(f"语言筛选后剩余 {len(filtered_high_confidence_patches)}/{len(verified_high_confidence_patches)} 个高置信度补丁")
            
            all_patches_dicts = filtered_all_patches
            verified_high_confidence_patches = filtered_high_confidence_patches
        
        # ------------------------------------------------------------------
        # 步骤3.6: 基于最终的高置信度补丁在线搜索等价补丁
        # ------------------------------------------------------------------
        logger.info("步骤3.6: 搜索等价补丁 (equivalent patches)")
        equivalent_patches_map = self._find_equivalent_patches(cve_id, verified_high_confidence_patches)
        
        # 步骤5: 生成结果
        result = {
            "cve_id": cve_id,
            "trace_time": datetime.now().isoformat(),
            "network_stats": {
                "total_nodes": len(reference_network.nodes),
                "total_edges": len(reference_network.edges),
                "issue_nodes": len(reference_network.get_issue_nodes()),
                "patch_nodes": len(reference_network.get_patch_nodes())
            },
            "all_patches": all_patches_dicts,
            "high_confidence_patches": verified_high_confidence_patches,
            "patch_summary": patch_summary,
            "equivalent_patches": equivalent_patches_map,
        }
        
        # 如果使用了语言筛选，添加到结果中
        if language:
            result["language_filter"] = language
            logger.info(f"应用了语言筛选: {language}")
            
            # 添加语言筛选统计
            result["language_filter_stats"] = {
                "original_patches": len([p for p in patch_candidates]),
                "filtered_patches": len(all_patches_dicts),
                "original_high_confidence": len(high_confidence_patch_dicts),
                "filtered_high_confidence": len(verified_high_confidence_patches)
            }
            
            # 输出语言筛选统计
            logger.info(f"语言筛选统计: 原始补丁 {result['language_filter_stats']['original_patches']} -> 筛选后 {result['language_filter_stats']['filtered_patches']}")
            logger.info(f"语言筛选统计: 原始高置信度补丁 {result['language_filter_stats']['original_high_confidence']} -> 筛选后 {result['language_filter_stats']['filtered_high_confidence']}")
        
        # 保存结果到文件
        self._save_results(cve_id, result, reference_network)
        
        logger.info(f"补丁跟踪完成")
        logger.info(f"发现 {len(patch_candidates)} 个潜在补丁，其中 {len(verified_high_confidence_patches)} 个高置信度可访问补丁")
        
        return result
    
    def _save_results(self, cve_id: str, result: Dict[str, Any], reference_network: ReferenceNetwork) -> None:
        """
        保存跟踪结果到文件
        
        Args:
            cve_id: CVE ID
            result: 跟踪结果
            reference_network: 引用网络
        """
        # 规范化cve_id作为目录名
        cve_dir = cve_id.replace('-', '_')
        cve_output_dir = os.path.join(self.output_dir, cve_dir)
        os.makedirs(cve_output_dir, exist_ok=True)
        
        # 保存跟踪结果
        summary_file = os.path.join(cve_output_dir, "trace_result.json")
        save_json(result, summary_file)
        
        # 保存网络数据
        network_file = os.path.join(cve_output_dir, "reference_network.json")
        save_json(reference_network.to_dict(), network_file)
        
        # 保存网络可视化图片
        network_image_file = os.path.join(cve_output_dir, "reference_network.png")
        reference_network.visualize(output_path=network_image_file)
        
        logger.debug(f"已保存结果到 {cve_output_dir}")
        
    def _verify_patch_accessibility(self, patch_results: List[Dict], analyzer: ReferenceAnalyzer) -> List[Dict]:
        """
        验证补丁URL的可访问性，降低或剔除无法访问的URL
        
        Args:
            patch_results: 原始补丁结果列表
            analyzer: 引用分析器(用于复用其HTTP会话)
            
        Returns:
            List[Dict]: 过滤后的补丁结果列表
        """
        verified_patches = []
        logger.info(f"验证 {len(patch_results)} 个补丁URL的可访问性...")
        
        # 为避免多次验证相同URL，使用集合记录已验证的URL
        verified_urls = {}  # url -> 是否可访问
        
        for patch in patch_results:
            url = patch['url']
            
            # 如果已验证过此URL，直接使用结果
            if url in verified_urls:
                is_accessible = verified_urls[url]
            else:
                # 否则进行验证
                is_accessible = self._is_url_accessible(url, analyzer.session)
                verified_urls[url] = is_accessible
                
            if is_accessible:
                # URL可访问，保留
                verified_patches.append(patch)
            else:
                # URL不可访问，记录日志
                logger.warning(f"补丁URL不可访问，已从结果中剔除: {url}")
        
        logger.info(f"验证完成: {len(verified_patches)}/{len(patch_results)} 个补丁URL可访问")
        return verified_patches
        
    def _is_url_accessible(self, url: str, session: Optional[requests.Session] = None) -> bool:
        """
        检查URL是否可访问
        
        Args:
            url: 要检查的URL
            session: 可选的请求会话，如果提供将复用
            
        Returns:
            bool: URL是否可访问
        """
        if not session:
            session = requests.Session()
            
        try:
            # 使用HEAD请求快速检查URL是否可访问
            response = session.head(
                url,
                timeout=10,
                allow_redirects=True,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            # 检查状态码
            if response.status_code < 400:
                return True
                
            # 如果HEAD请求失败，尝试GET请求
            response = session.get(
                url,
                timeout=10,
                allow_redirects=True,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                stream=True  # 使用流式请求，不下载完整内容
            )
            
            # 只读取一小部分内容即关闭连接
            if response.status_code < 400:
                response.close()
                return True
                
            return False
            
        except (requests.RequestException, Exception) as e:
            logger.debug(f"URL访问检查失败: {url} - {str(e)}")
            return False
    
    def _get_repository_language(self, url: str) -> Optional[Dict[str, int]]:
        """
        获取GitHub仓库的主要语言信息
        
        Args:
            url: GitHub仓库相关URL
            
        Returns:
            Dict[str, int]: 语言及其代码量的字典，如果不是GitHub URL或获取失败则返回None
        """
        # 检查是否是GitHub URL
        repo_info = extract_github_repo_from_url(url)
        if not repo_info:
            logger.debug(f"URL {url} 不是GitHub仓库URL")
            return None
            
        # 提取仓库所有者和名称
        owner, repo = repo_info
        logger.debug(f"识别到GitHub仓库: {owner}/{repo}")
        
        # 缓存仓库语言信息（避免重复请求）
        cache_key = f"{owner}/{repo}"
        if hasattr(self, '_repo_language_cache') and cache_key in self._repo_language_cache:
            logger.debug(f"使用缓存的仓库语言信息: {owner}/{repo}")
            return self._repo_language_cache[cache_key]
            
        # 初始化缓存（如果不存在）
        if not hasattr(self, '_repo_language_cache'):
            self._repo_language_cache = {}
            
        # GitHub API URL
        api_url = f"https://api.github.com/repos/{owner}/{repo}/languages"
        logger.debug(f"请求GitHub API获取仓库语言: {api_url}")
        
        try:
            # 准备请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/vnd.github.v3+json'
            }
            
            # 如果有GitHub令牌，添加到请求头
            if self.github_token:
                headers['Authorization'] = f"token {self.github_token}"
                logger.debug(f"请求中使用GitHub API令牌")
                
            # 发送请求
            response = requests.get(
                api_url,
                headers=headers,
                timeout=self.timeout,
                verify=self.verify_ssl
            )
            
            # 检查响应
            if response.status_code == 200:
                # 解析响应为语言统计
                languages = response.json()
                logger.debug(f"获取到仓库语言信息: {owner}/{repo} => {languages}")
                # 缓存结果
                self._repo_language_cache[cache_key] = languages
                return languages
                
            else:
                logger.warning(f"获取仓库 {owner}/{repo} 语言信息失败: HTTP {response.status_code}, {response.text[:100]}")
                # 缓存空结果避免重复请求
                self._repo_language_cache[cache_key] = {}
                return {}
                
        except Exception as e:
            logger.warning(f"获取仓库 {owner}/{repo} 语言信息时出错: {str(e)}")
            # 缓存空结果避免重复请求
            self._repo_language_cache[cache_key] = {}
            return {}
    
    def batch_trace(self, cve_list: List[Dict[str, Any]], language: Optional[str] = None) -> Dict[str, Any]:
        """
        批量跟踪多个CVE的补丁
        
        Args:
            cve_list: CVE列表，每个元素包含cve_id和references
                例如: [{"cve_id": "CVE-2021-44228", "references": ["url1", "url2"]}]
            language: 按编程语言筛选补丁(可选)，如"java,python,c++"
                
        Returns:
            Dict: 批量跟踪结果
        """
        results = {}
        for item in cve_list:
            cve_id = item.get("cve_id")
            references = item.get("references", [])
            sources = item.get("sources", None)
            
            if not cve_id or not references:
                logger.warning(f"跳过无效的CVE项: {item}")
                continue
                
            try:
                logger.info(f"开始处理 {cve_id}")
                result = self.trace(cve_id, references, sources, language)
                results[cve_id] = {
                    "status": "success",
                    "high_confidence_patches": len(result["high_confidence_patches"]),
                    "total_patches": len(result["all_patches"])
                }
            except Exception as e:
                logger.error(f"处理 {cve_id} 时出错: {str(e)}")
                results[cve_id] = {
                    "status": "error",
                    "error": str(e)
                }
                
        # 保存批量结果摘要
        summary_file = os.path.join(self.output_dir, "batch_trace_summary.json")
        save_json(results, summary_file)
        
        return results
        
    def load_trace_result(self, cve_id: str) -> Optional[Dict[str, Any]]:
        """
        加载之前的跟踪结果
        
        Args:
            cve_id: CVE ID
            
        Returns:
            Optional[Dict]: 结果数据，如果不存在则返回None
        """
        cve_output_dir = os.path.join(self.output_dir, cve_id.replace('-', '_'))
        summary_file = os.path.join(cve_output_dir, "trace_result.json")
        
        if os.path.exists(summary_file):
            return load_json(summary_file)
        
        return None

    # ------------------------------------------------------------------
    #  等价补丁发现相关辅助方法
    # ------------------------------------------------------------------

    def _find_equivalent_patches(self, cve_id: str, base_patches: List[Dict], 
                                 similarity_threshold: float = 0.55, 
                                 max_candidates: int = 50) -> Dict[str, Dict[str, Any]]:
        """
        在线搜索与给定补丁等价的其他补丁提交。
        
        支持多种Git仓库URL格式，包括：
        - GitHub: github.com/owner/repo/commit/HASH
        - GitLab: gitlab.com/owner/repo/commit/HASH 或 gitlab.com/owner/repo/-/commit/HASH
        - OpenSSL: git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=HASH
        - Apache: gitbox.apache.org/repos/asf?p=httpd.git;a=commit;h=HASH
        - Linux kernel: git.kernel.org/pub/scm/linux/kernel/git/.../commit/?id=HASH
        - 以及其他Git仓库格式

        等价补丁判定采用分级机制:
        优先级1 (高置信度条件):
            - 提交信息中包含同一个 CVE ID 且 diff代码分块相似度 ≥ similarity_threshold
            - 提交信息中显式引用了原始补丁的短 SHA 且 diff代码分块相似度 ≥ similarity_threshold * 0.8
            
        优先级2 (中置信度条件): 
            - 提交信息与原始补丁相似度 ≥ 0.7 且 diff代码分块相似度 ≥ similarity_threshold
            - 代码变更完全相同
          
        优先级3 (低置信度条件):
            - 仅提交信息中包含同一个 CVE ID
            - 仅提交信息中显式引用了原始补丁的短 SHA
            - 仅消息完全相同

        增强功能：
            1. 获取每个补丁的提交时间并保存
            2. 按时间戳排序所有等价补丁
            3. 将最早的提交标记为"original_patch"，最晚的提交标记为"target_patch"
            4. 只有当找到多个补丁时才形成补丁对
            5. 记录匹配的置信度级别(1-3)

        Args:
            cve_id: CVE 编号
            base_patches: 高置信度补丁列表（dict 形式），字段至少包含 'url'
            similarity_threshold: 补丁 diff 相似度阈值
            max_candidates: 每个仓库最多考察的候选数量（分页上限）

        Returns:
            Dict[str, Dict[str, Any]]: {
                原始补丁 URL: {
                    "equivalent_patches": [ 等价补丁信息... ],
                    "original_patch": 最早的补丁信息,
                    "target_patch": 最晚的补丁信息,
                    "total_count": 总补丁数量
                }
            }
        """
        equivalent_map: Dict[str, Dict[str, Any]] = {}

        for patch in base_patches:
            patch_url = patch.get('url')
            if not patch_url:
                continue
                
            # 使用增强的函数从URL中提取commit hash
            commit_hash = extract_commit_hash_from_url(patch_url)
            if not commit_hash:
                logger.debug(f"无法从URL提取commit hash: {patch_url}")
                continue
                
            # 尝试获取GitHub仓库信息
            github_info = None
            
            # 如果是GitHub URL，直接解析
            if 'github.com' in patch_url and '/commit/' in patch_url:
                match = re.search(r'github\.com/([^/]+)/([^/]+)/commit/([a-f0-9]{7,40})', patch_url)
                if match:
                    owner, repo, _ = match.groups()
                    github_info = (owner, repo)
            # 如果不是GitHub URL，尝试查找对应的GitHub仓库
            else:
                logger.info(f"检测到非GitHub URL: {patch_url}，尝试查找对应的GitHub仓库")
                github_repo = find_github_repo_by_commit_hash(commit_hash = commit_hash, github_token = self.github_token, original_url = patch_url)
                if github_repo:
                    owner, repo, _ = github_repo
                    github_info = (owner, repo)
                    logger.info(f"成功找到对应的GitHub仓库: {owner}/{repo}")
                else:
                    logger.info(f"未找到对应的GitHub仓库，跳过此URL: {patch_url}")
                    continue

            # 如果无法获取GitHub仓库信息，跳过
            if not github_info:
                continue
                
            owner, repo = github_info
            logger.debug(f"Searching equivalent patches for {patch_url} (owner={owner}, repo={repo}, sha={commit_hash[:7]})")

            # 初始化列表
            equivalent_map[patch_url] = {
                "equivalent_patches": [],
                "original_patch": None,
                "target_patch": None,
                "total_count": 0
            }

            # -----------------------
            # 1) 基于 CVE ID 搜索提交
            # -----------------------
            cve_equivalents = self._search_commit_by_query(owner, repo, cve_id, max_candidates)

            # -----------------------
            # 2) 基于 SHA 引用搜索提交（取前 7 位短 SHA）
            # -----------------------
            short_sha = commit_hash[:7]
            sha_equivalents = self._search_commit_by_query(owner, repo, short_sha, max_candidates)

            candidate_commits = {c['sha']: c for c in (cve_equivalents + sha_equivalents)}

            # 获取原始补丁的详细信息（包括时间戳）
            base_commit_info = self._get_commit_detail(owner, repo, commit_hash)
            base_message = base_commit_info.get('message', '') if base_commit_info else ''
            base_diff = base_commit_info.get('diff', '') if base_commit_info else ''
            base_date = base_commit_info.get('commit_date', '') if base_commit_info else ''
            base_modified_files = base_commit_info.get('modified_files', [])
            
            # 检查原始补丁是否修改了任何文件，不再限制只检查C/C++文件
            base_files_modified = []
            for file_info in base_modified_files:
                filename = file_info.get('filename', '')
                base_files_modified.append(filename)
            
            # 只检查是否修改了文件，不再要求是C/C++文件
            if not base_files_modified:
                logger.info(f"Original patch {commit_hash[:7]} did not modify any files, skipping equivalent patch search")
                continue
                
            # 创建基础补丁信息
            base_patch_info = {
                'url': patch_url,
                'sha': commit_hash,
                'message': base_message,
                'commit_date': base_date,
                'reason': 'original_base_patch',
                'modified_files': base_files_modified,
                'confidence_level': 1,  # 基准补丁设置为最高置信度
                'has_function_changes': False  # 不再检查函数级别变更
            }
            
            # 添加到等价补丁列表
            equivalent_map[patch_url]["equivalent_patches"].append(base_patch_info)

            # 如果没有候选则继续
            if not candidate_commits:
                continue

            for sha, info in candidate_commits.items():
                # 排除自身
                if sha == commit_hash:
                    continue
                    
                commit_message = info.get('message', '')
                cand_commit_info = None
                cand_diff = None
                diff_similarity = 0.0
                message_similarity = 0.0

                # 初始化分析结果
                has_cve = cve_id in commit_message
                has_sha_reference = self._has_sha_reference(short_sha, commit_message)
                
                # 提前计算消息相似度，以便之后使用
                if base_message and commit_message:
                    message_similarity = self._compute_similarity(base_message, commit_message)
                    message_identical = base_message.strip() == commit_message.strip()
                else:
                    message_identical = False
                
                # 获取提交详情以计算diff相似度
                if has_cve or has_sha_reference or message_similarity >= 0.7 or message_identical:
                        cand_commit_info = self._get_commit_detail(owner, repo, sha)
                        cand_diff = cand_commit_info.get('diff', '') if cand_commit_info else ''
                        diff_similarity = self._compute_hunk_based_similarity(base_diff, cand_diff)
                        logger.debug(f"比较 diff 相似度: {commit_hash[:7]} vs {sha[:7]} => {diff_similarity:.2f}")
                        
                # 代码完全相同检查
                identical_code = base_diff and cand_diff and base_diff.strip() == cand_diff.strip()
                
                # 分级判定逻辑
                confidence_level = 0
                reasons = []
                
                # 优先级1 (高置信度条件)
                if (has_cve and diff_similarity >= similarity_threshold):
                    confidence_level = 1
                    reasons.append(f'HIGH_CONFIDENCE: CVE_MATCH+CODE_SIMILAR_{diff_similarity:.2f}')
                elif (has_sha_reference and diff_similarity >= similarity_threshold * 0.8):
                    confidence_level = 1
                    reasons.append(f'HIGH_CONFIDENCE: SHA_REFERENCE+CODE_SIMILAR_{diff_similarity:.2f}')
                        
                # 优先级2 (中置信度条件)
                elif (message_similarity >= 0.7 and diff_similarity >= similarity_threshold):
                    confidence_level = 2
                    reasons.append(f'MEDIUM_CONFIDENCE: MESSAGE_SIMILAR_{message_similarity:.2f}+CODE_SIMILAR_{diff_similarity:.2f}')
                elif identical_code:
                    confidence_level = 2
                    reasons.append('MEDIUM_CONFIDENCE: IDENTICAL_CODE_CHANGES')
                
                # 优先级3 (低置信度条件)
                elif has_cve:
                    confidence_level = 3
                    reasons.append('LOW_CONFIDENCE: ONLY_CVE_MATCH')
                elif has_sha_reference:
                    confidence_level = 3
                    reasons.append('LOW_CONFIDENCE: ONLY_SHA_REFERENCE')
                elif message_identical:
                    confidence_level = 3
                    reasons.append('LOW_CONFIDENCE: ONLY_IDENTICAL_MESSAGE')

                # 如果匹配到任何条件
                if confidence_level > 0:
                    # 合并所有匹配的原因为一个字符串
                    reason = ", ".join(reasons)
                
                    # 获取提交详细信息，包括时间戳
                    if not cand_commit_info:
                        cand_commit_info = self._get_commit_detail(owner, repo, sha)
                    commit_date = cand_commit_info.get('commit_date', '')
                    modified_files = cand_commit_info.get('modified_files', [])
                    
                    # 检查是否修改了文件
                    files_modified = []
                    for file_info in modified_files:
                        filename = file_info.get('filename', '')
                        files_modified.append(filename)
                    
                    # 只检查是否修改了文件
                    if not files_modified:
                        logger.debug(f"Patch {sha[:7]} did not modify any files, skipping")
                        continue
                    
                    patch_info = {
                        'url': info.get('html_url'),
                        'sha': sha,
                        'reason': reason,
                        'message': commit_message,
                        'commit_date': commit_date,
                        'modified_files': files_modified,
                        'confidence_level': confidence_level,  # 添加置信度级别
                        'has_function_changes': False  # 不再检查函数级别变更
                    }
                    
                    equivalent_map[patch_url]["equivalent_patches"].append(patch_info)

            # 处理找到的等价补丁
            patches = equivalent_map[patch_url]["equivalent_patches"]
            if not patches:
                # 未找到任何等价补丁，删除条目
                equivalent_map.pop(patch_url, None)
                continue
                
            # 按提交时间排序(用于original_patch选择和通用列表展示)
            sorted_patches = sorted(patches, key=lambda x: x.get('commit_date', ''))
            
            # 更新结果，添加排序后的补丁列表
            equivalent_map[patch_url]["equivalent_patches"] = sorted_patches
            equivalent_map[patch_url]["total_count"] = len(sorted_patches)
            
            # 只有当存在多个补丁时才形成补丁对
            if len(sorted_patches) >= 2:
                # 标记最早的补丁为original_patch
                equivalent_map[patch_url]["original_patch"] = sorted_patches[0]
                
                # 为target_patch选择高置信度或中置信度补丁中最新的
                # 1. 先筛选出高置信度(level 1)和中置信度(level 2)的补丁
                high_medium_patches = [p for p in sorted_patches if p.get('confidence_level', 3) <= 2]
                
                if high_medium_patches:
                    # 2. 按置信度排序，然后按时间倒序排列
                    # 处理ISO日期格式，将带Z的格式转换为Python可以解析的格式
                    def parse_date_to_timestamp(date_str):
                        # 处理带Z后缀的ISO日期格式
                        if date_str and date_str.endswith('Z'):
                            date_str = date_str[:-1] + '+00:00'
                        try:
                            return datetime.fromisoformat(date_str).timestamp()
                        except (ValueError, TypeError):
                            return 0  # 返回默认时间戳
                    
                    target_candidates = sorted(
                        high_medium_patches, 
                        key=lambda x: (x.get('confidence_level', 3), -parse_date_to_timestamp(x.get('commit_date', '')))
                    )
                    
                    # 确保第一个候选不是original_patch
                    if target_candidates[0]['sha'] == sorted_patches[0]['sha']:
                        # 如果最优候选就是original_patch，尝试选择下一个候选
                        if len(target_candidates) > 1:
                            # 选择下一个最佳候选
                            equivalent_map[patch_url]["target_patch"] = target_candidates[1]
                            logger.info(f"Found patch pair: original patch({sorted_patches[0]['commit_date']}) -> "
                                      f"target patch({target_candidates[1]['commit_date']}, "
                                      f"confidence level: {target_candidates[1]['confidence_level']})")
                        else:
                            # 没有其他候选，设置为null
                            equivalent_map[patch_url]["target_patch"] = None
                            logger.info(f"Only single candidate found after filtering, target_patch set to null")
                    else:
                        # 选择置信度最高且时间最新的作为target_patch
                        equivalent_map[patch_url]["target_patch"] = target_candidates[0]
                        logger.info(f"Found patch pair: original patch({sorted_patches[0]['commit_date']}) -> "
                                  f"target patch({target_candidates[0]['commit_date']}, "
                                  f"confidence level: {target_candidates[0]['confidence_level']})")
                else:
                    # 如果没有高置信度或中置信度的补丁，则使用所有补丁中最新的（排除原始补丁）
                    # 先按时间排序
                    time_sorted_patches = sorted(sorted_patches, key=lambda x: x.get('commit_date', ''))
                    
                    # 如果只有一个补丁，或者最新的补丁就是原始补丁
                    if len(time_sorted_patches) <= 1 or time_sorted_patches[-1]['sha'] == sorted_patches[0]['sha']:
                        # 设置target_patch为null
                        equivalent_map[patch_url]["target_patch"] = None
                        logger.info(f"No suitable target patch found, target_patch set to null")
                    else:
                        # 使用最新的非原始补丁
                        latest_patch = time_sorted_patches[-1]
                        # 再次确认不是原始补丁
                        if latest_patch['sha'] != sorted_patches[0]['sha']:
                            equivalent_map[patch_url]["target_patch"] = latest_patch
                            logger.info(f"No high/medium confidence patches found. Using latest patch as target: "
                                       f"{latest_patch['commit_date']} (confidence level: {latest_patch['confidence_level']})")
                        else:
                            # 如果最新的是原始补丁，取倒数第二个
                            if len(time_sorted_patches) > 1:
                                latest_patch = time_sorted_patches[-2]
                                equivalent_map[patch_url]["target_patch"] = latest_patch
                                logger.info(f"Original patch was latest, using second latest as target: "
                                         f"{latest_patch['commit_date']} (confidence level: {latest_patch['confidence_level']})")
                            else:
                                # 没有其他选择，设为null
                                equivalent_map[patch_url]["target_patch"] = None
                                logger.info(f"No suitable target patch found, target_patch set to null")
            else:
                # 只有单个补丁，将original_patch设置为该补丁，但target_patch设为null
                equivalent_map[patch_url]["original_patch"] = sorted_patches[0]
                equivalent_map[patch_url]["target_patch"] = None
                logger.info(f"Only single patch found, target_patch set to null")

        # 处理非Github URL的等价补丁
        self._add_cross_repo_equivalent_patches(equivalent_map)
        
        return equivalent_map
    
    def _add_cross_repo_equivalent_patches(self, equivalent_map: Dict[str, Dict[str, Any]]) -> None:
        """
        处理不同仓库间的等价补丁
        
        这个方法寻找不同URL来源但具有相同commit hash的补丁，并将它们标记为等价
        
        Args:
            equivalent_map: 等价补丁映射表
        """
        # 创建commit hash到URL的映射
        commit_to_urls = {}
        
        # 第一次遍历：收集所有commit hash和对应URL
        for patch_url, patch_data in equivalent_map.items():
            for patch in patch_data.get("equivalent_patches", []):
                commit_hash = patch.get("sha")
                if commit_hash:
                    if commit_hash not in commit_to_urls:
                        commit_to_urls[commit_hash] = []
                    if patch_url not in commit_to_urls[commit_hash]:
                        commit_to_urls[commit_hash].append(patch_url)
        
        # 第二次遍历：为具有多个URL的commit hash添加交叉引用
        for commit_hash, urls in commit_to_urls.items():
            if len(urls) > 1:
                logger.info(f"发现跨仓库等价补丁 (commit: {commit_hash[:7]}, 仓库数: {len(urls)})")
                
                for i, url1 in enumerate(urls):
                    for url2 in urls[i+1:]:
                        # 为第一个URL添加到第二个URL的等价引用
                        if url1 in equivalent_map:
                            for patch in equivalent_map[url1].get("equivalent_patches", []):
                                if patch.get("sha") == commit_hash:
                                    patch["has_cross_repo_equivalent"] = True
                                    if "cross_repo_equivalents" not in patch:
                                        patch["cross_repo_equivalents"] = []
                                    if url2 not in patch["cross_repo_equivalents"]:
                                        patch["cross_repo_equivalents"].append(url2)
                        
                        # 为第二个URL添加到第一个URL的等价引用
                        if url2 in equivalent_map:
                            for patch in equivalent_map[url2].get("equivalent_patches", []):
                                if patch.get("sha") == commit_hash:
                                    patch["has_cross_repo_equivalent"] = True
                                    if "cross_repo_equivalents" not in patch:
                                        patch["cross_repo_equivalents"] = []
                                    if url1 not in patch["cross_repo_equivalents"]:
                                        patch["cross_repo_equivalents"].append(url1)

    def _has_sha_reference(self, short_sha: str, commit_message: str) -> bool:
        """
        检查提交信息是否引用了特定的SHA
        
        Args:
            short_sha: 短SHA值
            commit_message: 提交信息
            
        Returns:
            bool: 是否找到引用
        """
        # 直接包含SHA
        if short_sha in commit_message:
            return True
        
        # 特殊引用格式
        special_patterns = [
            re.compile(fr'\bcommit\s+{short_sha}(?:\s+|$)'),         # "commit fda6c89" 格式
            re.compile(fr'\bfixes:?\s+{short_sha}', re.IGNORECASE),   # "fixes: fda6c89" 格式
            re.compile(fr'\bbackport\s+.*?\b{short_sha}\b', re.IGNORECASE), # "backport ... fda6c89" 格式
            re.compile(fr'\bupstream:?\s+{short_sha}', re.IGNORECASE), # "upstream: fda6c89" 格式
            re.compile(fr'\b{short_sha}\s+upstream\b')                # "fda6c89 upstream" 格式
        ]
        
        for pattern in special_patterns:
            if pattern.search(commit_message):
                return True
                
        return False

    def _search_commit_by_query(self, owner: str, repo: str, query: str, max_candidates: int = 50) -> List[Dict[str, Any]]:
        """
        使用 GitHub commit 搜索 API 按关键词查询提交
        
        增强功能：
        1. 基本搜索
        2. 稳定分支查询（针对Linux仓库）
        """
        results: List[Dict[str, Any]] = []
        seen_shas = set()  # 用于去重

        # 基本 headers 设置
        headers = {
            'Accept': 'application/vnd.github.cloak-preview',  # 必须的 preview 头
            'User-Agent': 'PatchTracer/1.0'
        }
        if self.github_token:
            headers['Authorization'] = f'token {self.github_token}'

        # 1. 主分支搜索
        search_url = f"https://api.github.com/search/commits?q={query}+repo:{owner}/{repo}&per_page=100"
        try:
            response = requests.get(search_url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
            if response.status_code == 200:
                data = response.json()
                for item in data.get('items', [])[:max_candidates]:
                    sha = item.get('sha')
                    if sha and sha not in seen_shas:
                        seen_shas.add(sha)
                        results.append({
                            'sha': sha,
                            'html_url': item.get('html_url'),
                            'message': item.get('commit', {}).get('message', '')
                        })
            else:
                logger.debug(f"GitHub commit 搜索失败 {search_url}: HTTP {response.status_code}")
        except Exception as e:
            logger.debug(f"GitHub commit 搜索异常 {search_url}: {e}")

        # 2. 针对 Linux 仓库的稳定分支搜索 (如果是 torvalds/linux)
        if owner == 'torvalds' and repo == 'linux':
            # 知名的Linux稳定分支维护者仓库
            stable_repos = [
                ('gregkh', 'linux-stable'),     # Greg KH的稳定版仓库
                ('gregkh', 'linux-5.15.y'),     # 5.15长期支持版
                ('gregkh', 'linux-5.10.y'),     # 5.10长期支持版
                ('gregkh', 'linux-5.4.y'),      # 5.4长期支持版
                ('gregkh', 'linux-4.19.y'),     # 4.19长期支持版
                ('gregkh', 'linux-4.14.y')      # 4.14长期支持版
            ]
            
            # 在稳定分支仓库中搜索
            for stable_owner, stable_repo in stable_repos:
                stable_search_url = f"https://api.github.com/search/commits?q={query}+repo:{stable_owner}/{stable_repo}&per_page=100"
                try:
                    response = requests.get(stable_search_url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
                    if response.status_code == 200:
                        data = response.json()
                        for item in data.get('items', [])[:max_candidates//2]:  # 限制每个稳定分支的结果数量
                            sha = item.get('sha')
                            if sha and sha not in seen_shas:
                                seen_shas.add(sha)
                                results.append({
                                    'sha': sha,
                                    'html_url': item.get('html_url'),
                                    'message': item.get('commit', {}).get('message', '')
                                })
                    else:
                        logger.debug(f"GitHub 稳定分支搜索失败 {stable_search_url}: HTTP {response.status_code}")
                except Exception as e:
                    logger.debug(f"GitHub 稳定分支搜索异常 {stable_search_url}: {e}")
                
                # 避免请求过于频繁
                time.sleep(0.5)

        return results

    def _get_commit_detail(self, owner: str, repo: str, sha: str) -> Dict[str, Any]:
        """
        获取提交详情，包括 message、diff 文本以及提交时间
        
        Args:
            owner: 仓库所有者
            repo: 仓库名称
            sha: 提交哈希
            
        Returns:
            Dict[str, Any]: 提交详情，包含message、diff、commit_date字段
        """
        headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'PatchTracer/1.0'
        }
        if self.github_token:
            headers['Authorization'] = f'token {self.github_token}'

        api_url = f"https://api.github.com/repos/{owner}/{repo}/commits/{sha}"
        try:
            resp = requests.get(api_url, headers=headers, timeout=self.timeout, verify=self.verify_ssl)
            if resp.status_code != 200:
                return {}
            data = resp.json()
            message = data.get('commit', {}).get('message', '')
            # 获取提交日期（ISO 8601格式）
            commit_date = data.get('commit', {}).get('committer', {}).get('date')
            if not commit_date:
                commit_date = data.get('commit', {}).get('author', {}).get('date')

            # 获取修改的文件列表
            files = data.get('files', [])
            modified_files = []
            for file_info in files:
                modified_files.append({
                    'filename': file_info.get('filename', ''),
                    'status': file_info.get('status', ''),
                    'additions': file_info.get('additions', 0),
                    'deletions': file_info.get('deletions', 0),
                    'changes': file_info.get('changes', 0),
                    'patch': file_info.get('patch', '')
                })

            # 获取 diff
            diff_headers = headers.copy()
            diff_headers['Accept'] = 'application/vnd.github.v3.diff'
            diff_resp = requests.get(api_url, headers=diff_headers, timeout=self.timeout, verify=self.verify_ssl)
            diff_text = diff_resp.text if diff_resp.status_code == 200 else ''

            return {
                'message': message,
                'diff': diff_text,
                'commit_date': commit_date,
                'modified_files': modified_files
            }
        except Exception as e:
            logger.debug(f"获取提交详情失败 {owner}/{repo}@{sha}: {e}")
            return {}

    @staticmethod
    def _compute_similarity(diff1: str, other_diff: str) -> float:
        """计算两个 diff 文本的相似度 (0.0-1.0)
        
        使用整体文本的相似度计算方法，不考虑diff的hunk结构
        """
        if not diff1 or not other_diff:
            return 0.0
        return difflib.SequenceMatcher(None, diff1, other_diff).ratio()
        
    @staticmethod
    def _parse_diff_hunks(diff_text: str) -> List[str]:
        """
        将diff文本解析为独立的hunk块列表
        
        Args:
            diff_text: 完整的diff文本
            
        Returns:
            List[str]: hunk块列表
        """
        if not diff_text:
            return []
            
        hunks = []
        current_hunk = []
        in_hunk = False
        
        for line in diff_text.splitlines():
            # 新文件的开始，重置状态
            if line.startswith('diff --git') or line.startswith('commit '):
                if current_hunk:
                    hunks.append('\n'.join(current_hunk))
                    current_hunk = []
                in_hunk = False
                
            # hunk块的开始（@@ 形式的行）
            elif line.startswith('@@') and '@@' in line[2:]:
                if current_hunk:
                    hunks.append('\n'.join(current_hunk))
                    current_hunk = []
                in_hunk = True
                current_hunk.append(line)
                
            # 在hunk内部，收集行
            elif in_hunk:
                current_hunk.append(line)
                
        # 添加最后一个hunk
        if current_hunk:
            hunks.append('\n'.join(current_hunk))
            
        return hunks
        
    @staticmethod
    def _compute_hunk_based_similarity(diff1: str, diff2: str, 
                                      hunk_similarity_threshold: float = 0.8) -> float:
        """
        基于hunk级别计算两个diff文本的相似度
        
        1. 将两个diff解析为各自的hunk块
        2. 对每个hunk与另一个diff的所有hunk计算相似度
        3. 记录每个hunk的最高匹配相似度
        4. 返回:
           - 如果有任意hunk的相似度超过阈值，返回最高的hunk相似度
           - 否则返回整体相似度作为后备
        
        Args:
            diff1: 第一个diff文本
            diff2: 第二个diff文本
            hunk_similarity_threshold: 单个hunk的相似度阈值，超过此值认为是等价hunk
            
        Returns:
            float: 相似度分数 (0.0-1.0)
        """
        # 如果任一diff为空，返回0
        if not diff1 or not diff2:
            return 0.0
            
        # 解析为hunk块
        hunks1 = PatchTracer._parse_diff_hunks(diff1)
        hunks2 = PatchTracer._parse_diff_hunks(diff2)
        
        # 如果解析后没有有效hunk，使用整体相似度
        if not hunks1 or not hunks2:
            logger.debug(f"未找到有效hunk块，使用整体相似度")
            return PatchTracer._compute_similarity(diff1, diff2)
        
        # 记录每个hunk的最高匹配分数
        max_hunk_similarities = []
        detailed_similarities = []
        
        # 对第一个diff的每个hunk，计算与第二个diff所有hunk的最高相似度
        for i, hunk1 in enumerate(hunks1):
            hunk_max_similarity = 0.0
            best_match_idx = -1
            
            for j, hunk2 in enumerate(hunks2):
                similarity = difflib.SequenceMatcher(None, hunk1, hunk2).ratio()
                if similarity > hunk_max_similarity:
                    hunk_max_similarity = similarity
                    best_match_idx = j
                    
            max_hunk_similarities.append(hunk_max_similarity)
            detailed_similarities.append((f"source hunk#{i+1}", f"target hunk#{best_match_idx+1}", hunk_max_similarity))
        
        # 对第二个diff的每个hunk，计算与第一个diff所有hunk的最高相似度
        # 这是为了捕获单向移植的情况
        for j, hunk2 in enumerate(hunks2):
            hunk_max_similarity = 0.0
            best_match_idx = -1
            
            for i, hunk1 in enumerate(hunks1):
                similarity = difflib.SequenceMatcher(None, hunk2, hunk1).ratio()
                if similarity > hunk_max_similarity:
                    hunk_max_similarity = similarity
                    best_match_idx = i
                    
            max_hunk_similarities.append(hunk_max_similarity)
            detailed_similarities.append((f"target hunk#{j+1}", f"source hunk#{best_match_idx+1}", hunk_max_similarity))
        
        # 输出详细的hunk匹配情况（仅当日志级别为DEBUG时）
        if logger.getEffectiveLevel() <= logging.DEBUG:
            for src, dst, sim in sorted(detailed_similarities, key=lambda x: x[2], reverse=True)[:5]:  # 只显示前5个最高相似度
                logger.debug(f"Hunk matching: {src} <-> {dst}, similarity: {sim:.2f}")
        
        # 计算最高的hunk相似度
        if max_hunk_similarities:
            highest_hunk_similarity = max(max_hunk_similarities)
            logger.debug(f"highest hunk similarity: {highest_hunk_similarity:.2f}, threshold: {hunk_similarity_threshold}")
            
            # 如果任意hunk的相似度超过阈值，认为patch等价，返回最高相似度
            if highest_hunk_similarity >= hunk_similarity_threshold:
                logger.debug(f"Found matching hunk block, using hunk level similarity: {highest_hunk_similarity:.2f}")
                return highest_hunk_similarity
            else:
                logger.debug(f"All hunk similarities are below threshold {hunk_similarity_threshold}, using overall similarity as fallback")
        
        # 如果没有hunk相似度超过阈值，返回整体相似度作为后备
        overall_similarity = PatchTracer._compute_similarity(diff1, diff2)
        logger.debug(f"Using overall similarity as fallback: {overall_similarity:.2f}")
        return overall_similarity

    def _is_c_cpp_file(self, filename: str) -> bool:
        """
        检查文件是否为C/C++语言文件
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否为C/C++文件
        """
        c_cpp_extensions = ['.c', '.h', '.cpp', '.hpp', '.cc', '.cxx', '.hxx', '.c++', '.h++']
        _, ext = os.path.splitext(filename.lower())
        return ext in c_cpp_extensions
        
    def _contains_function_level_changes(self, patch: str) -> bool:
        """
        检查补丁是否包含函数级别的修改
        
        这里使用简化的启发式方法：
        - 检查补丁是否含有函数定义相关的行（如包含括号、大括号等模式）
        - 避免只修改注释、头文件包含、宏定义等非函数内部的改动
        
        Args:
            patch: 补丁文本
            
        Returns:
            bool: 是否包含函数级别的修改
        """
        # 简化实现：检查是否有可能的函数内部修改特征
        # 查找函数体内的修改（在花括号内的代码修改）
        if not patch:
            return False
            
        # 查找函数定义模式（如 "void func(" 或 "int main()"）
        function_patterns = [
            r'[+-]\s*\w+\s+\w+\s*\([^)]*\)\s*{',  # 函数定义
            r'[+-]\s*\w+\s+\w+::\w+\s*\([^)]*\)\s*{',  # 类方法定义
            r'[+-]\s*\w+\s*\([^)]*\)\s*{',  # 简化函数定义
            r'[+-]\s*}\s*else\s*{',  # 条件语句块
            r'[+-]\s*for\s*\([^)]*\)\s*{',  # for循环
            r'[+-]\s*while\s*\([^)]*\)\s*{',  # while循环
            r'[+-]\s*if\s*\([^)]*\)\s*{',  # if语句
            r'[+-]\s*switch\s*\([^)]*\)\s*{',  # switch语句
            r'[+-]?\s*{\s*[+-]',  # 花括号内的修改
            r'[+-]\s*return\s+.*;',  # return语句
        ]
        
        # 如果匹配任一模式，认为是函数级别的修改
        for pattern in function_patterns:
            if re.search(pattern, patch):
                return True
                
        return False

def setup_logging(log_level: str = "INFO") -> None:
    """
    设置日志
    
    Args:
        log_level: 日志级别
    """
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"无效的日志级别: {log_level}")
        
    logging.basicConfig(
        level=numeric_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def main():
    """命令行入口点"""
    parser = argparse.ArgumentParser(description="补丁跟踪器 - 从CVE引用中发现高置信度补丁")
    parser.add_argument("cve_id", help="要分析的CVE ID (例如: CVE-2021-44228)")
    parser.add_argument("--references", nargs="+", help="初始引用URL列表")
    parser.add_argument("--references-file", help="包含初始引用的JSON文件")
    parser.add_argument("--output-dir", default="/home/<USER>/data", help="输出目录")
    parser.add_argument("--max-depth", type=int, default=2, help="最大抓取深度")
    parser.add_argument("--request-delay", type=float, default=1.0, help="请求延迟(秒)")
    parser.add_argument("--timeout", type=int, default=30, help="请求超时(秒)")
    parser.add_argument("--github-token", help="GitHub API令牌")
    parser.add_argument("--confidence-threshold", type=float, default=0.7, 
                        help="补丁置信度阈值(0.0-1.0)")
    parser.add_argument("--log-level", default="INFO", 
                        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                        help="日志级别")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 获取初始引用
    if args.references:
        initial_references = args.references
    elif args.references_file:
        if not os.path.exists(args.references_file):
            logger.error(f"引用文件不存在: {args.references_file}")
            return 1
        refs_data = load_json(args.references_file)
        if isinstance(refs_data, list):
            initial_references = refs_data
        elif isinstance(refs_data, dict) and "references" in refs_data:
            initial_references = refs_data["references"]
        else:
            logger.error(f"无法从{args.references_file}解析引用")
            return 1
    else:
        logger.error("必须提供引用列表(--references)或引用文件(--references-file)")
        return 1
    
    # 创建跟踪器并运行
    tracer = PatchTracer(
        output_dir=args.output_dir,
        max_depth=args.max_depth,
        request_delay=args.request_delay,
        timeout=args.timeout,
        github_token=args.github_token,
        confidence_threshold=args.confidence_threshold
    )
    
    try:
        result = tracer.trace(args.cve_id, initial_references)
        logger.info(f"找到 {len(result['high_confidence_patches'])} 个高置信度补丁")
        for i, patch in enumerate(result['high_confidence_patches'], 1):
            logger.info(f"补丁 {i}: {patch['url']} (置信度: {patch['confidence']:.2f})")
        return 0
    except Exception as e:
        logger.error(f"补丁跟踪过程中出错: {str(e)}", exc_info=True)
        return 1

if __name__ == "__main__":
    exit(main()) 