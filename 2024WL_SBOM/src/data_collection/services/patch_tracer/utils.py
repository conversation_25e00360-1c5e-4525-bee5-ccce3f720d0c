#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
补丁跟踪器工具模块

提供了一些辅助函数，用于处理URL、文本分析等。
"""

import re
import json
import hashlib
import logging
import urllib.parse
import requests
import time
from datetime import datetime
from typing import Dict, List, Set, Any, Optional, Union, Tuple
import os
import sys

# 将相对导入修改为绝对导入
# 首先检查当前文件的路径，从中获取repo_mapping模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from repo_mapping import find_official_repo_for_commit, update_repo_mapping, add_repo_mapping

logger = logging.getLogger(__name__)

def normalize_url(url: str) -> str:
    """
    规范化URL
    
    Args:
        url: 原始URL
        
    Returns:
        str: 规范化后的URL
    """
    # 修复没有协议的URL
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
        
    # 解码URL
    url = urllib.parse.unquote(url)
    
    # 检查是否为gitweb格式URL
    if 'gitweb' in url and ('a=commitdiff' in url or 'a=commit' in url):
        # 为gitweb URL保留查询参数
        return url.split('#')[0].rstrip('/')
    
    # 移除URL片段和查询参数
    url = url.split('#')[0]
    url = url.split('?')[0]
    
    # 确保URL不以斜杠结尾
    url = url.rstrip('/')
    
    return url

def extract_github_repo_from_url(url: str) -> Optional[tuple]:
    """
    从URL中提取GitHub仓库信息
    
    Args:
        url: URL
        
    Returns:
        tuple: (所有者, 仓库名称)元组
    """
    if 'github.com/' not in url:
        return None
        
    try:
        parts = url.split('github.com/')[-1].split('/')
        if len(parts) >= 2:
            return (parts[0], parts[1])
    except Exception:
        pass
        
    return None

def extract_commit_hash_from_url(url: str) -> Optional[str]:
    """
    从各种Git仓库URL中提取commit hash
    
    支持的URL格式包括：
    - GitHub: github.com/owner/repo/commit/HASH
    - GitLab: gitlab.com/owner/repo/commit/HASH or gitlab.com/owner/repo/-/commit/HASH
    - OpenSSL: git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=HASH
    - Apache: gitbox.apache.org/repos/asf?p=httpd.git;a=commit;h=HASH
    - Linux kernel: git.kernel.org/pub/scm/linux/kernel/git/.../commit/?id=HASH
    - GNOME: gitlab.gnome.org/GNOME/project/-/commit/HASH
    - 通用 Git commit URL格式
    
    Args:
        url: Git仓库的commit URL
        
    Returns:
        str: 提取的commit hash，如果无法提取则返回None
    """
    # 预处理: 解码URL
    decoded_url = urllib.parse.unquote(url)
    
    # 常规URL标准化处理
    normalized_url = normalize_url(url)
    
    # GitHub/GitLab标准commit URL
    std_match = re.search(r'/(commit|commits)/([a-f0-9]{7,40})(?:/|\?|$)', normalized_url)
    if std_match:
        return std_match.group(2)
    
    # GitLab带破折号格式 (例如 /-/commit/)
    gitlab_match = re.search(r'/-/commit/([a-f0-9]{7,40})(?:/|\?|$)', normalized_url)
    if gitlab_match:
        return gitlab_match.group(1)
    
    # OpenSSL和Apache commitdiff URL格式 (h=HASH)
    # 注意：这里使用原始URL，因为查询参数可能在normalize_url中被移除
    openssl_match = re.search(r'[?&;]h=([a-f0-9]{7,40})(?:&|;|$)', decoded_url)
    if openssl_match:
        return openssl_match.group(1)
    
    # Linux kernel Git格式 (id=HASH)
    # 注意：这里使用原始URL，因为查询参数可能在normalize_url中被移除
    kernel_match = re.search(r'[?&;]id=([a-f0-9]{7,40})(?:&|;|$)', decoded_url)
    if kernel_match:
        return kernel_match.group(1)
        
    # Apache GitBox特有格式
    apache_match = re.search(r'gitbox\.apache\.org/repos/asf\?p=[^;]+;a=commit;h=([a-f0-9]{7,40})', decoded_url)
    if apache_match:
        return apache_match.group(1)
    
    # Git SCM格式 (Linux Kernel 官方网站)
    git_scm_match = re.search(r'git\.kernel\.org/pub/scm/[^/]+/[^/]+/[^/]+/commit/\?id=([a-f0-9]{7,40})', decoded_url)
    if git_scm_match:
        return git_scm_match.group(1)
    
    # 通用hashref提取（适用于URL中包含明确的commit hash但不符合上述格式的情况）
    # 注意：保持这个检查在最后，以避免错误的匹配
    generic_match = re.search(r'([a-f0-9]{7,40})(?:/|\?|#|&|;|$)', normalized_url)
    if generic_match:
        # 验证是否像Git hash（至少7个十六进制字符）
        potential_hash = generic_match.group(1)
        if len(potential_hash) >= 7 and all(c in '0123456789abcdef' for c in potential_hash.lower()):
            return potential_hash
    
    return None

def find_github_repo_by_commit_hash(commit_hash: str, github_token: Optional[str] = None, 
                                    max_repos: int = 20, original_url: Optional[str] = None) -> Optional[Tuple[str, str, str]]:
    """
    根据commit hash在GitHub上查找原始仓库
    
    排序策略综合考虑以下因素：
    1. 仓库的流行度（star数量、fork数量）
    2. 仓库是否为知名组织/项目
    3. 提交时间（较早的提交通常为原始仓库）
    4. 仓库是否为fork (非fork优先)
    5. 若提供original_url，会尝试从URL映射到官方GitHub仓库
    
    Args:
        commit_hash: 提交哈希值
        github_token: GitHub API令牌(可选)
        max_repos: 最大搜索仓库数量
        original_url: 原始URL，用于尝试映射到官方仓库
        
    Returns:
        Optional[Tuple[str, str, str]]: (owner, repo, url)元组，表示仓库所有者、仓库名和完整URL
                                        如果未找到则返回None
    """
    if not commit_hash or len(commit_hash) < 7:
        logger.warning(f"无效的commit hash: {commit_hash}")
        return None
        
    # 1. 首先尝试使用仓库映射模块查找官方仓库
    if original_url:
        official_repo = find_official_repo_for_commit(commit_hash, original_url, github_token)
        if official_repo:
            logger.info(f"在官方仓库中找到commit {commit_hash[:7]}: {official_repo[0]}/{official_repo[1]}")
            return official_repo
        
    # 构造搜索URL (GitHub API提供对commit hash的搜索)
    search_url = f"https://api.github.com/search/commits?q=hash:{commit_hash}"
    
    # 设置请求头 - 增强认证和请求头处理
    headers = {
        'Accept': 'application/vnd.github.cloak-preview+json',  # 修正preview头格式
        'User-Agent': 'PatchTracer/1.0'
    }
    if github_token:
        # 检查token格式，自动添加适当前缀
        if github_token.startswith('github_pat_'):
            headers['Authorization'] = f'token {github_token}'
        else:
            headers['Authorization'] = f'Bearer {github_token}'
        logger.info(f"使用GitHub API token: {github_token[:5]}...{github_token[-5:]}")
    else:
        logger.warning("未提供GitHub API token，可能会受到API速率限制")
        
    try:
        # 尝试直接hash搜索
        logger.info(f"搜索commit hash: {commit_hash}, 搜索URL: {search_url}")
        response = requests.get(search_url, headers=headers, timeout=30)
        repos_data = []
        
        if response.status_code != 200:
            logger.warning(f"GitHub API请求失败: HTTP {response.status_code}, {response.text}")
            
            # 检查是否达到API速率限制
            if response.status_code == 403 and 'rate limit' in response.text.lower():
                rate_limit = response.headers.get('X-RateLimit-Limit', 'unknown')
                rate_remaining = response.headers.get('X-RateLimit-Remaining', 'unknown')
                rate_reset = response.headers.get('X-RateLimit-Reset', 'unknown')
                logger.error(f"GitHub API速率限制: 限额={rate_limit}, 剩余={rate_remaining}, 重置时间={rate_reset}")
                return None
                
            # 如果直接hash搜索失败，尝试扩展搜索
            search_url = f"https://api.github.com/search/commits?q={commit_hash}"
            logger.info(f"尝试扩展搜索: {search_url}")
            response = requests.get(search_url, headers=headers, timeout=30)
            
            if response.status_code != 200:
                logger.warning(f"扩展搜索也失败: HTTP {response.status_code}, {response.text}")
                return None
        
        data = response.json()
        
        # 记录搜索结果
        total_count = data.get('total_count', 0)
        logger.info(f"搜索返回 {total_count} 个结果")
        
        if total_count == 0:
            logger.info(f"未找到包含commit hash {commit_hash} 的仓库")
            return None
            
        # 收集批量处理的仓库信息
        owner_repo_pairs = []
        
        # 处理搜索结果
        for item in data.get('items', [])[:max_repos]:
            repo_url = item.get('repository', {}).get('html_url', '')
            if not repo_url:
                logger.debug("搜索结果中缺少仓库URL")
                continue
                
            owner_repo_match = re.search(r'github\.com/([^/]+)/([^/]+)/?$', repo_url)
            if not owner_repo_match:
                logger.debug(f"无法从URL解析所有者/仓库: {repo_url}")
                continue
                
            owner = owner_repo_match.group(1)
            repo = owner_repo_match.group(2)
            logger.debug(f"找到仓库: {owner}/{repo}")
            
            # 获取仓库元数据JSON，如果存在
            repo_metadata = item.get('repository', {})
            
            # 记录额外的仓库信息（如果搜索结果中包含）
            extra_info = {
                'stars': repo_metadata.get('stargazers_count', -1),  # -1表示未知
                'forks': repo_metadata.get('forks_count', -1),
                'watchers': repo_metadata.get('watchers_count', -1),
                'is_fork': repo_metadata.get('fork', False),
                'updated_at': repo_metadata.get('updated_at', ''),
                'repo_url': repo_url
            }
            
            owner_repo_pairs.append((owner, repo, extra_info))
            
        if not owner_repo_pairs:
            logger.warning("搜索结果中没有有效的仓库")
            return None
            
        logger.info(f"找到 {len(owner_repo_pairs)} 个可能的仓库")
            
        # 批量获取commit详情和仓库详情，每次最多处理5个
        batch_size = 5
        for i in range(0, len(owner_repo_pairs), batch_size):
            batch = owner_repo_pairs[i:i+batch_size]
            
            for owner, repo, extra_info in batch:
                # 获取提交详情，包括时间戳
                commit_url = f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_hash}"
                logger.debug(f"获取commit详情: {commit_url}")
                
                # 避免API请求过于频繁
                time.sleep(0.5)
                
                commit_headers = headers.copy()
                try:
                    # 获取commit详情
                    commit_response = requests.get(commit_url, headers=commit_headers, timeout=30)
                    if commit_response.status_code != 200:
                        logger.debug(f"获取commit详情失败: HTTP {commit_response.status_code}, 仓库: {owner}/{repo}")
                        continue
                        
                    commit_data = commit_response.json()
                    commit_date = commit_data.get('commit', {}).get('committer', {}).get('date')
                    if not commit_date:
                        commit_date = commit_data.get('commit', {}).get('author', {}).get('date')
                        
                    # 如果stars等信息不在commit搜索结果中，额外获取仓库详情
                    stars = extra_info['stars']
                    forks = extra_info['forks']
                    is_fork = extra_info['is_fork']
                    
                    if stars == -1 or forks == -1:
                        # 获取仓库详情
                        repo_url = f"https://api.github.com/repos/{owner}/{repo}"
                        logger.debug(f"额外获取仓库详情: {repo_url}")
                        
                        # 避免请求过于频繁
                        time.sleep(0.5)
                        
                        try:
                            repo_response = requests.get(repo_url, headers=headers, timeout=30)
                            if repo_response.status_code == 200:
                                repo_data = repo_response.json()
                                stars = repo_data.get('stargazers_count', 0)
                                forks = repo_data.get('forks_count', 0)
                                is_fork = repo_data.get('fork', False)
                                logger.debug(f"仓库 {owner}/{repo} 统计信息: stars={stars}, forks={forks}, is_fork={is_fork}")
                        except Exception as e:
                            logger.debug(f"获取仓库详情失败: {str(e)}")
                        
                    if commit_date:
                        # 标准化日期格式
                        if commit_date.endswith('Z'):
                            commit_date = commit_date[:-1] + '+00:00'
                        
                        # 获取额外的仓库数据用于评分
                        repo_info = {
                            'owner': owner,
                            'repo': repo,
                            'url': f"https://github.com/{owner}/{repo}/commit/{commit_hash}",
                            'commit_date': commit_date,
                            'stars': stars,
                            'forks': forks,
                            'is_fork': is_fork
                        }
                        
                        repos_data.append(repo_info)
                        logger.debug(f"添加候选仓库: {owner}/{repo}, 提交日期: {commit_date}, stars: {stars}, forks: {forks}, is_fork: {is_fork}")
                except Exception as e:
                    logger.warning(f"获取仓库 {owner}/{repo} 的commit详情时出错: {str(e)}")
        
        # 如果找到了仓库，使用更精确的排序策略
        if repos_data:
            logger.info(f"找到 {len(repos_data)} 个有效仓库记录")
            
            # 定义更精确的排序函数
            def rank_repo(repo_data):
                # 流行度分数 (基于stars和forks)
                popularity_score = repo_data.get('stars', 0) * 2 + repo_data.get('forks', 0)
                
                # 时间分数（越早提交越高）
                time_score = 0
                if 'commit_date' in repo_data:
                    try:
                        # 转换时间为分数
                        commit_timestamp = datetime.fromisoformat(repo_data['commit_date']).timestamp()
                        # 时间因素仅占较小比重
                        time_score = -commit_timestamp / 1e10  # 缩小时间因素的影响
                    except Exception as e:
                        logger.warning(f"解析日期时发生错误: {repo_data['commit_date']}, {str(e)}")
                
                # 知名组织/项目加分
                owner_score = 0
                known_orgs = ['apache', 'google', 'facebook', 'microsoft', 'linux', 'torvalds', 
                            'openssl', 'openjdk', 'rust-lang', 'tensorflow', 'kubernetes', 
                            'nodejs', 'python']
                if repo_data.get('owner', '').lower() in known_orgs:
                    owner_score = 1000
                    logger.debug(f"知名组织加分: {repo_data['owner']}")
                
                # 非fork额外加分
                fork_score = 0
                if repo_data.get('is_fork') is False:  # 明确不是fork的加分
                    fork_score = 2000
                    logger.debug(f"仓库 {repo_data.get('owner', '')}/{repo_data.get('repo', '')} 非fork加分: {fork_score}")
                
                # 计算总分
                total_score = popularity_score + time_score + owner_score + fork_score
                
                # 记录详细评分
                logger.debug(f"仓库 {repo_data.get('owner', '')}/{repo_data.get('repo', '')} 评分: " +
                           f"流行度={popularity_score}, 时间={time_score:.2f}, " +
                           f"组织={owner_score}, fork={fork_score}, 总分={total_score:.2f}")
                
                return total_score
                
            # 按新的排序策略排序
            try:
                sorted_repos = sorted(repos_data, key=rank_repo, reverse=True)
                best_repo = sorted_repos[0]
                
                # 记录排序后的前几个仓库，方便调试
                logger.info(f"排序后前3个仓库:")
                for i, repo in enumerate(sorted_repos[:min(3, len(sorted_repos))]):
                    logger.info(f"{i+1}. {repo['owner']}/{repo['repo']} " +
                               f"(stars: {repo.get('stars', 'unknown')}, " +
                               f"forks: {repo.get('forks', 'unknown')}, " + 
                               f"is_fork: {repo.get('is_fork', 'unknown')}, " +
                               f"date: {repo.get('commit_date', 'unknown')})")
                
                logger.info(f"选择commit {commit_hash[:7]} 的最佳仓库: {best_repo['owner']}/{best_repo['repo']} " +
                           f"(stars: {best_repo.get('stars', 'unknown')}, forks: {best_repo.get('forks', 'unknown')})")
                return (best_repo['owner'], best_repo['repo'], best_repo['url'])
            except Exception as e:
                logger.error(f"排序仓库时发生错误: {str(e)}")
        else:
            logger.debug(f"未找到commit {commit_hash[:7]} 的仓库信息")
                
    except Exception as e:
        logger.warning(f"查找仓库时发生错误: {str(e)}")
        
    return None

def extract_commit_id_from_url(url: str) -> Optional[str]:
    """
    从URL中提取提交ID
    
    Args:
        url: URL
        
    Returns:
        str: 提交ID
    """
    # GitHub提交
    github_match = re.search(r'github\.com/[^/]+/[^/]+/commit/([a-f0-9]+)', url)
    if github_match:
        return github_match.group(1)
        
    # GitLab提交
    gitlab_match = re.search(r'gitlab\.com/[^/]+/[^/]+/commit/([a-f0-9]+)', url)
    if gitlab_match:
        return gitlab_match.group(1)
        
    # SVN提交
    svn_match = re.search(r'[?&]r=(\d+)', url)
    if svn_match:
        return svn_match.group(1)
        
    return None

def extract_issue_id_from_url(url: str) -> Optional[str]:
    """
    从URL中提取Issue ID
    
    Args:
        url: URL
        
    Returns:
        str: Issue ID
    """
    # GitHub Issue
    github_issue_match = re.search(r'github\.com/[^/]+/[^/]+/issues/(\d+)', url)
    if github_issue_match:
        return github_issue_match.group(1)
        
    # GitHub PR
    github_pr_match = re.search(r'github\.com/[^/]+/[^/]+/pull/(\d+)', url)
    if github_pr_match:
        return github_pr_match.group(1)
        
    # JIRA Issue
    jira_match = re.search(r'[A-Z]+-\d+', url)
    if jira_match:
        return jira_match.group(0)
        
    # Bugzilla
    bugzilla_match = re.search(r'show_bug\.cgi\?id=(\d+)', url)
    if bugzilla_match:
        return bugzilla_match.group(1)
        
    return None

def compute_url_hash(url: str) -> str:
    """
    计算URL哈希值
    
    Args:
        url: URL
        
    Returns:
        str: 哈希值
    """
    normalized_url = normalize_url(url)
    return hashlib.md5(normalized_url.encode('utf-8')).hexdigest()

def filter_urls(urls: List[str], 
               include_patterns: Optional[List[str]] = None,
               exclude_patterns: Optional[List[str]] = None) -> List[str]:
    """
    过滤URL列表
    
    Args:
        urls: URL列表
        include_patterns: 包含模式列表
        exclude_patterns: 排除模式列表
        
    Returns:
        List[str]: 过滤后的URL列表
    """
    filtered_urls = []
    
    for url in urls:
        # 检查排除模式
        if exclude_patterns:
            exclude = False
            for pattern in exclude_patterns:
                if re.search(pattern, url):
                    exclude = True
                    break
            if exclude:
                continue
                
        # 检查包含模式
        if include_patterns:
            include = False
            for pattern in include_patterns:
                if re.search(pattern, url):
                    include = True
                    break
            if not include:
                continue
                
        filtered_urls.append(url)
        
    return filtered_urls

def save_json(data: Any, file_path: str) -> None:
    """
    将数据保存为JSON文件
    
    Args:
        data: 要保存的数据
        file_path: 文件路径
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"数据已保存到: {file_path}")
    except Exception as e:
        logger.error(f"保存数据到 {file_path} 时出错: {str(e)}")

def load_json(file_path: str) -> Any:
    """
    从JSON文件加载数据
    
    Args:
        file_path: 文件路径
        
    Returns:
        Any: 加载的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"从 {file_path} 加载数据时出错: {str(e)}")
        return None

def find_best_repo_for_commit(commit_hash: str, url: Optional[str] = None, 
                            github_token: Optional[str] = None, max_repos: int = 20) -> Optional[Tuple[str, str, str]]:
    """
    查找commit的最佳仓库，优先使用仓库映射，再使用GitHub API搜索
    
    整合了仓库映射模块和GitHub API搜索功能，用于查找commit对应的最佳仓库。
    查找顺序：
    1. 如果提供URL，尝试使用仓库映射找到官方仓库
    2. 使用GitHub API搜索，并按评分选择最佳仓库
    
    Args:
        commit_hash: 提交哈希值
        url: 原始URL (可选)
        github_token: GitHub API令牌 (可选)
        max_repos: 最大搜索仓库数量
        
    Returns:
        Optional[Tuple[str, str, str]]: (owner, repo, url)元组，表示仓库所有者、仓库名和完整URL
                                      如果未找到则返回None
    """
    if not commit_hash or len(commit_hash) < 7:
        logger.warning(f"无效的commit hash: {commit_hash}")
        return None
        
    logger.info(f"为commit {commit_hash[:7]} 查找最佳仓库")
    
    # 首先通过映射查找官方仓库
    if url:
        official_repo = find_official_repo_for_commit(commit_hash, url, github_token)
        if official_repo:
            logger.info(f"在官方映射仓库中找到commit {commit_hash[:7]}: {official_repo[0]}/{official_repo[1]}")
            return official_repo
    
    # 然后通过GitHub API搜索
    github_repo = find_github_repo_by_commit_hash(commit_hash, github_token, max_repos, url)
    if github_repo:
        logger.info(f"通过GitHub API找到commit {commit_hash[:7]}: {github_repo[0]}/{github_repo[1]}")
        return github_repo
        
    logger.warning(f"无法为commit {commit_hash[:7]} 找到合适的仓库")
    return None 