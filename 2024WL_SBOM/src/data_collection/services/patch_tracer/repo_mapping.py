#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
仓库映射工具模块

提供从非GitHub URL识别和映射到官方GitHub仓库的功能。
"""

import re
import logging
import urllib.parse
import requests
from typing import Dict, List, Set, Any, Optional, Union, Tuple

logger = logging.getLogger(__name__)

# 官方仓库映射字典
OFFICIAL_REPO_MAP = {
    # 域名映射
    "domains": {
        "openssl.org": "openssl/openssl",
        "kernel.org": "torvalds/linux",
        "apache.org": {
            "httpd": "apache/httpd",
            "tomcat": "apache/tomcat",
            "spark": "apache/spark",
            "hadoop": "apache/hadoop",
            "kafka": "apache/kafka",
            "struts": "apache/struts",
            "_default": "apache/apache"
        },
        "rust-lang.org": "rust-lang/rust",
        "python.org": "python/cpython",
        "nodejs.org": "nodejs/node",
        "ruby-lang.org": "ruby/ruby",
        "postgresql.org": "postgres/postgres",
        "mysql.com": "mysql/mysql-server",
        "webkit.org": "WebKit/WebKit",
        "chromium.org": "chromium/chromium",
        "mozilla.org": "mozilla/gecko-dev"
    },
    
    # 项目名称映射
    "projects": {
        "openssl": "openssl/openssl",
        "linux kernel": "torvalds/linux",
        "linux": "torvalds/linux",
        "gcc": "gcc-mirror/gcc",
        "sqlite": "sqlite/sqlite",
        "python": "python/cpython",
        "nodejs": "nodejs/node",
        "node.js": "nodejs/node",
        "kubernetes": "kubernetes/kubernetes",
        "k8s": "kubernetes/kubernetes",
        "docker": "moby/moby",
        "nginx": "nginx/nginx",
        "apache": "apache/httpd",
        "httpd": "apache/httpd",
        "tomcat": "apache/tomcat",
        "postgres": "postgres/postgres",
        "postgresql": "postgres/postgres",
        "mysql": "mysql/mysql-server",
        "redis": "redis/redis",
        "mongodb": "mongodb/mongo",
        "react": "facebook/react",
        "vue": "vuejs/vue",
        "angular": "angular/angular",
        "tensorflow": "tensorflow/tensorflow",
        "pytorch": "pytorch/pytorch",
        "django": "django/django",
        "flask": "pallets/flask",
        "rust": "rust-lang/rust",
        "go": "golang/go",
        "ruby": "ruby/ruby",
        "rails": "rails/rails",
        "php": "php/php-src",
        "laravel": "laravel/laravel",
        "symfony": "symfony/symfony"
    },
    
    # URL模式映射
    "url_patterns": {
        r"git\.kernel\.org/.*/linux/kernel/git/torvalds/linux": "torvalds/linux",
        r"git\.kernel\.org/.*/linux/kernel/git/.*/linux": "torvalds/linux",
        r"svn\.apache\.org/repos/asf/httpd": "apache/httpd",
        r"svn\.apache\.org/repos/asf/tomcat": "apache/tomcat",
        r"svn\.apache\.org/repos/asf/hadoop": "apache/hadoop",
        r"git\.postgresql\.org/git/postgresql": "postgres/postgres",
        r"git\.openssl\.org/.*openssl\.git": "openssl/openssl",
        r"git\.php\.net": "php/php-src",
        r"git\.gnome\.org": "GNOME/",
        r"git\.savannah\.gnu\.org/git/gcc": "gcc-mirror/gcc",
        r"svn\.python\.org/projects/python": "python/cpython",
        r"hg\.python\.org": "python/cpython",
        r"hg\.mozilla\.org": "mozilla/gecko-dev",
        r"chromium\.googlesource\.com": "chromium/chromium",
        r"anongit\.kde\.org": "KDE/",
        r"gitlab\.freedesktop\.org": "freedesktop/",
        r"gitlab\.gnome\.org": "GNOME/"
    }
}

def identify_project_from_url(url: str) -> Optional[Tuple[str, str]]:
    """从URL识别开源项目的官方GitHub仓库
    
    Args:
        url: 原始URL
        
    Returns:
        Optional[Tuple[str, str]]: (owner, repo)元组，未找到则返回None
    """
    if not url:
        return None
        
    parsed_url = urllib.parse.urlparse(url)
    domain = parsed_url.netloc.lower()
    path = parsed_url.path.lower()
    
    # 1. 检查域名映射
    if domain in OFFICIAL_REPO_MAP["domains"]:
        domain_map = OFFICIAL_REPO_MAP["domains"][domain]
        if isinstance(domain_map, str):
            return tuple(domain_map.split('/'))
        
        # 检查子项目
        for subproject, repo_path in domain_map.items():
            if subproject != "_default" and subproject in path:
                return tuple(repo_path.split('/'))
                
        # 使用默认映射
        if "_default" in domain_map:
            return tuple(domain_map["_default"].split('/'))
    
    # 2. 检查URL模式
    for pattern, repo_path in OFFICIAL_REPO_MAP["url_patterns"].items():
        if re.search(pattern, url):
            # 处理特殊情况（如果只有组织名）
            if repo_path.endswith('/'):
                # 尝试从URL中提取项目名
                parts = path.strip('/').split('/')
                if parts:
                    project_name = parts[-1]
                    if '.' in project_name:
                        project_name = project_name.split('.')[0]  # 移除扩展名
                    return (repo_path.rstrip('/'), project_name)
            else:
                return tuple(repo_path.split('/'))
    
    # 3. 尝试从URL中提取项目名
    path_parts = [p for p in path.split('/') if p]
    for part in path_parts:
        part = part.split('.')[0]  # 移除扩展名
        if part in OFFICIAL_REPO_MAP["projects"]:
            return tuple(OFFICIAL_REPO_MAP["projects"][part].split('/'))
        
        # 尝试部分匹配
        for project, repo_path in OFFICIAL_REPO_MAP["projects"].items():
            if project.lower() in part.lower() or part.lower() in project.lower():
                return tuple(repo_path.split('/'))
    
    # 4. 最后尝试使用URL中的项目名搜索
    if len(path_parts) >= 2:
        potential_owner = path_parts[0]
        potential_repo = path_parts[1]
        for project, repo_path in OFFICIAL_REPO_MAP["projects"].items():
            if project.lower() in potential_repo.lower():
                owner, _ = repo_path.split('/')
                return (owner, potential_repo)
                
    return None

def verify_commit_in_repo(owner: str, repo: str, commit_hash: str, 
                         github_token: Optional[str] = None) -> bool:
    """验证commit是否存在于指定仓库
    
    Args:
        owner: 仓库所有者
        repo: 仓库名称
        commit_hash: 提交哈希
        github_token: GitHub API令牌
        
    Returns:
        bool: 存在则返回True
    """
    if not owner or not repo or not commit_hash:
        return False
        
    headers = {'User-Agent': 'PatchTracer/1.0'}
    if github_token:
        if github_token.startswith('github_pat_'):
            headers['Authorization'] = f'token {github_token}'
        else:
            headers['Authorization'] = f'Bearer {github_token}'
    
    url = f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_hash}"
    try:
        response = requests.get(url, headers=headers, timeout=15)
        return response.status_code == 200
    except Exception as e:
        logger.debug(f"验证commit存在性时出错: {str(e)}")
        return False

def find_official_repo_for_commit(commit_hash: str, original_url: str = None, 
                               github_token: Optional[str] = None) -> Optional[Tuple[str, str, str]]:
    """根据commit hash和原始URL查找官方GitHub仓库
    
    Args:
        commit_hash: 提交哈希
        original_url: 原始URL
        github_token: GitHub API令牌
        
    Returns:
        Optional[Tuple[str, str, str]]: (owner, repo, url)元组，表示仓库所有者、仓库名和完整URL
    """
    if not commit_hash:
        return None
        
    logger.info(f"尝试为commit {commit_hash[:7]} 查找官方仓库")
    
    # 如果提供了原始URL，尝试识别官方仓库
    if original_url:
        project_info = identify_project_from_url(original_url)
        if project_info:
            owner, repo = project_info
            logger.info(f"从URL识别到可能的官方仓库: {owner}/{repo}")
            
            # 验证commit是否存在于该仓库
            if verify_commit_in_repo(owner, repo, commit_hash, github_token):
                logger.info(f"已确认commit存在于官方仓库 {owner}/{repo}")
                return (owner, repo, f"https://github.com/{owner}/{repo}/commit/{commit_hash}")
            else:
                logger.info(f"未在官方仓库 {owner}/{repo} 中找到commit {commit_hash[:7]}")
    
    # 无法通过映射确定或验证失败
    return None

def update_repo_mapping(new_mapping: Dict) -> None:
    """更新仓库映射字典
    
    Args:
        new_mapping: 新的映射数据
    """
    global OFFICIAL_REPO_MAP
    
    # 更新各个部分
    for section in ["domains", "projects", "url_patterns"]:
        if section in new_mapping:
            if section not in OFFICIAL_REPO_MAP:
                OFFICIAL_REPO_MAP[section] = {}
            OFFICIAL_REPO_MAP[section].update(new_mapping[section])
    
    logger.info("已更新仓库映射字典")

def add_repo_mapping(section: str, key: str, value: str) -> None:
    """添加单个仓库映射
    
    Args:
        section: 映射部分 ("domains", "projects", "url_patterns")
        key: 映射键（域名、项目名称或URL模式）
        value: 映射值（GitHub仓库路径，格式为"owner/repo"）
    """
    global OFFICIAL_REPO_MAP
    
    if section not in OFFICIAL_REPO_MAP:
        OFFICIAL_REPO_MAP[section] = {}
        
    OFFICIAL_REPO_MAP[section][key] = value
    logger.info(f"已添加映射: {section}[{key}] = {value}") 