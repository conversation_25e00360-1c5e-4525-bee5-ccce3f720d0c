#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
补丁识别与评分模块

负责识别和评估参考网络中的补丁链接，计算置信度得分。
"""

import re
import json
import time
import logging
import requests
from dataclasses import dataclass, field
from typing import List, Dict, Set, Any, Optional, Tuple
import networkx as nx

from .reference_node import ReferenceNode, NodeType
from .reference_network import ReferenceNetwork

logger = logging.getLogger(__name__)

@dataclass
class PatchCandidate:
    """
    补丁候选类
    
    表示一个识别出的潜在补丁
    """
    node: ReferenceNode            # 补丁节点
    confidence: float              # 置信度得分(0.0-1.0)
    source_paths: List[List[str]] = field(default_factory=list)  # 从根节点到补丁节点的路径
    metadata: Dict[str, Any] = field(default_factory=dict)       # 补丁元数据
    
    def __post_init__(self):
        """
        初始化后处理
        """
        if not self.source_paths:
            self.source_paths = []
        if not self.metadata:
            self.metadata = {}

class PatchIdentifier:
    """
    补丁识别与评分类
    
    负责识别和评估参考网络中的补丁链接，计算置信度得分
    """
    
    def __init__(self, network: ReferenceNetwork, github_token: Optional[str] = None, strategy: str = "use_rules"):
        """
        初始化补丁识别器
        
        Args:
            network: 参考网络
            github_token: GitHub API令牌（可选）
            strategy: 补丁识别策略，可选值:
                - "use_rules": 使用规则过滤
                - "all_patches": 使用网络中所有补丁节点
        """
        self.network = network
        self.github_token = github_token
        self.strategy = strategy
        self.high_confidence_sources = {
            'NVD', 'REDHAT', 'DEBIAN', 'UBUNTU', 'MITRE', 'APACHE'
        }
        
    def identify_patches(self) -> List[PatchCandidate]:
        """
        从参考网络中识别补丁候选项
        
        此方法是补丁识别的主要入口，完成以下步骤：
        1. 根据策略选择识别方法（规则过滤或使用所有补丁节点）
        2. 应用补丁过滤规则，包括：
           - 补丁类型规则（git_commit、svn等）
           - 深度限制规则
           - 如果有CVE日期信息，应用补丁日期规则
           - 如果有CPE信息，应用目标CPE匹配规则
        3. 对每个补丁节点计算置信度分数：
           - 连接度分数：基于节点在网络中的位置和连接性
           - 来源置信度：基于节点的来源平台可信度
           - 内容相关性：基于补丁内容与CVE的相关性
        4. 提取每个补丁节点的元数据和来源路径
        5. 按照置信度由高到低排序
        
        如果规则过滤后没有找到补丁节点，将回退到使用所有补丁节点。
        
        Returns:
            List[PatchCandidate]: 按置信度排序的补丁候选项列表
        """
        # 如果是网络中所有补丁类型节点为候选项，则直接获取
        if self.strategy == "all_patches":
            patch_nodes = self.network.get_patch_nodes()
        # 如果是使用特定规则，则通过规则获取
        else:
            # 定义规则，进行补丁过滤
            rules = {
                'patch_type': ['git_commit', 'svn', 'svn_commit'],  # 添加'svn'以匹配更多可能的补丁
            }
            
            # 如果有配置cutoff，则添加
            if self.network.nodes:
                max_depth = max(node.depth for node in self.network.nodes)
                rules['cutoff'] = str(min(max_depth, 5))  # 使用最大深度，但不超过5
            
            # 检查是否有CVE发布日期和CPE信息
            if self.network.cve_published_date and self.network.cve_affected_cpes:
                # 如果有完整信息，可以添加这些规则，但作为可选项
                patch_content_rules = []
                
                # 有CVE发布日期，可以添加日期检查
                if self.network.cve_published_date:
                    patch_content_rules.append('patch_date')
                
                # 有受影响CPE信息，可以添加CPE相似度检查
                if self.network.cve_affected_cpes and len(self.network.cve_affected_cpes) > 0:
                    patch_content_rules.append('only_target_CPEs')
                
                # 只有当有规则可用时，才添加patch_content键
                if patch_content_rules:
                    rules['patch_content'] = patch_content_rules
            
            # 获取符合规则的patch节点
            patch_nodes = self.network.get_patches_by_rules(rules)
            
            # 如果使用规则后没有找到补丁，回退到获取所有补丁节点
            if not patch_nodes:
                logger.warning("使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点")
                patch_nodes = self.network.get_patch_nodes()
        
        # 为每个节点计算置信度
        patch_candidates = []
        for node in patch_nodes:
            # 使用原始置信度计算方法，而不是简化版本
            connectivity_score = self.network.calculate_patch_connectivity(node)
            source_confidence = self._evaluate_source_confidence(node)
            content_relevance = self._evaluate_content_relevance(node)
            
            confidence = self._calculate_overall_confidence(
                connectivity_score, source_confidence, content_relevance, node
            )
            
            # 获取源路径
            source_paths = self._get_source_paths(node)
            
            # 提取元数据
            metadata = self._extract_patch_metadata(node)
            
            patch_candidate = PatchCandidate(
                node=node,
                confidence=confidence,
                source_paths=source_paths,
                metadata=metadata
            )
            patch_candidates.append(patch_candidate)
        
        # 按置信度排序
        patch_candidates.sort(key=lambda p: p.confidence, reverse=True)
        
        # 记录找到的补丁数量
        logger.info(f"找到 {len(patch_candidates)} 个潜在补丁候选项")
        
        return patch_candidates
    
    def _calculate_confidence(self, node: ReferenceNode) -> float:
        """
        计算节点的置信度评分
        
        这是一个简化版本，结合了节点的连接度和网络位置来评估置信度
        
        Args:
            node: 要评估的节点
            
        Returns:
            float: 0.0到1.0之间的置信度评分
        """
        # 获取到节点的最短路径和所有路径
        try:
            # 计算连接度 - 基于到ROOT的路径数和这些路径的质量
            shortest_path = nx.shortest_path(self.network.graph, 
                                           self.network.root_node.node_id, 
                                           node.node_id)
            shortest_path_length = len(shortest_path) - 1
            
            # 路径越短，置信度越高
            path_score = 1.0 / max(1, shortest_path_length) 
            
            # 考虑节点的度数 - 连接越多的节点可能越重要
            node_degree = self.network.graph.degree(node.node_id)
            degree_score = min(node_degree / 5.0, 1.0)  # 最多5个连接得满分
            
            # 组合评分
            confidence = 0.7 * path_score + 0.3 * degree_score
            
            # 额外的提升因素
            boost = 0.0
            
            # 特定域名的补丁链接有更高可信度
            trusted_domains = ['github.com', 'gitlab.com', 'apache.org', 'linux.org']
            for domain in trusted_domains:
                if domain in node.url:
                    boost = max(boost, 0.1)
            
            # 明确的补丁URL模式
            patch_patterns = ['/commit/', '/patch/', '/pull/']
            for pattern in patch_patterns:
                if pattern in node.url:
                    boost = max(boost, 0.2)
                    
            # 应用提升
            confidence = min(confidence + boost, 1.0)
            
            return round(confidence, 2)
            
        except (nx.NetworkXNoPath, nx.NetworkXError):
            # 如果没有路径，给出非常低的置信度
            return 0.1
    
    def _evaluate_source_confidence(self, patch_node: ReferenceNode) -> float:
        """
        评估来源可信度
        
        Args:
            patch_node: 补丁节点
            
        Returns:
            float: 来源可信度得分(0.0-1.0)
        """
        # 检查节点来源是否为高可信来源
        if patch_node.source in self.high_confidence_sources:
            return 1.0
            
        # 检查父节点是否为高可信来源
        parent_nodes = []
        for edge in self.network.edges:
            if edge.target_id == patch_node.node_id:
                parent_node = self.network.get_node_by_id(edge.source_id)
                if parent_node:
                    parent_nodes.append(parent_node)
        
        for parent in parent_nodes:
            if parent.source in self.high_confidence_sources:
                return 0.8
                
        # 默认来源可信度
        return 0.5
    
    def _evaluate_content_relevance(self, patch_node: ReferenceNode) -> float:
        """
        评估内容相关性
        
        Args:
            patch_node: 补丁节点
            
        Returns:
            float: 内容相关性得分(0.0-1.0)
        """
        # GitHub提交通常相关性较高
        if 'github.com' in patch_node.url and '/commit/' in patch_node.url:
            return 0.9
            
        # GitHub PR通常相关性较高
        if 'github.com' in patch_node.url and '/pull/' in patch_node.url:
            return 0.85
            
        # GitLab提交和合并请求
        if 'gitlab.com' in patch_node.url and ('/commit/' in patch_node.url or '/merge_requests/' in patch_node.url):
            return 0.85
            
        # 其他补丁链接
        if any(kw in patch_node.url.lower() for kw in ['patch', 'fix', 'commit']):
            return 0.7
            
        # 默认相关性
        return 0.5
    
    def _calculate_overall_confidence(self, 
                                     connectivity_score: float, 
                                     source_confidence: float,
                                     content_relevance: float,
                                     node: ReferenceNode) -> float:
        """
        计算综合置信度
        
        Args:
            connectivity_score: 连接度得分
            source_confidence: 来源可信度
            content_relevance: 内容相关性
            node: 要评估的节点
            
        Returns:
            float: 综合置信度(0.0-1.0)
        """
        # 权重分配
        weights = {
            'connectivity': 0.5,    # 连接度权重最高
            'source': 0.3,          # 来源可信度次之
            'content': 0.2          # 内容相关性权重较低
        }
        
        # 计算加权得分
        confidence = (
            connectivity_score * weights['connectivity'] +
            source_confidence * weights['source'] +
            content_relevance * weights['content']
        )
        
        # 对直接从NVD引用的补丁，额外增加置信度
        if self.network.root_node:
            for edge in self.network.edges:
                if (edge.source_id == self.network.root_node.node_id and 
                    edge.target_id == node.node_id):
                    confidence = max(confidence, 0.8)
                    break
        
        # 确保在0.0-1.0范围内
        return max(0.0, min(1.0, confidence))
    
    def _get_source_paths(self, patch_node: ReferenceNode) -> List[List[str]]:
        """
        获取从根节点到补丁节点的所有路径
        
        Args:
            patch_node: 补丁节点
            
        Returns:
            List[List[str]]: 路径列表，每个路径是节点ID列表
        """
        if not self.network.root_node:
            return []
            
        return self.network.get_all_paths(self.network.root_node, patch_node)
    
    def _extract_patch_metadata(self, patch_node: ReferenceNode) -> Dict[str, Any]:
        """
        提取补丁元数据
        
        Args:
            patch_node: 补丁节点
            
        Returns:
            Dict[str, Any]: 元数据
        """
        metadata = {
            'type': patch_node.node_type.value,
            'source': patch_node.source
        }
        
        # 解析GitHub提交链接
        if 'github.com' in patch_node.url and '/commit/' in patch_node.url:
            try:
                # 例如: https://github.com/owner/repo/commit/hash
                match = re.search(r'github\.com/([^/]+)/([^/]+)/commit/([a-f0-9]+)', patch_node.url)
                if match:
                    owner, repo, commit_hash = match.groups()
                    metadata['repository'] = f"{owner}/{repo}"
                    metadata['commit_hash'] = commit_hash
                    
                    # 获取提交信息（如果有GitHub令牌）
                    if self.github_token:
                        commit_info = self._get_github_commit_info(owner, repo, commit_hash)
                        if commit_info:
                            metadata.update(commit_info)
            except Exception as e:
                logger.warning(f"提取GitHub提交信息时出错: {str(e)}")
                
        # 解析GitHub PR链接
        elif 'github.com' in patch_node.url and '/pull/' in patch_node.url:
            try:
                # 例如: https://github.com/owner/repo/pull/number
                match = re.search(r'github\.com/([^/]+)/([^/]+)/pull/(\d+)', patch_node.url)
                if match:
                    owner, repo, pr_number = match.groups()
                    metadata['repository'] = f"{owner}/{repo}"
                    metadata['pr_number'] = pr_number
                    
                    # 获取PR信息（如果有GitHub令牌）
                    if self.github_token:
                        pr_info = self._get_github_pr_info(owner, repo, pr_number)
                        if pr_info:
                            metadata.update(pr_info)
            except Exception as e:
                logger.warning(f"提取GitHub PR信息时出错: {str(e)}")
        
        # 解析GitLab提交链接
        elif 'gitlab.com' in patch_node.url and '/commit/' in patch_node.url:
            match = re.search(r'gitlab\.com/([^/]+)/([^/]+)/commit/([a-f0-9]+)', patch_node.url)
            if match:
                owner, repo, commit_hash = match.groups()
                metadata['repository'] = f"{owner}/{repo}"
                metadata['commit_hash'] = commit_hash
                
        # 解析GitLab合并请求链接
        elif 'gitlab.com' in patch_node.url and '/merge_requests/' in patch_node.url:
            match = re.search(r'gitlab\.com/([^/]+)/([^/]+)/merge_requests/(\d+)', patch_node.url)
            if match:
                owner, repo, mr_number = match.groups()
                metadata['repository'] = f"{owner}/{repo}"
                metadata['mr_number'] = mr_number
                
        return metadata
    
    def _get_github_commit_info(self, owner: str, repo: str, commit_hash: str) -> Optional[Dict[str, Any]]:
        """
        获取GitHub提交信息
        
        Args:
            owner: 仓库所有者
            repo: 仓库名称
            commit_hash: 提交哈希
            
        Returns:
            Optional[Dict[str, Any]]: 提交信息
        """
        if not self.github_token:
            return None
            
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_hash}"
            headers = {
                'Authorization': f'token {self.github_token}',
                'Accept': 'application/vnd.github.v3+json'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            return {
                'commit_message': data.get('commit', {}).get('message', ''),
                'author': data.get('commit', {}).get('author', {}).get('name', ''),
                'date': data.get('commit', {}).get('author', {}).get('date', ''),
                'files_changed': len(data.get('files', [])),
                'additions': sum(f.get('additions', 0) for f in data.get('files', [])),
                'deletions': sum(f.get('deletions', 0) for f in data.get('files', [])),
                'changed_files': [f.get('filename') for f in data.get('files', [])]
            }
            
        except Exception as e:
            logger.warning(f"获取GitHub提交信息时出错: {str(e)}")
            return None
    
    def _get_github_pr_info(self, owner: str, repo: str, pr_number: str) -> Optional[Dict[str, Any]]:
        """
        获取GitHub PR信息
        
        Args:
            owner: 仓库所有者
            repo: 仓库名称
            pr_number: PR编号
            
        Returns:
            Optional[Dict[str, Any]]: PR信息
        """
        if not self.github_token:
            return None
            
        try:
            url = f"https://api.github.com/repos/{owner}/{repo}/pulls/{pr_number}"
            headers = {
                'Authorization': f'token {self.github_token}',
                'Accept': 'application/vnd.github.v3+json'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            return {
                'pr_title': data.get('title', ''),
                'pr_description': data.get('body', ''),
                'author': data.get('user', {}).get('login', ''),
                'created_at': data.get('created_at', ''),
                'updated_at': data.get('updated_at', ''),
                'state': data.get('state', ''),
                'merged': data.get('merged', False),
                'merged_at': data.get('merged_at', ''),
                'commits': data.get('commits', 0),
                'additions': data.get('additions', 0),
                'deletions': data.get('deletions', 0),
                'changed_files': data.get('changed_files', 0)
            }
            
        except Exception as e:
            logger.warning(f"获取GitHub PR信息时出错: {str(e)}")
            return None
    
    def get_high_confidence_patches(self, confidence_threshold: float = 0.7) -> List[PatchCandidate]:
        """
        获取高置信度补丁
        
        Args:
            confidence_threshold: 置信度阈值(0.0-1.0)
            
        Returns:
            List[PatchCandidate]: 高置信度补丁列表
        """
        patches = self.identify_patches()
        return [p for p in patches if p.confidence >= confidence_threshold]
    
    def summarize_patch_candidates(self, patches: List[PatchCandidate]) -> str:
        """
        生成补丁候选摘要
        
        Args:
            patches: 补丁候选列表
            
        Returns:
            str: 摘要文本
        """
        if not patches:
            return "未发现补丁候选"
            
        lines = ["## 补丁候选摘要", ""]
        
        for i, patch in enumerate(patches, 1):
            lines.append(f"### {i}. {patch.node.url}")
            lines.append(f"- 置信度: {patch.confidence:.2f}")
            lines.append(f"- 类型: {patch.node.node_type.value}")
            lines.append(f"- 来源: {patch.node.source}")
            
            if patch.source_paths:
                lines.append("- 来源路径:")
                for j, path in enumerate(patch.source_paths, 1):
                    path_nodes = [self.network.get_node_by_id(node_id) for node_id in path]
                    path_str = " → ".join([n.source if n else "?" for n in path_nodes])
                    lines.append(f"  - 路径{j}: {path_str}")
            
            if 'commit_message' in patch.metadata:
                lines.append(f"- 提交信息: {patch.metadata['commit_message'].splitlines()[0]}")
                
            if 'repository' in patch.metadata:
                lines.append(f"- 仓库: {patch.metadata['repository']}")
                
            if 'files_changed' in patch.metadata:
                lines.append(f"- 更改统计: {patch.metadata['files_changed']}个文件, +{patch.metadata['additions']}, -{patch.metadata['deletions']}")
                
            if 'pr_title' in patch.metadata:
                lines.append(f"- PR标题: {patch.metadata['pr_title']}")
                
            lines.append("")
            
        return "\n".join(lines) 