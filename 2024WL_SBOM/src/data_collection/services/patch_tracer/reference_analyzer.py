#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
引用链接分析器模块

负责爬取和分析引用链接，从中提取URL，并构建参考网络。
支持递归爬取和深度控制，能够分析不同类型网页中的内容和链接。
"""

import os
import re
import time
import logging
import requests
import urllib.parse
from typing import List, Dict, Set, Any, Optional, Tuple
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

from .reference_node import ReferenceNode, NodeType
from .reference_network import ReferenceNetwork, Edge

logger = logging.getLogger(__name__)

class ReferenceAnalyzer:
    """
    引用链接分析器
    
    负责爬取和分析引用链接，从中提取URL，并构建参考网络
    支持递归爬取和深度控制
    """
    
    def __init__(self, 
                 max_depth: int = 2, 
                 request_delay: float = 1.0,
                 timeout: int = 30,
                 user_agent: Optional[str] = None,
                 proxy: Optional[Dict[str, str]] = None,
                 verify_ssl: bool = False,
                 max_retries: int = 8):
        """
        初始化分析器
        
        Args:
            max_depth: 最大爬取深度
            request_delay: 请求延迟(秒)
            timeout: 超时时间(秒)
            user_agent: 用户代理
            proxy: 代理设置 {"http": "http://...", "https": "https://..."}
            verify_ssl: 是否验证SSL证书
            max_retries: 最大重试次数
        """
        self.max_depth = max_depth
        self.request_delay = request_delay
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self.max_retries = max_retries
        self.user_agent = user_agent or 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        self.proxy = proxy
        self.domain_delays = {
            'nvd.nist.gov': 2.0,  # NVD网站需要更长的延迟，但降低到2秒
            'github.com': 1.0,    # GitHub延迟降低到1秒
            'gitlab.com': 1.0,    # GitLab延迟降低到1秒
        }
        
        # 设置会话和重试机制
        self.session = requests.Session()
        retry_strategy = Retry(
            total=max_retries,  # 使用传入的重试次数参数
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"],
            backoff_factor=1.5,  # 降低退避因子
            respect_retry_after_header=True,
            raise_on_status=False,  # 不抛出状态错误异常
            remove_headers_on_redirect=["Authorization"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=10)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0',
            'DNT': '1'  # 请勿跟踪
        })
        
        if self.proxy:
            self.session.proxies.update(self.proxy)
    
    def create_reference_network(self, cve_id: str, references: List[str], sources: Optional[List[str]] = None, 
                              cve_published_date: Optional[str] = None, cve_affected_cpes: Optional[List[str]] = None) -> ReferenceNetwork:
        """
        创建参考网络
        
        按照正确的层次结构：CVE根节点 -> 来源节点(RedHat/NVD/GitHub等) -> URL节点
        所有初始URL节点(即从NVD API获取的URL)都与NVD节点建立连接
        
        Args:
            cve_id: CVE ID
            references: 初始引用URL列表
            sources: 引用来源列表(可选)
            cve_published_date: CVE发布日期，ISO 8601格式如 '2023-01-15T10:00:00Z'
            cve_affected_cpes: 受影响的CPE列表
            
        Returns:
            ReferenceNetwork: 构建的参考网络
        """
        # 创建网络 - 传递CVE发布日期和CPE信息
        network = ReferenceNetwork(cve_id, cve_published_date, cve_affected_cpes)
        
        # 创建CVE根节点
        root_node = ReferenceNode(
            url=f"https://nvd.nist.gov/vuln/detail/{cve_id}",
            title=f"{cve_id}",
            node_type=NodeType.ROOT,
            source="ROOT",
            depth=0
        )
        network.add_node(root_node)
        # 设置网络的根节点
        network.root_node = root_node
        
        # 添加来源节点 - 预定义所有可能的来源
        common_sources = {
            "NVD": "nvd.nist.gov",
            "GITHUB": "github",
            "GITLAB": "gitlab",
            "DEBIAN": "debian",
            "REDHAT": "redhat",
            "APACHE": "apache",
            "OTHER": "other.source"  # OTHER也预定义，确保唯一性
        }
        
        # 规范化传入的来源名称
        if sources:
            # 统一为大写，确保一致性
            sources = [s.upper() if s else s for s in sources]
            # 确保所有来源都被识别，将不在预定义列表中的归为"OTHER"
            sources = [s if s in common_sources else "OTHER" for s in sources]
        
        # 先创建所有来源节点，确保唯一性
        source_nodes = {}
        for source_name, source_domain in common_sources.items():
            # 检查是否已经创建了该来源的节点（防止重复）
            existing_node = None
            for node in network.nodes:
                if node.node_type == NodeType.SOURCE and node.source == source_name:
                    existing_node = node
                    break
            
            # 如果已存在，使用现有节点
            if existing_node:
                source_nodes[source_name] = existing_node
            else:
                # 否则创建新节点
                source_node = ReferenceNode(
                    url=f"https://{source_domain}",
                    title=source_name,
                    node_type=NodeType.SOURCE,
                    source=source_name,
                    depth=1
                )
                network.add_node(source_node)
                network.add_edge(root_node, source_node, 
                                weight=0.8 if source_name != "OTHER" else 0.5, 
                                description="来源" if source_name != "OTHER" else "其他来源")
                source_nodes[source_name] = source_node
        
        # 添加引用节点
        visited_urls = set()
        has_direct_patches = False
        
        # 处理传入的来源信息（如果提供）
        url_to_source_map = {}
        if sources and len(sources) == len(references):
            url_to_source_map = dict(zip(references, sources))
        
        # 确保NVD节点存在
        nvd_node = source_nodes.get("NVD")
        if not nvd_node:
            logger.warning("NVD来源节点不存在，无法建立初始引用与NVD的连接")
        
        # 处理初始引用
        for i, url in enumerate(references):
            # 标准化URL
            normalized_url = self._normalize_url(url)
            if not normalized_url or normalized_url in visited_urls:
                continue
                
            visited_urls.add(normalized_url)
            
            # 识别节点类型
            node_type, category = ReferenceNode.identify_node_type(normalized_url)
            
            # 确定该链接的来源
            original_source = None
            if url in url_to_source_map:
                # 优先使用传入的来源信息
                original_source = url_to_source_map[url]
            
            # 作为后备选项，根据URL域名判断来源
            if not original_source or original_source not in source_nodes:
                for source_name, source_domain in common_sources.items():
                    if source_domain in normalized_url and source_name != "OTHER":
                        original_source = source_name
                        break
            
            # 如果仍未确定来源，归为"OTHER"
            if not original_source or original_source not in source_nodes:
                original_source = "OTHER"
            
            # 创建节点
            node = ReferenceNode(
                url=normalized_url,
                title=self._extract_title(normalized_url),
                node_type=node_type,
                source=original_source,  # 使用确定的来源
                depth=2,
                category_in_type=category
            )
            
            # 添加节点到网络
            network.add_node(node)
            
            # 1. 连接到其对应的来源平台节点
            if original_source in source_nodes:
                network.add_edge(source_nodes[original_source], node, weight=1.0, description="引用")
            
            # 2. 所有初始引用都额外连接到NVD节点（因为它们都是从NVD获取的）
            if nvd_node and original_source != "NVD":
                network.add_edge(nvd_node, node, weight=0.7, description="NVD引用")
                
            # 对于补丁节点，添加直接连接
            if node.node_type == NodeType.PATCH:
                network.add_edge(root_node, node, weight=0.9, description="直接补丁")
                has_direct_patches = True
        
        # 根据深度选择不同的分析策略
        if self.max_depth <= 2:
            # 浅层分析: 分析所有初始节点
            for url in references:
                normalized_url = self._normalize_url(url)
                if normalized_url:
                    node = network.get_node_by_url(normalized_url)
                    if node:
                        self._analyze_references_recursive(network, node, visited_urls, 2, is_initial_reference=True)
        else:
            # 深层分析: 只分析重要节点
            logger.info(f"深度 > 2，仅限分析重要节点")
            
            # 设置不同深度对应的URL数量限制
            max_urls_per_depth = {
                3: 20,  # 深度3最多分析20个URL
                4: 10,  # 深度4最多分析10个URL
                5: 5    # 深度5最多分析5个URL
            }
            
            # 根据当前深度设置限制
            max_analyzed_urls = max_urls_per_depth.get(self.max_depth, 5)
            
            # 计算节点优先级
            network.compute_node_priorities(network.nodes, method='DP')
            
            # 1. 获取最重要的补丁节点
            patch_rules = {
                'patch_type': ['git_commit', 'svn'],
                'cutoff': str(self.max_depth)
            }
            patch_nodes = network.get_patches_by_rules(patch_rules)
            sorted_patch_nodes = sorted(patch_nodes, key=lambda n: n.importance_score, reverse=True)
            prioritized_patch_nodes = sorted_patch_nodes[:min(len(sorted_patch_nodes), max_analyzed_urls // 2)]
            
            # 2. 获取最重要的问题节点
            issue_nodes = [n for n in network.nodes if n.node_type == NodeType.ISSUE]
            sorted_issue_nodes = sorted(issue_nodes, key=lambda n: n.importance_score, reverse=True)
            prioritized_issue_nodes = sorted_issue_nodes[:min(len(sorted_issue_nodes), max_analyzed_urls // 4)]
            
            # 3. 如果还有空位，加入一些普通节点
            remaining_slots = max_analyzed_urls - len(prioritized_patch_nodes) - len(prioritized_issue_nodes)
            prioritized_other_nodes = []
            if remaining_slots > 0:
                other_nodes = [n for n in network.nodes 
                              if n.node_type == NodeType.COMMON 
                              and n not in prioritized_patch_nodes
                              and n not in prioritized_issue_nodes]
                sorted_other_nodes = sorted(other_nodes, key=lambda n: n.importance_score, reverse=True)
                prioritized_other_nodes = sorted_other_nodes[:remaining_slots]
            
            # 合并所有重要节点
            important_nodes = prioritized_patch_nodes + prioritized_issue_nodes + prioritized_other_nodes
            
            logger.info(f"选择了 {len(important_nodes)} 个重要节点进行深度分析: "
                       f"{len(prioritized_patch_nodes)} 个补丁节点, "
                       f"{len(prioritized_issue_nodes)} 个问题节点, "
                       f"{len(prioritized_other_nodes)} 个其他节点")
            
            # 记录这些URL已被分析
            important_urls = set(node.url for node in important_nodes)
            visited_urls = visited_urls.union(important_urls)
            
            # 对每个重要节点进行递归分析
            for node in important_nodes:
                # 判断是否为初始引用节点
                is_initial = node.url in references
                self._analyze_references_recursive(network, node, visited_urls, 2, is_initial_reference=is_initial)
        
        # 构建图
        network.build_graph()
        
        logger.info(f"引用网络构建完成: {len(network.nodes)} 个节点, {len(network.edges)} 条边")
        
        return network
        
    def _analyze_references_recursive(self, 
                                     network: ReferenceNetwork, 
                                     parent_node: ReferenceNode,
                                     visited_urls: Set[str],
                                     current_depth: int,
                                     is_initial_reference: bool = False) -> None:
        """
        递归分析引用
        
        Args:
            network: 参考网络
            parent_node: 父节点
            visited_urls: 已访问的URL集合
            current_depth: 当前深度
            is_initial_reference: 是否是初始引用
        """
        # 超过最大深度，停止递归
        if current_depth >= self.max_depth:
            return
            
        logger.debug(f"Analyzing node at depth {current_depth}: {parent_node.url}")
        
        try:
            # 获取域名
            domain = urlparse(parent_node.url).netloc
            
            # 根据域名使用不同的延迟时间
            delay = self.domain_delays.get(domain, self.request_delay)
            logger.debug(f"Using delay of {delay}s for domain {domain}")
            
            # 请求延迟
            time.sleep(delay)
            
            # 对于深度大于2的节点，进一步限制URL数
            max_urls_to_extract = 50  # 默认值
            if current_depth > 2:
                max_urls_to_extract = 30 // (current_depth - 1)  # 随深度增加而减少
            
            # 爬取页面
            page_content = self._get_page_content(parent_node.url, is_initial_reference=is_initial_reference)
            if not page_content:
                return
            
            # 更新节点标题
            if not parent_node.title and isinstance(page_content, str):
                parent_node.title = self._extract_title_from_html(page_content) or ""
            
            # 提取URL
            if isinstance(page_content, str):
                extracted_urls = self._extract_urls_from_page(
                    page_content, 
                    parent_node.url, 
                    max_urls=max_urls_to_extract
                )
                logger.debug(f"Extracted {len(extracted_urls)} URLs from {parent_node.url}")
                
                # 处理提取的URL
                self._process_extracted_urls(
                    network, 
                    parent_node, 
                    extracted_urls, 
                    current_depth + 1, 
                    visited_urls
                )
                
        except Exception as e:
            logger.warning(f"Error analyzing {parent_node.url}: {str(e)}")
            return
    
    def _get_page_content(self, url: str, is_initial_reference: bool = False) -> Optional[str]:
        """
        获取页面内容，带有重试和异常处理
        
        Args:
            url: 页面URL
            is_initial_reference: 是否是初始引用URL，如果是只重试一次
            
        Returns:
            Optional[str]: 页面内容，失败返回None
        """
        # 对特殊域名使用更稳健的请求策略
        special_domains = ['nvd.nist.gov', 'cve.mitre.org', 'github.com']
        is_special_domain = any(domain in url for domain in special_domains)
        
        # 确定重试次数
        # 优先使用传入的实例参数max_retries，但对非关键网站限制重试
        if is_initial_reference:
            # 对于初始引用，始终只重试一次
            retry_count = 1
        elif self.max_retries <= 1:
            # 如果用户设置了最大重试为0或1，尊重这个设置
            retry_count = self.max_retries
        else:
            # 否则使用动态重试策略
            retry_count = self.max_retries if is_special_domain else min(self.max_retries, 1)
        
        # 检查是否为404常见错误URL，避免浪费时间
        skip_patterns = [
            # 已知404或不可访问的域名后缀
            '.fedoraproject.org/message/',
            'cert-portal.siemens.com/productcert/pdf',
            '/user-images/',
            '.deb.debian.org',
            '.twitter.com/',
            '.facebook.com/'
        ]
        
        if any(pattern in url for pattern in skip_patterns):
            logger.debug(f"跳过可能不可访问的URL: {url}")
            return None
        
        # 尝试多次请求
        for attempt in range(retry_count):
            try:
                # 增加请求头，更像浏览器
                headers = {
                    'User-Agent': self.user_agent or 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml',
                    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                    'Connection': 'keep-alive',
                    'DNT': '1',
                    'Upgrade-Insecure-Requests': '1'
                }
                
                # 设置超时 - 减少超时时间
                timeout = min(self.timeout, 15) * (1.5 if is_special_domain else 1)
                
                # 发送请求
                response = self.session.get(
                    url,
                    headers=headers,
                    timeout=timeout,
                    allow_redirects=True,
                    verify=self.verify_ssl
                )
                
                # 对于客户端错误（如404），直接跳过重试
                if 400 <= response.status_code < 500:
                    logger.debug(f"客户端错误 {response.status_code} 访问 {url}，不再重试")
                    return None
                
                response.raise_for_status()
                
                # 返回内容
                if 'text/html' in response.headers.get('Content-Type', ''):
                    return response.text
                elif 'application/json' in response.headers.get('Content-Type', ''):
                    return response.text
                else:
                    logger.debug(f"跳过非HTML/JSON内容: {url}")
                    return None
                
            except requests.exceptions.RequestException as e:
                # 区分不同类型的异常，对有些不再重试
                if isinstance(e, requests.exceptions.ConnectionError):
                    logger.warning(f"连接错误: {url} - {str(e)}")
                    return None  # 连接错误不再重试
                
                if isinstance(e, requests.exceptions.Timeout):
                    logger.warning(f"请求超时: {url}")
                    if attempt >= retry_count - 1:
                        return None  # 最后一次超时直接返回
                
                # 其他请求异常
                wait_time = self.request_delay * (attempt + 1)
                logger.warning(f"尝试 {attempt+1}/{retry_count} 失败: {url}: {str(e)}")
                
                if attempt < retry_count - 1:
                    logger.debug(f"等待 {wait_time}秒 后重试")
                    time.sleep(wait_time)
            except Exception as e:
                logger.error(f"未预期的错误: {url} - {str(e)}")
                return None  # 未预期的错误不再重试
        
        logger.error(f"请求失败: {url} (重试 {retry_count} 次后)")
        return None
    
    def _normalize_url(self, url: str) -> Optional[str]:
        """
        规范化URL，过滤无效URL，确保URL一致性
        
        Args:
            url: 原始URL
            
        Returns:
            Optional[str]: 规范化后的URL，无效则返回None
        """
        if not url:
            return None
            
        try:
            # 确保URL有协议头
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
                
            # 解析URL
            parsed = urlparse(url)
            if not parsed.netloc:
                return None
                
            # 解码URL
            url = urllib.parse.unquote(url)
            
            # 检查是否为gitweb URL
            if 'gitweb' in url and ('commitdiff' in url or 'a=commit' in url or 'a=commitdiff' in url):
                # 为gitweb URL保留查询参数
                normalized_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
                if parsed.query:
                    normalized_url += f"?{parsed.query}"
                return normalized_url.rstrip('/')
            
            # 移除URL片段和查询参数
            base_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
            
            # 移除尾部斜杠
            base_url = base_url.rstrip('/')
            
            # 标准化路径部分的大小写（对于特定域名）
            if 'github.com' in parsed.netloc or 'gitlab.com' in parsed.netloc:
                # GitHub/GitLab URLs应该保留大小写
                pass
            else:
                # 其他域名可以标准化路径大小写
                path_parts = parsed.path.split('/')
                path_parts = [p.lower() for p in path_parts if p]
                base_url = f"{parsed.scheme}://{parsed.netloc.lower()}/{'/'.join(path_parts)}"
            
            # 过滤无用URL
            unwanted_patterns = [
                'twitter.com', 'facebook.com', 'linkedin.com', 'youtube.com',
                'javascript:', 'mailto:', 'tel:', '.jpg', '.png', '.gif',
                '.css', '.js', 'api.', '.xml', '.json', '.txt'
            ]
            if any(pattern in base_url for pattern in unwanted_patterns):
                return None
                
            return base_url
            
        except Exception as e:
            logger.debug(f"Error normalizing URL {url}: {str(e)}")
            return None
    
    def _extract_title(self, url: str) -> str:
        """
        从URL中提取可能的标题
        
        Args:
            url: URL
            
        Returns:
            str: 提取的标题
        """
        try:
            # 从URL路径中提取
            path = urlparse(url).path
            segments = [seg for seg in path.split('/') if seg]
            
            if segments:
                # 使用最后一个非空段
                last_segment = segments[-1]
                
                # 移除文件扩展名
                if '.' in last_segment:
                    last_segment = last_segment.split('.')[0]
                
                # 将连字符和下划线替换为空格
                title = last_segment.replace('-', ' ').replace('_', ' ')
                
                # 首字母大写
                return title.capitalize()
                
        except Exception:
            pass
            
        return ""
    
    def _extract_title_from_html(self, html_content: str) -> Optional[str]:
        """
        从HTML内容中提取标题
        
        Args:
            html_content: HTML内容
            
        Returns:
            Optional[str]: 提取的标题
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 尝试找到标题标签
            if soup.title:
                return soup.title.text.strip()
                
            # 尝试找到主要标题标签
            for header in soup.find_all(['h1', 'h2'], limit=2):
                return header.text.strip()
                
        except Exception:
            pass
            
        return None
    
    def _extract_urls_from_page(self, html_content: str, base_url: str, max_urls: int = 50) -> List[str]:
        """
        从页面中提取URL，并进行过滤和去重
        
        Args:
            html_content: HTML内容
            base_url: 基础URL
            max_urls: 最大URL数量
            
        Returns:
            List[str]: 提取的URL列表（已去重）
        """
        extracted_urls = []
        extracted_urls_set = set()  # 用于快速去重
        
        base_url_host = urlparse(base_url).netloc.lower()
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 特殊处理GitHub页面 - 尤其是Pull Request页面
            if 'github.com' in base_url and ('/pull/' in base_url or '/issues/' in base_url):
                logger.debug(f"特殊处理GitHub页面: {base_url}")
                
                # 1. 先提取commits相关链接 (高优先级)
                # 查找 commits 列表元素
                commits_elements = soup.find_all(['div', 'a'], class_=lambda x: x and ('commits' in x.lower() or 'commit-' in x.lower()))
                commits_links = []
                
                # 如果找不到专门的commits元素，尝试从整个页面找commit链接
                if not commits_elements:
                    # 尝试通过href属性找commit链接
                    commits_links = soup.find_all('a', href=lambda x: x and ('/commit/' in x or '/commits/' in x))
                else:
                    # 从找到的commits元素中提取链接
                    for element in commits_elements:
                        links = element.find_all('a', href=True)
                        commits_links.extend(links)
                
                # 处理找到的commits链接
                for a_tag in commits_links:
                    href = a_tag['href']
                    
                    # 转为绝对URL
                    if href.startswith(('http://', 'https://')):
                        absolute_url = href
                    else:
                        absolute_url = urljoin(base_url, href)
                    
                    # 只保留commit链接
                    if '/commit/' in absolute_url or '/commits/' in absolute_url:
                        normalized_url = self._normalize_url(absolute_url)
                        if normalized_url and normalized_url not in extracted_urls_set:
                            extracted_urls.append(normalized_url)
                            extracted_urls_set.add(normalized_url)
                            
                            # 检查是否已达到上限
                            if len(extracted_urls) >= max_urls:
                                return extracted_urls
                
                # 2. 提取关联issue链接 (中优先级)
                issue_links = soup.find_all('a', href=lambda x: x and ('/issues/' in x))
                for a_tag in issue_links:
                    href = a_tag['href']
                    
                    # 转为绝对URL
                    absolute_url = urljoin(base_url, href)
                    
                    # 规范化并添加
                    normalized_url = self._normalize_url(absolute_url)
                    if normalized_url and normalized_url not in extracted_urls_set:
                        extracted_urls.append(normalized_url)
                        extracted_urls_set.add(normalized_url)
                        
                        # 检查是否已达到上限
                        if len(extracted_urls) >= max_urls:
                            return extracted_urls
            
            # 首先处理主要内容区域的链接
            main_content_tags = soup.find_all(['div', 'main', 'article', 'section'], 
                                             class_=lambda x: x and ('content' in x.lower() or 
                                                                  'main' in x.lower() or
                                                                  'body' in x.lower()))
            
            # 如果找到主要内容区域，优先从中提取链接
            if main_content_tags:
                for content_area in main_content_tags:
                    self._process_links_from_element(content_area, base_url, extracted_urls, extracted_urls_set, 
                                               max_urls // 2, base_url_host)
            
            # 如果主要内容区域的链接不足，再从整个页面提取
            if len(extracted_urls) < max_urls:
                remaining_urls = max_urls - len(extracted_urls)
                self._process_links_from_element(soup, base_url, extracted_urls, extracted_urls_set, 
                                           remaining_urls, base_url_host)
                        
        except Exception as e:
            logger.warning(f"Error extracting URLs from {base_url}: {str(e)}")
            
        return extracted_urls
    
    def _process_links_from_element(self, element, base_url: str, 
                              extracted_urls: List[str], extracted_urls_set: Set[str], 
                              max_urls: int, base_url_host: str):
        """
        从HTML元素中处理链接
        
        Args:
            element: BeautifulSoup元素
            base_url: 基础URL
            extracted_urls: 提取的URL列表
            extracted_urls_set: 提取的URL集合（用于快速去重）
            max_urls: 最大URL数量
            base_url_host: 基础URL主机名
        """
        # 优先级1: 补丁和问题相关链接
        priority_patterns = [
            r'/commit/', r'/pull/', r'/issues/', r'/merge_requests/',
            r'/bug/', r'/patch/', r'/svn/', r'/git/', r'/jira/'
        ]
        
        # 优先级2: 同域名、包含版本号或CVE的链接
        medium_patterns = [
            r'v\d+\.\d+', r'\bCVE-', r'/release/', r'/security/',
            r'/vuln/', r'/changelog/', r'/advisory/'
        ]
        
        # 获取所有链接
        all_links = element.find_all('a', href=True)
        
        # 按优先级处理链接
        for priority, patterns in enumerate([priority_patterns, medium_patterns, []]):
            for a_tag in all_links:
                href = a_tag['href']
                
                # 排除锚链接
                if href.startswith('#'):
                    continue
                
                # 转为绝对URL
                if href.startswith(('http://', 'https://')):
                    absolute_url = href
                elif href.startswith('/'):
                    # 获取基础URL的协议和域名
                    parsed_base = urlparse(base_url)
                    base = f"{parsed_base.scheme}://{parsed_base.netloc}"
                    absolute_url = f"{base}{href}"
                else:
                    # 相对URL，使用urljoin处理
                    absolute_url = urljoin(base_url, href)
                
                # 根据优先级过滤
                if priority < 2:  # 对于优先级1和2，检查是否匹配模式
                    match_pattern = False
                    for pattern in patterns:
                        if re.search(pattern, absolute_url, re.IGNORECASE):
                            match_pattern = True
                            break
                    if not match_pattern:
                        continue
                        
                # 规范化URL
                normalized_url = self._normalize_url(absolute_url)
                if normalized_url and normalized_url not in extracted_urls_set:
                    # 添加到结果
                    extracted_urls.append(normalized_url)
                    extracted_urls_set.add(normalized_url)
                    
                    # 检查是否达到上限
                    if len(extracted_urls) >= max_urls:
                        return
        
    def _process_extracted_urls(self, 
                               network: ReferenceNetwork, 
                               parent_node: ReferenceNode, 
                               urls: List[str],
                               new_depth: int,
                               visited_urls: Set[str]) -> None:
        """
        处理从页面提取的URL
        
        此方法是构建引用网络的核心，它处理爬取过程中发现的URL，并将它们添加到网络中
        
        具体实现：
        1. 规范化和过滤URL
        2. 识别URL类型（补丁、问题、仓库等）
        3. 根据URL类型进行筛选和优先级排序
        4. 创建ReferenceNode节点并添加到网络
        5. 根据平台关键词匹配确定节点归属的平台
        6. 建立节点间的连接关系
        
        平台识别逻辑：
        - 使用简单的关键词匹配来识别节点所属平台
        - 提取URL的主机名并在其中查找平台关键词（如'github'、'gitlab'等）
        - 这允许正确识别子域名上的平台，如'gitlab.gnome.org'会被识别为'GITLAB'
        - 如果找不到匹配的平台关键词，节点归类为'OTHER'
        
        Args:
            network: 引用网络实例
            parent_node: 父节点
            urls: 从父节点页面提取的URL列表
            new_depth: 新节点的深度
            visited_urls: 已访问URL集合
        """
        # 对于不同深度，限制处理的URL数量
        max_urls_per_depth = {
            2: 25,
            3: 15,
            4: 7,
            5: 3
        }
        
        max_urls = max_urls_per_depth.get(new_depth, 3)
        
        # 1. 去重处理提取的URL并标准化
        normalized_urls = []
        for url in urls:
            normalized_url = self._normalize_url(url)
            if not normalized_url:
                continue
                
            # 检查是否已经访问过
            already_visited = False
            
            # 精确匹配
            if normalized_url in visited_urls:
                already_visited = True
                
            # 如果不是精确匹配，尝试变体匹配（不区分结尾斜杠）
            if not already_visited:
                normalized_url_no_slash = normalized_url.rstrip('/')
                for visited_url in visited_urls:
                    if visited_url.rstrip('/') == normalized_url_no_slash:
                        already_visited = True
                        break
            
            if not already_visited:
                normalized_urls.append(normalized_url)
                visited_urls.add(normalized_url)
                
        # 2. 分类URL
        # PR commit链接具有更高优先级
        pr_commit_urls = []  # PR中的commit链接
        patch_urls = []      # 其他补丁链接
        issue_urls = []      # issue链接
        other_urls = []      # 其他链接
        
        for url in normalized_urls:
            # 识别节点类型
            node_type, category = ReferenceNode.identify_node_type(url)
            
            # PR commit链接(最高优先级)
            if (node_type == NodeType.PATCH and 
                ('/pull/' in url or '/merge_requests/' in url) and
                ('/commit/' in url or '/commits/' in url)):
                pr_commit_urls.append((url, node_type, category))
            # 普通补丁链接(次高优先级)
            elif node_type == NodeType.PATCH:
                patch_urls.append((url, node_type, category))
            # issue链接(中等优先级)
            elif node_type == NodeType.ISSUE:
                issue_urls.append((url, node_type, category))
            # 其他链接(低优先级)
            else:
                # 过滤无关的URL类型
                is_low_value = (
                    '/search?' in url or 
                    '/assets/' in url or
                    '/docs/' in url or
                    '/about/' in url or
                    '/help/' in url or
                    '/support/' in url
                )
                
                if not is_low_value:
                    other_urls.append((url, node_type, category))
        
        # 3. 基于Github PR/Issue页面特殊处理
        if parent_node.node_type == NodeType.ISSUE and 'github.com' in parent_node.url:
            # 对于Github PR, 优先选择其中的commit
            if '/pull/' in parent_node.url and pr_commit_urls:
                # 确保PR commit得到足够重视
                max_pr_commits = min(max_urls // 2, len(pr_commit_urls))
                max_patches = min(max_urls // 4, len(patch_urls))
                max_issues = min(max_urls // 4, len(issue_urls))
                max_others = max(0, max_urls - max_pr_commits - max_patches - max_issues)
                
                selected_urls = (pr_commit_urls[:max_pr_commits] + 
                                 patch_urls[:max_patches] + 
                                 issue_urls[:max_issues] + 
                                 other_urls[:max_others])
            else:
                # 对于其他Github issues，平衡选择
                max_patches = min(max_urls // 3, len(patch_urls) + len(pr_commit_urls))
                max_issues = min(max_urls // 3, len(issue_urls))
                max_others = max(0, max_urls - max_patches - max_issues)
                
                selected_urls = (pr_commit_urls + patch_urls)[:max_patches] + issue_urls[:max_issues] + other_urls[:max_others]
        else:
            # 4. 一般情况，平衡选择各类URL
            total_slots = min(max_urls, len(pr_commit_urls) + len(patch_urls) + len(issue_urls) + len(other_urls))
            
            # 对于补丁节点父节点，优先选择issue节点
            if parent_node.node_type == NodeType.PATCH:
                max_issues = min(total_slots // 2, len(issue_urls))
                max_patches = min(total_slots // 4, len(pr_commit_urls) + len(patch_urls))
                max_others = total_slots - max_issues - max_patches
                
                selected_urls = (pr_commit_urls + patch_urls)[:max_patches] + issue_urls[:max_issues] + other_urls[:max_others]
            # 对于其他类型父节点
            else:
                max_patches = min(total_slots // 2, len(pr_commit_urls) + len(patch_urls))
                max_issues = min(total_slots // 3, len(issue_urls))
                max_others = total_slots - max_patches - max_issues
                
                selected_urls = (pr_commit_urls + patch_urls)[:max_patches] + issue_urls[:max_issues] + other_urls[:max_others]
        
        # 记录新增节点数
        added_nodes_count = 0
        
        # 获取根节点和来源节点
        root_nodes = [n for n in network.nodes if n.node_type == NodeType.ROOT]
        root_node = root_nodes[0] if root_nodes else None
        
        # 创建节点，但尚未添加到网络
        temp_nodes = []
        for url, node_type, category in selected_urls:
            # 检查是否已有相同URL的节点
            existing_node = network.get_node_by_url(url)
            if existing_node:
                # 如果节点已存在，只添加边
                if parent_node.node_id != existing_node.node_id:  # 防止自环
                    # 如果是PR中的commit，设置更高的权重
                    weight = 1.5 if ('/pull/' in url and '/commit' in url) else 1.0 / new_depth
                    network.add_edge(parent_node, existing_node, weight=weight, description="引用")
                continue
            
            # 创建节点
            node = ReferenceNode(
                url=url,
                node_type=node_type,
                source=parent_node.source,
                depth=new_depth,
                category_in_type=category
            )
            
            # 临时存储节点，以便后续过滤
            temp_nodes.append(node)
        
        # 对临时节点进行过滤，特别是对issue节点的子节点
        filtered_nodes = self._filter_identified_nodes(temp_nodes, parent_node)
        
        # 添加过滤后的节点到网络
        for node in filtered_nodes:
            # 添加到网络
            node_id = network.add_node(node)
            added_nodes_count += 1
            
            # 确定主机域名，用于找到合适的来源节点
            host = urlparse(node.url).netloc
            
            # 确定来源节点
            source_node = None
            common_sources = {
                "nvd.nist.gov": "NVD",
                "cve.mitre.org": "MITRE",
                "github.com": "GITHUB",
                "gitlab.com": "GITLAB",
                "debian.org": "DEBIAN",
                "ubuntu.com": "UBUNTU", 
                "redhat.com": "REDHAT",
                "apache.org": "APACHE"
            }
            
            parent_source = None
            for domain, source_name in common_sources.items():
                if domain in host:
                    parent_source = source_name
                    # 查找对应的来源节点
                    for n in network.nodes:
                        if n.node_type == NodeType.SOURCE and n.source == source_name:
                            source_node = n
                            break
                    break
            
            # 根据节点类型建立不同的连接
            
            # 1. 与父节点的连接
            # 对于PR中的commit节点，添加更高权重的边
            if ('/pull/' in node.url and '/commit' in node.url):
                network.add_edge(parent_node, node, weight=1.5, description="PR中的提交")
            else:
                network.add_edge(parent_node, node, weight=1.0 / new_depth, description="引用")
            
            # 2. 与来源节点的连接（如果存在）
            if source_node:
                network.add_edge(source_node, node, weight=0.9, description="来源引用")
            
            # 3. 对于补丁节点，建立与根节点的直接连接
            if node.node_type == NodeType.PATCH and root_node:
                # 如果是PR中的commit，设置更高的权重
                weight = 1.0 if ('/pull/' in node.url and '/commit' in node.url) else 0.8
                network.add_edge(root_node, node, weight=weight, description="关联补丁")
            
            # 控制新增节点数量，避免网络过大
            if added_nodes_count >= max_urls:
                break
                
        # 递归分析重要节点
        for node in network.nodes:
            # 只分析当前访问层次中的issue节点
            if (node.depth == new_depth and 
                node.node_type == NodeType.ISSUE and  # 只有issue节点才递归扩展
                new_depth < self.max_depth):
                # 避免重复分析
                if node.url in [url for url, _, _ in selected_urls]:
                    self._analyze_references_recursive(network, node, visited_urls, new_depth, is_initial_reference=False)
    
    def analyze_page_content(self, url: str) -> Dict[str, Any]:
        """
        分析页面内容
        
        Args:
            url: 页面URL
            
        Returns:
            Dict: 分析结果
        """
        result = {
            "url": url,
            "title": "",
            "extracted_urls": [],
            "content_type": "",
            "text_content": ""
        }
        
        try:
            # 请求页面
            response = self.session.get(
                url, 
                timeout=self.timeout, 
                allow_redirects=True,
                verify=self.verify_ssl
            )
            response.raise_for_status()
            
            # 获取内容类型
            content_type = response.headers.get('Content-Type', '')
            result["content_type"] = content_type
            
            # 处理HTML内容
            if 'text/html' in content_type:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取标题
                if soup.title:
                    result["title"] = soup.title.text.strip()
                    
                # 提取正文文本
                text_content = ""
                for p in soup.find_all(['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                    text = p.get_text(strip=True)
                    if text:
                        text_content += text + "\n"
                        
                result["text_content"] = text_content
                
                # 提取URL
                extracted_urls = self._extract_urls_from_page(response.text, url)
                result["extracted_urls"] = extracted_urls
                
            # 其他内容类型
            else:
                # 尝试获取部分文本内容
                try:
                    result["text_content"] = response.text[:1000] if response.text else ""
                except:
                    pass
                    
        except Exception as e:
            logger.warning(f"分析页面内容时出错: {url} - {str(e)}")
            
        return result 

    def _filter_identified_nodes(self, children_nodes, parent_node):
        """
        对识别出的节点进行过滤，主要针对issue节点的子节点进行过滤
        基于原始patch-tracer项目中的filter_identified_nodes函数逻辑
        
        Args:
            children_nodes: 待过滤的子节点列表
            parent_node: 父节点
            
        Returns:
            List: 过滤后的节点列表
        """
        selected_nodes = []
        selected_urls = set()  # 用于去重
        selected_ids = set()   # 用于跟踪已添加节点的ID
        
        # 节点分类计数
        node_type_counts = {
            'patch': 0,
            'issue': 0,
            'other': 0
        }
        
        # 已发现的补丁节点URL的SHA值（用于避免相同补丁的不同URL表示）
        patch_sha_values = set()
        
        # 1. GitHub issues/PRs的特殊处理
        if (parent_node.node_type == NodeType.ISSUE and 
            parent_node.category_in_type in ['git_issue', 'git_pull_request'] and
            'github.com' in parent_node.url):
            
            # 提取仓库信息
            parent_repo = None
            try:
                url_parts = parent_node.url.split('github.com/')
                if len(url_parts) > 1:
                    repo_path = url_parts[1].split('/issues')[0].split('/pull')[0]
                    if '/' in repo_path:
                        parent_repo = repo_path
            except:
                pass
                
            parent_repos = [parent_repo] if parent_repo else []
            
            # 识别可能的仓库所有者变更情况
            if parent_repo and '/' in parent_repo:
                owner = parent_repo.split('/')[0]
                repo = parent_repo.split('/')[-1]
                
                # 提取issue或PR编号
                issue_number = None
                try:
                    if '/issues/' in parent_node.url:
                        issue_number = parent_node.url.split('/issues/')[-1].split('/')[0].split('#')[0]
                    elif '/pull/' in parent_node.url:
                        issue_number = parent_node.url.split('/pull/')[-1].split('/')[0].split('#')[0]
                except:
                    pass
                
                # 检查是否有仓库所有者变更的情况
                if issue_number:
                    for child_node in children_nodes:
                        if (child_node.node_type == NodeType.ISSUE and
                            child_node.category_in_type in ['git_issue', 'git_pull_request'] and
                            'github.com' in child_node.url):
                            try:
                                child_repo = child_node.url.split('github.com/')[1].split('/issues')[0].split('/pull')[0]
                                if '/' in child_repo:
                                    child_owner = child_repo.split('/')[0]
                                    child_repo_name = child_repo.split('/')[-1]
                                    
                                    # 相同的仓库名但不同的所有者，很可能是仓库转移了所有权
                                    if child_owner != owner and child_repo_name == repo:
                                        parent_repos.append(child_repo)
                            except:
                                continue
            
            # 1.1 首先处理PR中的commits，给予最高优先级
            pr_commits = []
            for child_node in children_nodes:
                if (child_node.node_type == NodeType.PATCH and 
                    'github.com' in child_node.url and 
                    '/commit/' in child_node.url):
                    
                    # 提取commit SHA
                    commit_sha = None
                    try:
                        sha_match = re.search(r'/commit/([0-9a-f]{7,40})', child_node.url)
                        if sha_match:
                            commit_sha = sha_match.group(1)
                    except:
                        pass
                    
                    # 提取子节点仓库
                    child_repo = None
                    try:
                        repo_part = child_node.url.split('github.com/')[1]
                        child_repo = repo_part.split('/commit/')[0]
                    except:
                        pass
                    
                    # 如果是PR中的commit，并且属于相同仓库，优先保留
                    is_pr_commit = '/pull/' in child_node.url and '/commit/' in child_node.url
                    is_same_repo = parent_repos and child_repo and child_repo in parent_repos
                    
                    if is_pr_commit and is_same_repo:
                        # 避免添加相同SHA值的不同URL格式
                        if commit_sha and commit_sha not in patch_sha_values:
                            pr_commits.append(child_node)
                            patch_sha_values.add(commit_sha)
            
            # 1.2 处理其他节点，但优先保留PR中的commits
            if pr_commits:
                # 如果找到PR中的commits，最多添加5个
                max_pr_commits = min(5, len(pr_commits))
                for i in range(max_pr_commits):
                    node = pr_commits[i]
                    if node.url not in selected_urls:
                        selected_nodes.append(node)
                        selected_urls.add(node.url)
                        selected_ids.add(id(node))
                        node_type_counts['patch'] += 1
            
            # 1.3 对GitHub相关节点做过滤
            for child_node in children_nodes:
                # 如果节点已添加，跳过
                if id(child_node) in selected_ids:
                    continue
                
                # 默认保留节点
                should_select = True
                
                if 'github.com' in child_node.url:
                    # 提取子节点仓库
                    child_repo = None
                    try:
                        repo_part = child_node.url.split('github.com/')[1]
                        # 根据URL类型确定仓库部分
                        if '/issues/' in repo_part:
                            child_repo = repo_part.split('/issues')[0]
                        elif '/pull/' in repo_part: 
                            child_repo = repo_part.split('/pull')[0]
                        elif '/commit/' in repo_part:
                            child_repo = repo_part.split('/commit')[0]
                        else:
                            # 可能是仓库URL或其他GitHub URL
                            parts = repo_part.split('/')
                            if len(parts) >= 2:
                                child_repo = parts[0] + '/' + parts[1]
                    except:
                        pass
                    
                    # 如果子节点不属于相同仓库，不选择
                    if parent_repos and child_repo and child_repo not in parent_repos:
                        should_select = False
                
                # 如果是补丁节点，检查是否已存在相同SHA
                if should_select and child_node.node_type == NodeType.PATCH:
                    # 提取commit SHA
                    commit_sha = None
                    try:
                        sha_match = re.search(r'/commit/([0-9a-f]{7,40})', child_node.url)
                        if sha_match:
                            commit_sha = sha_match.group(1)
                            # 如果SHA已存在，不选择
                            if commit_sha in patch_sha_values:
                                should_select = False
                            else:
                                patch_sha_values.add(commit_sha)
                    except:
                        pass
                
                # 限制节点类型数量
                if should_select:
                    if child_node.node_type == NodeType.PATCH:
                        # 如果已有太多补丁节点，不再添加
                        if node_type_counts['patch'] >= 10:
                            should_select = False
                        else:
                            node_type_counts['patch'] += 1
                    elif child_node.node_type == NodeType.ISSUE:
                        # 如果已有太多issue节点，不再添加
                        if node_type_counts['issue'] >= 5:
                            should_select = False
                        else:
                            node_type_counts['issue'] += 1
                    else:
                        # 其他节点类型限制更严格
                        if node_type_counts['other'] >= 3:
                            should_select = False
                        else:
                            node_type_counts['other'] += 1
                
                # 去重并添加符合条件的节点
                if should_select and child_node.url not in selected_urls:
                    selected_nodes.append(child_node)
                    selected_urls.add(child_node.url)
                    selected_ids.add(id(child_node))
                    
            return selected_nodes  # 直接返回GitHub特殊处理的结果
        
        # 2. JIRA issues的特殊处理
        elif (parent_node.node_type == NodeType.ISSUE and 
             parent_node.category_in_type == 'jira_issue'):
            
            # 提取JIRA项目标识
            jira_project = None
            try:
                match = re.search(r'([A-Z]+-)[0-9]+', parent_node.url)
                if match:
                    jira_project = match.group(1)  # 如"LOG4J-"
            except:
                pass
                
            for child_node in children_nodes:
                # 默认保留节点
                should_select = True
                
                # 对于同一JIRA项目下的issue，特别关注
                if (child_node.node_type == NodeType.ISSUE and 
                    child_node.category_in_type == 'jira_issue' and
                    jira_project):
                    try:
                        # 优先选择相同项目的issue
                        if jira_project not in child_node.url:
                            # 非同一项目的JIRA issue降低优先级
                            if len(selected_nodes) > 5 or node_type_counts['issue'] >= 3:
                                should_select = False
                    except:
                        pass
                
                # 对于补丁节点，检查是否与JIRA相关联
                elif child_node.node_type == NodeType.PATCH:
                    # 如果已有很多补丁节点，则不添加
                    if node_type_counts['patch'] >= 5:
                        should_select = False
                    else:
                        node_type_counts['patch'] += 1
                        
                # 对于其他类型节点，更严格地过滤
                else:
                    # 限制其他节点类型数量
                    if node_type_counts['other'] >= 3:
                        should_select = False
                    else:
                        node_type_counts['other'] += 1
                
                # 去重并添加符合条件的节点
                if should_select and child_node.url not in selected_urls:
                    selected_nodes.append(child_node)
                    selected_urls.add(child_node.url)
                    
                    if child_node.node_type == NodeType.ISSUE:
                        node_type_counts['issue'] += 1
                    elif child_node.node_type == NodeType.PATCH:
                        node_type_counts['patch'] += 1
                    else:
                        node_type_counts['other'] += 1
                    
            return selected_nodes  # 直接返回JIRA特殊处理的结果
        
        # 3. Bugzilla和其他issue tracker的处理
        elif (parent_node.node_type == NodeType.ISSUE and 
             parent_node.category_in_type in ['bugzilla_issue', 'bug_tracker', 'other_issue_tracker']):
            
            # 提取域名，用于匹配同一bug跟踪系统
            parent_domain = None
            try:
                parent_domain = urlparse(parent_node.url).netloc
            except:
                pass
            
            for child_node in children_nodes:
                # 默认保留节点
                should_select = True
                
                # 对于非同域名的节点，检查是否相关
                if parent_domain:
                    try:
                        child_domain = urlparse(child_node.url).netloc
                        if child_domain != parent_domain:
                            # 对于不同域名的节点，仅保留补丁类型的节点
                            if child_node.node_type != NodeType.PATCH:
                                # 不是补丁的外部域名节点减低优先级
                                if len(selected_nodes) > 3 or node_type_counts['other'] >= 2:
                                    should_select = False
                            else:
                                # 即使是补丁，如果已有足够数量，也不选择
                                if node_type_counts['patch'] >= 5:
                                    should_select = False
                    except:
                        pass
                
                # 限制节点类型数量
                if should_select:
                    if child_node.node_type == NodeType.PATCH:
                        if node_type_counts['patch'] >= 5:
                            should_select = False
                        else:
                            node_type_counts['patch'] += 1
                    elif child_node.node_type == NodeType.ISSUE:
                        if node_type_counts['issue'] >= 3:
                            should_select = False
                        else:
                            node_type_counts['issue'] += 1
                    else:
                        if node_type_counts['other'] >= 2:
                            should_select = False
                        else:
                            node_type_counts['other'] += 1
                
                # 去重并添加符合条件的节点
                if should_select and child_node.url not in selected_urls:
                    selected_nodes.append(child_node)
                    selected_urls.add(child_node.url)
                    
            return selected_nodes  # 直接返回bug tracker特殊处理的结果
        
        # 4. 补丁节点的子节点处理
        elif parent_node.node_type == NodeType.PATCH:
            # 补丁节点的子节点中优先选择issue节点
            issue_nodes = []
            patch_nodes = []
            other_nodes = []
            
            for child_node in children_nodes:
                if child_node.url in selected_urls:
                    continue
                    
                if child_node.node_type == NodeType.ISSUE:
                    issue_nodes.append(child_node)
                elif child_node.node_type == NodeType.PATCH:
                    # 对于补丁节点的补丁子节点，检查是否是同一仓库
                    if ('github.com' in parent_node.url and 'github.com' in child_node.url):
                        try:
                            parent_repo = parent_node.url.split('github.com/')[1].split('/commit')[0]
                            child_repo = child_node.url.split('github.com/')[1].split('/commit')[0]
                            if parent_repo != child_repo:
                                continue  # 不同仓库的补丁不添加
                        except:
                            pass
                    patch_nodes.append(child_node)
                else:
                    other_nodes.append(child_node)
            
            # 按优先级和数量限制添加节点
            max_issue_nodes = min(5, len(issue_nodes))
            max_patch_nodes = min(3, len(patch_nodes))
            max_other_nodes = min(2, len(other_nodes))
            
            selected_nodes = issue_nodes[:max_issue_nodes] + patch_nodes[:max_patch_nodes] + other_nodes[:max_other_nodes]
            
            # 更新选择的URL集合
            selected_urls = set(node.url for node in selected_nodes)
            
            return selected_nodes
        
        # 5. 其他类型节点的处理（普通节点等）
        else:
            # 按节点类型进行分类
            issue_nodes = []
            patch_nodes = []
            other_nodes = []
            
            for child_node in children_nodes:
                if child_node.url in selected_urls:
                    continue
                    
                if child_node.node_type == NodeType.ISSUE:
                    issue_nodes.append(child_node)
                elif child_node.node_type == NodeType.PATCH:
                    patch_nodes.append(child_node)
                else:
                    other_nodes.append(child_node)
            
            # 根据节点类型限制数量
            max_issue_nodes = min(3, len(issue_nodes))
            max_patch_nodes = min(5, len(patch_nodes))
            max_other_nodes = min(2, len(other_nodes))
            
            selected_nodes = patch_nodes[:max_patch_nodes] + issue_nodes[:max_issue_nodes] + other_nodes[:max_other_nodes]
            
            return selected_nodes 
