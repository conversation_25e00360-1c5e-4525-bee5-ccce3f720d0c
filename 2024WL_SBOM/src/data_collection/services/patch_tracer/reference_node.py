#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
参考网络节点模块

定义了引用网络中使用的节点类型和节点基类。
提供URL标准化处理和节点类型识别功能。
"""

import re
import enum
import hashlib
import logging
import urllib.parse
from typing import Dict, Any, Optional, Tuple
from enum import Enum, auto

logger = logging.getLogger(__name__)

class NodeType(Enum):
    """节点类型枚举"""
    ROOT = auto()      # 根节点(CVE)
    SOURCE = auto()    # 来源节点
    PATCH = auto()     # 补丁节点
    ISSUE = auto()     # 问题节点
    COMMON = auto()    # 普通节点
    REPO = auto()      # 仓库节点
    
    # 细分的节点类型
    GIT_COMMIT = auto() # Git提交
    SVN_COMMIT = auto() # SVN提交
    GIT_ISSUE = auto()  # Git问题
    GIT_PR = auto()     # Git Pull Request
    JIRA = auto()       # JIRA问题
    BUGZILLA = auto()   # Bugzilla问题
    
    
class ReferenceNode:
    """参考网络节点"""
    
    def __init__(self, url: str, title: str = "", node_type: NodeType = NodeType.COMMON, 
                 source: str = "UNKNOWN", depth: int = 1, category_in_type: str = None):
        """
        初始化引用节点
        
        Args:
            url: 节点URL
            title: 节点标题
            node_type: 节点类型
            source: 来源
            depth: 深度
            category_in_type: 节点子类型
        """
        self.node_id = None  # 由ReferenceNetwork分配
        self.url = url
        self.title = title
        self.node_type = node_type
        self.source = source
        self.depth = depth
        self.category_in_type = category_in_type  # 存储节点子类型
        self.importance_score = 0.0  # 重要性评分
        
    def __eq__(self, other):
        if not isinstance(other, ReferenceNode):
            return False
        return self.url == other.url
    
    def __hash__(self):
        return hash(self.url)
    
    @staticmethod
    def identify_node_type(url: str, source: str = None) -> Tuple[Optional[NodeType], Optional[str]]:
        """
        根据URL识别节点类型
        这是一个简化版本，更完整的实现在ReferenceNetwork类中
        
        Args:
            url: 节点URL
            source: 可选来源参数(用于向后兼容，但不再使用)
            
        Returns:
            Tuple[Optional[NodeType], Optional[str]]: 节点类型和子类别
        """
        from .reference_network import ReferenceNetwork
        return ReferenceNetwork.identify_node_type(url)
    
    @staticmethod
    def is_patch_url(url: str) -> bool:
        """
        判断URL是否为补丁URL
        
        Args:
            url: URL
            
        Returns:
            bool: 是否为补丁URL
        """
        # 标准化URL
        url = urllib.parse.unquote(url)
        
        # 过滤不可能是补丁的URL
        unwanted_patterns = ['attachment.cgi?id=', 'user-images', 'gist.github']
        if any(pattern in url for pattern in unwanted_patterns):
            return False
        
        # GitHub commit URL
        if '/github.com/' in url and '/commit/' in url:
            commit_id = ReferenceNode.extract_commit_id(url)
            if commit_id:
                return True
                
        # GitHub pull request commits URL
        if '/github.com/' in url and '/pull/' in url and '/commits/' in url:
            commit_id = ReferenceNode.extract_commit_id(url)
            if commit_id:
                return True
        
        # Git commit URL (非GitHub)
        if ('git' in url.lower() and ';a=commit' in url) or ('git' in url.lower() and 'st=commit' in url):
            commit_id = ReferenceNode.extract_commit_id(url)
            if commit_id:
                return True
        
        # SVN URL
        svn_patterns = ['/svn.' in url and 'rev=' in url, 
                        '/svn.' in url and '/r' in url.replace('repos', '_'),
                        '/svn.' in url and 'revision' in url]
        if any(svn_patterns):
            return True
            
        # Diff/Patch文件
        if url.endswith('.patch') or url.endswith('.diff'):
            return True
            
        return False
    
    @staticmethod
    def get_patch_category(url: str) -> str:
        """
        获取补丁URL的分类
        
        Args:
            url: URL
            
        Returns:
            str: 补丁分类
        """
        # GitHub commit URL
        if '/github.com/' in url and '/commit/' in url:
            return 'git_commit'
                
        # GitHub pull request commits URL
        if '/github.com/' in url and '/pull/' in url and '/commits/' in url:
            return 'git_commit'
        
        # Git commit URL (非GitHub)
        if ('git' in url.lower() and ';a=commit' in url) or ('git' in url.lower() and 'st=commit' in url):
            return 'git_commit'
        
        # SVN URL
        svn_patterns = ['/svn.' in url and 'rev=' in url, 
                        '/svn.' in url and '/r' in url.replace('repos', '_'),
                        '/svn.' in url and 'revision' in url]
        if any(svn_patterns):
            return 'svn'
            
        # Diff/Patch文件
        if url.endswith('.patch') or url.endswith('.diff'):
            return 'diff_file'
            
        return 'other_patch'
    
    @staticmethod
    def is_issue_url(url: str) -> bool:
        """
        判断URL是否为问题URL
        
        Args:
            url: URL
            
        Returns:
            bool: 是否为问题URL
        """
        # 标准化URL
        url = urllib.parse.unquote(url)
        
        # GitHub issue URL
        if '/github.com/' in url and '/issues/' in url:
            issue_id = url.split('/issues/')[-1].split('/')[0].split('#')[0]
            if issue_id.isdigit():
                return True
                
        # GitHub pull request URL
        if '/github.com/' in url and '/pull/' in url:
            pr_id = url.split('/pull/')[-1].split('/')[0].split('#')[0]
            if pr_id.isdigit():
                return True
        
        # JIRA URL
        if ('/jira.' in url or '/jira/' in url) and len(re.findall('[A-Z]+-[0-9]+', url)) > 0:
            return True
        
        # Bugzilla URL
        if ('/bugzilla.' in url or '/bugzilla/' in url) and len(re.findall('[0-9]{2,}', url)) > 0:
            # 排除特定Bugzilla URL
            blacklist = ['bugzilla.redhat.com', 'bugzilla.mozilla.org/show_bug.cgi']
            if not any(site in url for site in blacklist):
                return True
                
        return False
    
    @staticmethod
    def get_issue_category(url: str) -> str:
        """
        获取问题URL的分类
        
        Args:
            url: URL
            
        Returns:
            str: 问题分类
        """
        # GitHub issue URL
        if '/github.com/' in url and '/issues/' in url:
            return 'git_issue'
                
        # GitHub pull request URL
        if '/github.com/' in url and '/pull/' in url:
            return 'git_pr'
        
        # JIRA URL
        if ('/jira.' in url or '/jira/' in url) and len(re.findall('[A-Z]+-[0-9]+', url)) > 0:
            return 'jira'
        
        # Bugzilla URL
        if ('/bugzilla.' in url or '/bugzilla/' in url) and len(re.findall('[0-9]{2,}', url)) > 0:
            return 'bugzilla'
                
        return 'other_issue'
    
    @staticmethod
    def extract_commit_id(url: str) -> str:
        """
        从URL中提取Git提交ID
        
        Args:
            url: URL
            
        Returns:
            str: 提交ID，不存在则返回空字符串
        """
        # 提取提交ID的正则表达式
        commit_pattern = r'(?![0-9]{6,40})(?![a-f]{6,40})[0-9a-f]{6,40}'
        matches = re.findall(commit_pattern, url)
        if matches:
            return matches[0]
        return ""
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换节点为字典表示
        
        Returns:
            Dict[str, Any]: 节点的字典表示
        """
        return {
            'node_id': self.node_id,
            'url': self.url,
            'title': self.title or "",
            'node_type': self.node_type.name if self.node_type else "COMMON",  # 使用类型名称而不是枚举值
            'source': self.source,
            'depth': self.depth,
            'category_in_type': self.category_in_type or "",
            'importance_score': getattr(self, 'importance_score', 0.0)
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.node_type.value}:{self.source}:{self.url}"
    
    def __repr__(self) -> str:
        """调试表示"""
        return f"ReferenceNode(id={self.node_id}, type={self.node_type.value}, url={self.url})"
    
    def __eq__(self, other) -> bool:
        """相等比较"""
        if not isinstance(other, ReferenceNode):
            return False
        return self.node_id == other.node_id
    
    def __hash__(self) -> int:
        """哈希值"""
        return hash(self.node_id) 