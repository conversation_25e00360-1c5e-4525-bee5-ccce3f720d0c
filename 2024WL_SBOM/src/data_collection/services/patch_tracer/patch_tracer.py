from typing import List, Dict, Optional
import requests
from log import logger

class PatchTracer:
    def _verify_patch_accessibility(self, patch_results: List[Dict], analyzer: ReferenceAnalyzer) -> List[Dict]:
        """
        验证补丁URL的可访问性，降低或剔除无法访问的URL
        
        Args:
            patch_results: 原始补丁结果列表
            analyzer: 引用分析器(用于复用其HTTP会话)
            
        Returns:
            List[Dict]: 过滤后的补丁结果列表
        """
        verified_patches = []
        logger.info(f"验证 {len(patch_results)} 个补丁URL的可访问性...")
        
        # 为避免多次验证相同URL，使用集合记录已验证的URL
        verified_urls = {}  # url -> 是否可访问
        
        for patch in patch_results:
            url = patch['url']
            
            # 如果已验证过此URL，直接使用结果
            if url in verified_urls:
                is_accessible = verified_urls[url]
            else:
                # 否则进行验证
                is_accessible = self._is_url_accessible(url, analyzer.session)
                verified_urls[url] = is_accessible
                
            if is_accessible:
                # URL可访问，保留
                verified_patches.append(patch)
            else:
                # URL不可访问，记录日志
                logger.warning(f"补丁URL不可访问，已从结果中剔除: {url}")
        
        logger.info(f"验证完成: {len(verified_patches)}/{len(patch_results)} 个补丁URL可访问")
        return verified_patches
        
    def _is_url_accessible(self, url: str, session: Optional[requests.Session] = None) -> bool:
        """
        检查URL是否可访问
        
        Args:
            url: 要检查的URL
            session: 可选的请求会话，如果提供将复用
            
        Returns:
            bool: URL是否可访问
        """
        if not session:
            session = requests.Session()
            
        try:
            # 使用HEAD请求快速检查URL是否可访问
            response = session.head(
                url,
                timeout=10,
                allow_redirects=True,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            # 检查状态码
            if response.status_code < 400:
                return True
                
            # 如果HEAD请求失败，尝试GET请求
            response = session.get(
                url,
                timeout=10,
                allow_redirects=True,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                stream=True  # 使用流式请求，不下载完整内容
            )
            
            # 只读取一小部分内容即关闭连接
            if response.status_code < 400:
                response.close()
                return True
                
            return False
            
        except (requests.RequestException, Exception) as e:
            logger.debug(f"URL访问检查失败: {url} - {str(e)}")
            return False 