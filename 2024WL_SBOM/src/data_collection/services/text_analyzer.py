#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文本分析与组件关联服务

该服务负责:
1. 文本相似度分析
2. 组件名称标准化
3. 版本匹配算法
4. 从文本中提取组件信息
"""

import re
from typing import List, Tuple, Dict, Any, Optional, Set
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from packaging import version
from packaging.specifiers import SpecifierSet

from ..models.entities import Vulnerability, Package
from ..utils.logger import logger

class TextAnalyzer:
    """文本分析与组件关联服务"""
    
    def __init__(self, min_similarity: float = 0.7):
        """
        初始化文本分析器
        
        Args:
            min_similarity: 最小相似度阈值
        """
        self.min_similarity = min_similarity
        self.vectorizer = TfidfVectorizer(
            stop_words='english',
            max_features=5000,
            ngram_range=(1, 2)
        )
        
        # 常见生态系统名称
        self.ecosystems = {
            'npm': ['npm', 'node', 'javascript', 'js', 'nodejs'],
            'maven': ['maven', 'java', 'apache', 'spring', 'org.apache'],
            'pypi': ['pypi', 'python', 'pip'],
            'rubygems': ['ruby', 'gem', 'rubygems'],
            'nuget': ['nuget', '.net', 'dotnet', 'csharp', 'c#'],
            'composer': ['composer', 'php'],
            'golang': ['go', 'golang'],
            'cargo': ['rust', 'cargo', 'crates.io']
        }
        
        # 组件名称正则表达式
        self.name_patterns = [
            # Maven格式: group:artifact
            r'([a-zA-Z0-9_.-]+):([a-zA-Z0-9_.-]+)',
            # 普通组件名称
            r'([a-zA-Z0-9_.-]+)'
        ]
        
        # 版本范围正则表达式
        self.version_patterns = [
            # 版本范围，如">= 1.0.0, < 2.0.0"
            r'(>=|<=|>|<|=|~|\^)\s*(\d+(?:\.\d+)*(?:-[a-zA-Z0-9_.-]+)?)',
            # 版本区间，如"1.0.0 to 2.0.0"
            r'(\d+(?:\.\d+)*(?:-[a-zA-Z0-9_.-]+)?)\s+(?:to|through)\s+(\d+(?:\.\d+)*(?:-[a-zA-Z0-9_.-]+)?)',
            # 普通版本号
            r'(\d+(?:\.\d+)*(?:-[a-zA-Z0-9_.-]+)?)'
        ]
    
    def compute_text_similarity(self, texts: List[str]) -> np.ndarray:
        """
        计算文本之间的相似度
        
        Args:
            texts: 文本列表
            
        Returns:
            相似度矩阵
        """
        if not texts:
            return np.array([])
        
        try:
            # 将文本转换为TF-IDF特征向量
            tfidf_matrix = self.vectorizer.fit_transform(texts)
            
            # 计算余弦相似度
            similarity_matrix = cosine_similarity(tfidf_matrix, tfidf_matrix)
            
            return similarity_matrix
        except Exception as e:
            logger.error(f"计算文本相似度失败: {str(e)}")
            return np.array([])
    
    def find_similar_texts(self, 
                         texts: List[str], 
                         threshold: Optional[float] = None) -> List[Tuple[int, int, float]]:
        """
        查找相似的文本对
        
        Args:
            texts: 文本列表
            threshold: 相似度阈值，默认使用self.min_similarity
            
        Returns:
            相似文本对列表，每个元素为(i, j, score)
        """
        if threshold is None:
            threshold = self.min_similarity
        
        similarity_matrix = self.compute_text_similarity(texts)
        if similarity_matrix.size == 0:
            return []
        
        # 查找相似文本对
        similar_pairs = []
        for i in range(len(texts)):
            for j in range(i + 1, len(texts)):  # 只考虑上三角矩阵
                if similarity_matrix[i, j] >= threshold:
                    similar_pairs.append((i, j, similarity_matrix[i, j]))
        
        # 按相似度降序排序
        return sorted(similar_pairs, key=lambda x: x[2], reverse=True)
    
    def normalize_component_name(self, name: str) -> str:
        """
        标准化组件名称
        
        Args:
            name: 组件名称
            
        Returns:
            标准化后的名称
        """
        if not name:
            return ""
        
        # 转换为小写
        name = name.lower()
        
        # 移除生态系统前缀
        for ecosystem, prefixes in self.ecosystems.items():
            for prefix in prefixes:
                if name.startswith(f"{prefix}-"):
                    name = name[len(prefix) + 1:]
                    break
                    
        # 处理Maven格式 (group:artifact)
        if ":" in name:
            parts = name.split(":")
            if len(parts) >= 2:
                # 只保留组件名称部分
                name = parts[1]
        
        # 移除特殊字符和多余空格
        name = re.sub(r'[^a-z0-9_.-]', ' ', name)
        name = re.sub(r'\s+', '-', name.strip())
        
        return name
    
    def group_similar_components(self, components: List[str]) -> List[List[str]]:
        """
        将相似的组件名称分组
        
        Args:
            components: 组件名称列表
            
        Returns:
            组件组列表
        """
        if not components:
            return []
        
        # 标准化组件名称
        normalized_components = [self.normalize_component_name(c) for c in components]
        
        # 使用并查集进行分组
        parent = list(range(len(components)))
        
        def find(x):
            if parent[x] != x:
                parent[x] = find(parent[x])
            return parent[x]
        
        def union(x, y):
            parent[find(x)] = find(y)
        
        # 检查每对组件名称
        for i in range(len(normalized_components)):
            for j in range(i + 1, len(normalized_components)):
                # 如果标准化后的名称相同，或者一个是另一个的子字符串
                if (normalized_components[i] == normalized_components[j] or
                    normalized_components[i] in normalized_components[j] or
                    normalized_components[j] in normalized_components[i]):
                    union(i, j)
        
        # 收集分组结果
        groups_dict = {}
        for i in range(len(components)):
            root = find(i)
            if root not in groups_dict:
                groups_dict[root] = []
            groups_dict[root].append(components[i])
        
        return list(groups_dict.values())
    
    def is_version_affected(self, version_str: str, version_range: str) -> bool:
        """
        检查版本是否受影响
        
        Args:
            version_str: 版本字符串
            version_range: 版本范围字符串
            
        Returns:
            是否受影响
        """
        try:
            # 处理特殊格式
            version_range = version_range.strip()
            
            # 处理版本区间，如"1.0.0 to 2.0.0"
            to_match = re.search(r'(\d+(?:\.\d+)*)\s+to\s+(\d+(?:\.\d+)*)', version_range)
            if to_match:
                start_ver = version.parse(to_match.group(1))
                end_ver = version.parse(to_match.group(2))
                ver = version.parse(version_str)
                return start_ver <= ver <= end_ver
            
            # 处理"prior to X"格式
            prior_match = re.search(r'prior\s+to\s+(\d+(?:\.\d+)*)', version_range, re.IGNORECASE)
            if prior_match:
                limit_ver = version.parse(prior_match.group(1))
                ver = version.parse(version_str)
                return ver < limit_ver
            
            # 处理"before X"格式
            before_match = re.search(r'before\s+(\d+(?:\.\d+)*)', version_range, re.IGNORECASE)
            if before_match:
                limit_ver = version.parse(before_match.group(1))
                ver = version.parse(version_str)
                return ver < limit_ver
            
            # 处理"X and earlier"格式
            earlier_match = re.search(r'(\d+(?:\.\d+)*)\s+and\s+earlier', version_range, re.IGNORECASE)
            if earlier_match:
                limit_ver = version.parse(earlier_match.group(1))
                ver = version.parse(version_str)
                return ver <= limit_ver
            
            # 直接使用SpecifierSet进行版本比较
            spec = SpecifierSet(version_range)
            return spec.contains(version_str)
            
        except Exception as e:
            logger.error(f"版本比较失败: {str(e)}")
            # 无法解析版本范围，默认为受影响
            return True
    
    def extract_components_from_text(self, text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取组件信息
        
        Args:
            text: 输入文本
            
        Returns:
            组件信息列表，每个元素为包含name, version, ecosystem等信息的字典
        """
        components = []
        
        # 寻找组件名称
        for ecosystem, keywords in self.ecosystems.items():
            for keyword in keywords:
                if keyword.lower() in text.lower():
                    # 可能是该生态系统的组件
                    for pattern in self.name_patterns:
                        for match in re.finditer(pattern, text):
                            component_name = match.group(0)
                            
                            # 查找相关版本信息
                            version_info = {}
                            for v_pattern in self.version_patterns:
                                v_matches = re.finditer(v_pattern, text)
                                for v_match in v_matches:
                                    version_info["version_range"] = v_match.group(0)
                                    break
                                    
                            components.append({
                                "name": component_name,
                                "normalized_name": self.normalize_component_name(component_name),
                                "ecosystem": ecosystem,
                                **version_info
                            })
        
        # 如果没有找到特定生态系统，尝试查找通用组件名称
        if not components:
            # 查找可能的组件名称
            for pattern in self.name_patterns:
                for match in re.finditer(pattern, text):
                    component_name = match.group(0)
                    
                    # 查找相关版本信息
                    version_info = {}
                    for v_pattern in self.version_patterns:
                        v_matches = re.finditer(v_pattern, text)
                        for v_match in v_matches:
                            version_info["version_range"] = v_match.group(0)
                            break
                            
                    components.append({
                        "name": component_name,
                        "normalized_name": self.normalize_component_name(component_name),
                        **version_info
                    })
        
        # 去重
        unique_components = []
        seen_names = set()
        
        for component in components:
            if component["normalized_name"] not in seen_names:
                seen_names.add(component["normalized_name"])
                unique_components.append(component)
        
        return unique_components 