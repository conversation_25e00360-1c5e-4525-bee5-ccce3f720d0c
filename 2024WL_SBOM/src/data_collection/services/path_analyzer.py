#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
漏洞传播路径分析服务模块

提供漏洞传播路径分析功能:
1. 漏洞影响范围分析
2. 组件依赖链分析
3. 传播路径追踪
4. 风险评估
"""

from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from dataclasses import dataclass
from collections import defaultdict

# 修改导入路径，确保与主程序兼容
from ..models.graph import NodeType, RelationType
from .graph_store import GraphStore
from ..utils.logger import logger

@dataclass
class PathNode:
    """路径节点"""
    id: str
    type: str
    properties: Dict[str, Any]

@dataclass
class PathRelation:
    """路径关系"""
    source_id: str
    target_id: str
    type: str
    properties: Dict[str, Any]

@dataclass
class Path:
    """传播路径"""
    nodes: List[PathNode]
    relations: List[PathRelation]
    risk_score: float = 0.0

class PathAnalyzer:
    """漏洞传播路径分析服务"""
    
    def __init__(self,
                graph_store: GraphStore,
                max_depth: int = 5):
        """
        初始化路径分析服务
        
        Args:
            graph_store: 图数据库服务实例
            max_depth: 最大分析深度
        """
        self.graph_store = graph_store
        self.max_depth = max_depth
    
    def analyze_vulnerability_impact(self,
                                 vuln_id: str) -> Dict[str, Any]:
        """
        分析漏洞影响范围
        
        Args:
            vuln_id: 漏洞ID
            
        Returns:
            影响范围分析结果
        """
        # 获取直接受影响的组件
        direct_relations = self.graph_store.get_relations(
            source_id=vuln_id,
            relation_type=RelationType.AFFECTS
        )
        
        direct_components = [
            relation["target"]["id"]
            for relation in direct_relations
        ]
        
        # 获取间接受影响的组件
        indirect_components = set()
        for component_id in direct_components:
            # 查找依赖该组件的其他组件
            dependent_relations = self.graph_store.get_relations(
                target_id=component_id,
                relation_type=RelationType.DEPENDS_ON
            )
            
            for relation in dependent_relations:
                indirect_components.add(relation["source"]["id"])
        
        # 统计受影响版本
        affected_versions = defaultdict(list)
        for relation in direct_relations:
            component_id = relation["target"]["id"]
            if "affected_versions" in relation["relation"]:
                affected_versions[component_id].extend(
                    relation["relation"]["affected_versions"]
                )
        
        return {
            "vulnerability_id": vuln_id,
            "direct_components": direct_components,
            "indirect_components": list(indirect_components),
            "total_affected": len(direct_components) + len(indirect_components),
            "affected_versions": dict(affected_versions)
        }
    
    def analyze_component_dependencies(self,
                                   component_id: str,
                                   direction: str = "outgoing") -> Dict[str, Any]:
        """
        分析组件依赖链
        
        Args:
            component_id: 组件ID
            direction: 依赖方向("outgoing"=该组件依赖的其他组件, "incoming"=依赖该组件的其他组件)
            
        Returns:
            依赖链分析结果
        """
        dependencies = defaultdict(list)
        visited = set()
        
        def traverse_dependencies(current_id: str, depth: int = 0):
            if depth >= self.max_depth or current_id in visited:
                return
                
            visited.add(current_id)
            
            # 获取依赖关系
            if direction == "outgoing":
                relations = self.graph_store.get_relations(
                    source_id=current_id,
                    relation_type=RelationType.DEPENDS_ON
                )
                next_ids = [r["target"]["id"] for r in relations]
            else:
                relations = self.graph_store.get_relations(
                    target_id=current_id,
                    relation_type=RelationType.DEPENDS_ON
                )
                next_ids = [r["source"]["id"] for r in relations]
            
            # 记录当前深度的依赖
            dependencies[depth].extend(next_ids)
            
            # 递归遍历
            for next_id in next_ids:
                traverse_dependencies(next_id, depth + 1)
        
        # 开始遍历
        traverse_dependencies(component_id)
        
        # 统计每个深度的漏洞数量
        vulnerabilities_by_depth = defaultdict(int)
        for depth, components in dependencies.items():
            for component in components:
                # 查找影响该组件的漏洞
                vuln_relations = self.graph_store.get_relations(
                    target_id=component,
                    relation_type=RelationType.AFFECTS
                )
                vulnerabilities_by_depth[depth] += len(vuln_relations)
        
        return {
            "component_id": component_id,
            "direction": direction,
            "dependencies_by_depth": {
                depth: list(set(deps))  # 去重
                for depth, deps in dependencies.items()
            },
            "total_dependencies": sum(len(deps) for deps in dependencies.values()),
            "vulnerabilities_by_depth": dict(vulnerabilities_by_depth),
            "total_vulnerabilities": sum(vulnerabilities_by_depth.values())
        }
    
    def find_vulnerability_paths(self,
                              start_id: str,
                              end_id: str) -> List[Path]:
        """
        查找漏洞传播路径
        
        Args:
            start_id: 起始节点ID(漏洞或组件)
            end_id: 目标节点ID(漏洞或组件)
            
        Returns:
            传播路径列表
        """
        # 获取所有可能的路径
        raw_paths = self.graph_store.find_paths(
            start_id=start_id,
            end_id=end_id,
            max_depth=self.max_depth
        )
        
        paths = []
        for raw_path in raw_paths:
            nodes = []
            relations = []
            
            # 解析路径中的节点和关系
            for item in raw_path:
                if item["type"] == "node":
                    nodes.append(PathNode(
                        id=item["data"]["id"],
                        type=item["data"].get("type", "unknown"),
                        properties=item["data"]
                    ))
                else:  # relation
                    relations.append(PathRelation(
                        source_id=item["data"]["source_id"],
                        target_id=item["data"]["target_id"],
                        type=item["data"].get("type", "unknown"),
                        properties=item["data"]
                    ))
            
            # 计算路径风险得分
            risk_score = self._calculate_path_risk(nodes, relations)
            
            paths.append(Path(
                nodes=nodes,
                relations=relations,
                risk_score=risk_score
            ))
        
        # 按风险得分排序
        paths.sort(key=lambda p: p.risk_score, reverse=True)
        return paths
    
    def _calculate_path_risk(self,
                          nodes: List[PathNode],
                          relations: List[PathRelation]) -> float:
        """
        计算路径风险得分
        
        Args:
            nodes: 路径节点列表
            relations: 路径关系列表
            
        Returns:
            风险得分
        """
        risk_score = 0.0
        
        # 基于CVSS评分
        for node in nodes:
            if node.type == NodeType.VULNERABILITY.value:
                cvss_v3 = node.properties.get("cvss_v3_score")
                if cvss_v3 is not None:
                    risk_score += cvss_v3
                else:
                    cvss_v2 = node.properties.get("cvss_v2_score")
                    if cvss_v2 is not None:
                        risk_score += cvss_v2
        
        # 考虑路径长度的惩罚因子
        path_length = len(relations)
        if path_length > 0:
            risk_score *= (1.0 / path_length)
        
        return risk_score
    
    def assess_component_risk(self, component_id: str) -> Dict[str, Any]:
        """
        评估组件风险
        
        Args:
            component_id: 组件ID
            
        Returns:
            风险评估结果
        """
        # 获取直接影响该组件的漏洞
        vuln_relations = self.graph_store.get_relations(
            target_id=component_id,
            relation_type=RelationType.AFFECTS
        )
        
        vulnerabilities = []
        total_cvss_score = 0.0
        severity_counts = defaultdict(int)
        
        for relation in vuln_relations:
            vuln = relation["source"]
            vulnerabilities.append({
                "id": vuln["id"],
                "severity": vuln.get("severity"),
                "cvss_v3_score": vuln.get("cvss_v3_score"),
                "affected_versions": relation["relation"].get("affected_versions", []),
                "fixed_versions": relation["relation"].get("fixed_versions", [])
            })
            
            # 累计CVSS评分
            cvss_score = vuln.get("cvss_v3_score")
            if cvss_score is not None:
                total_cvss_score += cvss_score
            
            # 统计严重程度
            severity = vuln.get("severity")
            if severity:
                severity_counts[severity] += 1
        
        # 分析依赖链
        dependencies = self.analyze_component_dependencies(
            component_id,
            direction="outgoing"
        )
        
        # 计算风险等级
        vuln_count = len(vulnerabilities)
        if vuln_count > 0:
            avg_cvss_score = total_cvss_score / vuln_count
        else:
            avg_cvss_score = 0.0
        
        if avg_cvss_score >= 7.0:
            risk_level = "high"
        elif avg_cvss_score >= 4.0:
            risk_level = "medium"
        else:
            risk_level = "low"
        
        return {
            "component_id": component_id,
            "risk_level": risk_level,
            "risk_score": round(avg_cvss_score, 2),
            "vulnerability_count": vuln_count,
            "severity_distribution": dict(severity_counts),
            "vulnerabilities": vulnerabilities,
            "dependencies": dependencies,
            "recommendation": self._generate_recommendation(
                risk_level,
                vuln_count,
                dependencies["total_dependencies"]
            )
        }
    
    def _generate_recommendation(self,
                              risk_level: str,
                              vuln_count: int,
                              dep_count: int) -> str:
        """
        生成风险建议
        
        Args:
            risk_level: 风险等级
            vuln_count: 漏洞数量
            dep_count: 依赖数量
            
        Returns:
            建议内容
        """
        if risk_level == "high":
            return (
                f"建议立即更新该组件。当前发现{vuln_count}个漏洞，"
                f"且该组件有{dep_count}个依赖项。建议进行：\n"
                "1. 升级到最新的稳定版本\n"
                "2. 检查并更新受影响的依赖项\n"
                "3. 进行全面的安全测试"
            )
        elif risk_level == "medium":
            return (
                f"建议在下一个维护周期更新该组件。当前发现{vuln_count}个漏洞，"
                f"且该组件有{dep_count}个依赖项。建议进行：\n"
                "1. 规划版本升级时间表\n"
                "2. 评估更新对系统的影响\n"
                "3. 实施必要的安全补丁"
            )
        else:
            return (
                f"当前风险较低。发现{vuln_count}个低危漏洞，"
                f"该组件有{dep_count}个依赖项。建议：\n"
                "1. 保持定期更新\n"
                "2. 监控新的安全公告\n"
                "3. 在常规维护中进行升级"
            ) 