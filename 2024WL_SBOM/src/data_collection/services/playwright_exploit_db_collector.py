#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""使用 Playwright 实现的 ExploitDB 收集器，用于从 ExploitDB 获取 POC 信息 (增强反爬绕过)"""

import os
import re
import json
import time
import random
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Optional

from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError, Error as PlaywrightError
# 导入 stealth
from playwright_stealth import stealth_async

# 设置日志
logger = logging.getLogger("playwright_exploit_db_collector")

class PlaywrightExploitDBCollector:
    """使用 Playwright 实现的 ExploitDB 收集器 (增强反爬绕过)"""

    BASE_URL = "https://www.exploit-db.com"
    SEARCH_URL = f"{BASE_URL}/search"

    def __init__(self, proxies: Optional[Dict] = None, timeout: int = 60000, # Playwright 超时以毫秒为单位
                 mock_mode: bool = False, verbose: bool = False):
        """
        初始化 Playwright ExploitDB 收集器

        参数:
            proxies: 代理设置，格式为{"server": "http://host:port"}
            timeout: Playwright 操作超时时间（毫秒）
            mock_mode: 是否使用模拟数据（测试用）
            verbose: 是否输出详细日志
        """
        self.proxies = proxies if proxies else {}
        self.playwright_timeout = timeout
        self.mock_mode = mock_mode
        self.verbose = verbose

        if verbose:
            logger.setLevel(logging.DEBUG)
        else:
            logger.setLevel(logging.INFO)

        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
             "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/114.0", # Firefox User Agent
        ]

    async def _launch_browser(self, playwright):
        """启动 Playwright 浏览器实例"""
        logger.info("启动 Playwright Chromium 浏览器 (带反爬配置)...")
        launch_options = {
            "headless": True,
            "args": [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage", # 在 Docker 中重要
                "--disable-accelerated-2d-canvas",
                "--no-first-run",
                "--no-zygote",
                "--disable-gpu",
                "--disable-infobars", # 禁用信息栏
                "--disable-blink-features=AutomationControlled" # 尝试移除自动化标志
            ]
        }
        if self.proxies and 'server' in self.proxies:
            launch_options["proxy"] = self.proxies
            logger.info(f"使用代理: {self.proxies['server']}")
        try:
            browser = await playwright.chromium.launch(**launch_options)
            logger.info("浏览器启动成功")
            return browser
        except PlaywrightError as e:
            logger.error(f"启动浏览器失败: {e}")
            raise

    async def _search_cve(self, page, cve_id: str) -> List[int]:
        """
        使用 Playwright 在 ExploitDB 上搜索与 CVE 相关的 EDB ID
        """
        logger.info(f"使用 Playwright 搜索与 {cve_id} 相关的 EDB ID")
        edb_ids = []
        search_url = f"{self.SEARCH_URL}?cve={cve_id}"

        try:
            logger.info(f"导航到搜索 URL: {search_url}")
            # --- 修改: 使用 'networkidle' 等待更充分加载 ---
            await page.goto(search_url, timeout=self.playwright_timeout, wait_until='networkidle')
            logger.debug(f"页面 '{page.url}' 加载完成 (networkidle)")

            # 短暂等待，让JS有更多时间执行
            await asyncio.sleep(random.uniform(2, 4))

            # 等待结果表格出现，或提示没有结果，增加超时时间
            try:
                await page.wait_for_selector('table tbody tr, div.alert-warning', timeout=self.playwright_timeout * 0.8)
                logger.debug("搜索结果区域已加载")
            except PlaywrightTimeoutError:
                logger.warning("等待搜索结果超时或未找到预期元素")
                pass # 即使超时，也尝试解析

            if self.verbose:
                await self._save_page_content(page, f"playwright_search_{cve_id}")

            no_results_element = await page.query_selector('div.alert-warning:has-text("No results")')
            if no_results_element:
                logger.warning(f"搜索 {cve_id} 没有找到结果 (ExploitDB 页面提示)")
                return []

            exploit_links = await page.query_selector_all('table tbody tr td a[href*="/exploits/"]')
            if not exploit_links:
                 exploit_links = await page.query_selector_all('a[href*="/exploits/"]')

            for link in exploit_links:
                href = await link.get_attribute('href')
                if href and '/exploits/' in href:
                    try:
                        edb_id_str = href.split('/exploits/')[-1].split('/')[0]
                        if edb_id_str.isdigit():
                            edb_id = int(edb_id_str)
                            if edb_id not in edb_ids:
                                edb_ids.append(edb_id)
                    except (ValueError, IndexError):
                        logger.warning(f"无法从链接 {href} 解析 EDB ID")

            if not edb_ids:
                logger.debug("使用正则从页面内容提取 EDB ID 作为后备")
                content = await page.content()
                pattern = r'/exploits/(\d+)'
                matches = re.findall(pattern, content)
                for match in matches:
                     if match.isdigit() and int(match) not in edb_ids:
                        edb_ids.append(int(match))

            logger.info(f"找到 {len(edb_ids)} 个与 {cve_id} 相关的 EDB-ID: {edb_ids}")
            return edb_ids

        except PlaywrightTimeoutError:
            logger.error(f"页面导航或元素等待超时 ({search_url}) - 可能是 'networkidle' 等待过久或页面加载失败")
        except PlaywrightError as e:
            logger.error(f"Playwright 操作失败: {e}")
        except Exception as e:
            logger.error(f"搜索 EDB ID 时发生未知错误: {str(e)}")
            if self.verbose:
                import traceback
                logger.debug(f"异常堆栈: {traceback.format_exc()}")
        return []

    async def _get_poc_details(self, page, edb_id: int, cve_id: str) -> Optional[Dict]:
        """
        使用 Playwright 获取 POC 详情
        """
        logger.info(f"使用 Playwright 获取 EDB-ID {edb_id} 的 POC 详情")
        detail_url = f"{self.BASE_URL}/exploits/{edb_id}"

        try:
            logger.info(f"导航到详情页: {detail_url}")
            # --- 修改: 使用 'networkidle' 等待更充分加载 ---
            await page.goto(detail_url, timeout=self.playwright_timeout, wait_until='networkidle')
            logger.debug(f"页面 '{page.url}' 加载完成 (networkidle)")

            # 短暂等待
            await asyncio.sleep(random.uniform(1, 3))

            try:
                await page.wait_for_selector('h1, div.card-body', timeout=self.playwright_timeout * 0.8)
                logger.debug("详情页核心内容已加载")
            except PlaywrightTimeoutError:
                logger.warning("等待详情页核心内容超时")

            if self.verbose:
                await self._save_page_content(page, f"playwright_details_{edb_id}")

            metadata = {}
            title_element = await page.query_selector('h1')
            metadata['title'] = (await title_element.text_content()).strip() if title_element else 'Unknown'
            metadata['author'] = await self._extract_metadata_text(page, 'Author:') or 'Unknown'
            metadata['type'] = await self._extract_metadata_text(page, 'Type:') or 'Unknown'
            metadata['platform'] = await self._extract_metadata_text(page, 'Platform:') or 'Unknown'
            date_element = await page.query_selector('//h4[contains(text(),"Date:")]/following-sibling::h6')
            metadata['date'] = (await date_element.text_content()).strip() if date_element else ''
            cve_text = await self._extract_metadata_text(page, 'CVE:')
            metadata['cve'] = cve_text if cve_text == cve_id else 'N/A'

            poc_code = ''
            code_element = await page.query_selector('div.card-body pre code, div.code-view code')
            if code_element:
                poc_code = await code_element.text_content()
            else:
                 logger.debug("未找到主要的 pre code 元素")

            if not poc_code or len(poc_code) < 50:
                logger.info("页面 POC 代码不完整或未找到，尝试访问原始代码页面")
                raw_url = f"{self.BASE_URL}/raw/{edb_id}"
                try:
                    await page.goto(raw_url, timeout=self.playwright_timeout, wait_until='networkidle')
                    # --- 修改: 尝试直接获取页面文本内容，更可靠 ---
                    poc_code = await page.locator('body').text_content()
                    # pre_element = await page.query_selector('pre')
                    # if pre_element:
                    #     poc_code = await pre_element.text_content()
                    # else:
                    #     poc_code = await page.content()
                    logger.info(f"成功从 {raw_url} 获取原始代码/文本内容")
                except PlaywrightTimeoutError:
                     logger.warning(f"访问原始代码页面 {raw_url} 超时")
                except PlaywrightError as e:
                    logger.warning(f"访问原始代码页面 {raw_url} 失败: {e}")

            result = {
                'edb_id': edb_id,
                'cve_id': cve_id,
                'date': metadata.get('date', ''),
                'title': metadata.get('title', ''),
                'type': metadata.get('type', ''),
                'platform': metadata.get('platform', ''),
                'author': metadata.get('author', ''),
                'poc_code': poc_code.strip() if poc_code else '',
                'source': 'exploitdb-playwright'
            }
            logger.info(f"成功解析 EDB-ID {edb_id} 的 POC 详情")
            return result

        except PlaywrightTimeoutError:
            logger.error(f"获取 EDB-ID {edb_id} 详情时页面导航或元素等待超时: {detail_url}")
        except PlaywrightError as e:
            logger.error(f"获取 EDB-ID {edb_id} 详情时 Playwright 操作失败: {e}")
        except Exception as e:
            logger.error(f"获取 EDB-ID {edb_id} 详情时发生未知错误: {str(e)}")
            if self.verbose:
                import traceback
                logger.debug(f"异常堆栈: {traceback.format_exc()}")
        return None

    async def _extract_metadata_text(self, page, label: str) -> Optional[str]:
        """辅助函数：根据标签提取元数据文本"""
        try:
            element = await page.query_selector(f'//h4[contains(text(),"{label}")]/following-sibling::*[1]')
            if element:
                tag_name = await element.evaluate('node => node.tagName.toLowerCase()')
                if tag_name in ['a', 'h6', 'span']:
                     text = await element.text_content()
                     return text.strip() if text else None
            return None
        except PlaywrightError as e:
            logger.debug(f"提取元数据 '{label}' 时出错: {e}")
            return None

    def _verify_poc_matches_cve(self, poc_data: Dict, cve_id: str) -> bool:
        """验证 POC 是否与目标 CVE ID 匹配"""
        if not poc_data:
            return False
        if poc_data.get('cve_id') == cve_id:
             logger.info(f"POC 数据中的 CVE ID ({poc_data.get('cve_id')}) 与目标 CVE ID ({cve_id}) 匹配")
             return True
        if cve_id.lower() in poc_data.get('title', '').lower():
            logger.info(f"POC 标题 '{poc_data.get('title')}' 包含目标 CVE ID '{cve_id}'")
            return True
        if cve_id.lower() in poc_data.get('poc_code', '').lower():
            logger.info(f"POC 代码包含目标 CVE ID '{cve_id}'")
            return True
        cve_components = {
            "log4j": ["log4j", "log4shell", "jndi", "lookup"],
            "spring": ["spring", "springboot", "springframework"],
            "struts": ["struts", "struts2", "apache struts"],
        }
        found_components = []
        for component, keywords in cve_components.items():
            if any(keyword in cve_id.lower() for keyword in keywords):
                found_components.append(component)
                break
        poc_content = (poc_data.get('poc_code', '') + poc_data.get('title', '')).lower()
        for component in found_components:
            keywords_to_check = cve_components.get(component, [])
            if any(keyword in poc_content for keyword in keywords_to_check):
                logger.info(f"POC 内容包含与 CVE ({cve_id}) 相关的组件关键字")
                return True
        logger.warning(f"EDB-ID {poc_data.get('edb_id')} 的 POC 内容与目标 CVE ID {cve_id} 不匹配")
        return False

    async def fetch_poc_by_cve_async(self, cve_id: str) -> List[Dict]:
        """
        通过 CVE ID 异步获取所有匹配的 POC (Playwright 主逻辑)

        参数:
            cve_id: CVE ID，例如 CVE-2021-44228

        返回:
            包含所有匹配 POC 数据字典的列表
        """
        if self.mock_mode:
            logger.info("模拟模式下跳过 Playwright 操作")
            # 返回一个包含模拟数据的列表
            return [{"cve_id": cve_id, "title": "Mock POC", "poc_code": "mock code", "source": "mock"}]

        logger.info(f"使用 Playwright (增强反爬) 获取 {cve_id} 的所有 POC")
        # --- 修改: 初始化空列表用于存储匹配的 POC ---
        matching_pocs: List[Dict] = []

        async with async_playwright() as p:
            browser = None
            context = None
            page = None # 在 finally 中关闭 page
            try:
                browser = await self._launch_browser(p)
                # --- 修改: 添加更真实的上下文选项 ---
                context = await browser.new_context(
                    user_agent=random.choice(self.user_agents),
                    java_script_enabled=True,
                    ignore_https_errors=True,
                    viewport={'width': 1920, 'height': 1080}, # 标准桌面视口
                    locale='en-US', # 美国英语
                    timezone_id='America/New_York', # 时区
                    # color_scheme='light' # 可选：颜色方案
                )
                page = await context.new_page()
                # --- 应用 stealth --- > 放在 page 创建之后
                await stealth_async(page)
                logger.info("Playwright Stealth 已应用到页面")

                edb_ids = await self._search_cve(page, cve_id)

                if not edb_ids:
                    logger.warning(f"未能找到与 {cve_id} 相关的 EDB-ID")
                    # --- 修改: 返回空列表 ---
                    return []

                logger.info(f"找到 {len(edb_ids)} 个可能的 EDB-ID: {edb_ids}")

                for attempt, edb_id in enumerate(edb_ids, 1):
                    logger.info(f"尝试获取 EDB-ID {edb_id} 的详细信息 (尝试 {attempt}/{len(edb_ids)})")
                    await asyncio.sleep(random.uniform(2.0, 4.0)) # 增加延迟

                    poc_data = await self._get_poc_details(page, edb_id, cve_id)

                    if self._verify_poc_matches_cve(poc_data, cve_id):
                        logger.info(f"成功获取并验证了 EDB-ID {edb_id} (CVE: {cve_id}) 的 POC")
                        # --- 修改: 添加到列表而不是返回 ---
                        matching_pocs.append(poc_data)
                    else:
                        logger.debug(f"EDB-ID {edb_id} 的内容与 {cve_id} 不匹配，尝试下一个...")

                # --- 修改: 循环结束后判断列表是否为空 ---
                if not matching_pocs:
                    logger.warning(f"尝试了 {len(edb_ids)} 个 EDB-ID，但未能找到与 {cve_id} 匹配的有效 POC")
                else:
                     logger.info(f"共找到 {len(matching_pocs)} 个与 {cve_id} 匹配的 POC")
                # --- 修改: 返回列表 ---
                return matching_pocs

            except Exception as e:
                logger.error(f"获取 {cve_id} 的 POC 时发生严重错误: {str(e)}")
                if self.verbose:
                    import traceback
                    logger.debug(f"异常堆栈: {traceback.format_exc()}")
                # --- 修改: 发生异常时返回空列表 ---
                return []
            finally:
                # --- 修改: 确保按顺序关闭 page, context, browser ---
                if page:
                    try:
                        await page.close()
                        logger.info("Playwright Page 已关闭")
                    except PlaywrightError as e:
                        logger.warning(f"关闭 Page 时出错: {e}")
                if context:
                    try:
                        await context.close()
                        logger.info("Playwright Context 已关闭")
                    except PlaywrightError as e:
                        logger.warning(f"关闭 Context 时出错: {e}")
                if browser:
                    try:
                        await browser.close()
                        logger.info("Playwright Browser 已关闭")
                    except PlaywrightError as e:
                        logger.warning(f"关闭 Browser 时出错: {e}")

    def fetch_poc_by_cve(self, cve_id: str) -> List[Dict]:
         """
         同步接口，用于从外部调用异步的 fetch_poc_by_cve_async
         """
         # (此函数逻辑不变)
         try:
             loop = asyncio.get_running_loop()
             logger.debug("在现有事件循环中运行 Playwright 任务")
             # 在已运行的loop中运行需要更复杂的处理，取决于环境
             # 对于独立脚本，asyncio.run 是最简单的
             return asyncio.run(self.fetch_poc_by_cve_async(cve_id))
         except RuntimeError: # 没有正在运行的事件循环
             logger.debug("没有找到现有事件循环，创建新循环运行 Playwright 任务")
             # --- 修改: 确保返回列表 ---
             result = asyncio.run(self.fetch_poc_by_cve_async(cve_id))
             return result if isinstance(result, list) else []

    def save_poc_to_json(self, poc_data: Dict, output_dir: str = "data/poc/json", edb_id_for_filename: Optional[int] = None) -> str:
        """将单个 POC 保存为 JSON 文件"""
        if not poc_data: # 添加检查
            logger.error("尝试保存空的 POC 数据")
            return ""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        cve_id_sanitized = "UNKNOWN_CVE"
        if poc_data.get('cve_id') and poc_data['cve_id'] != 'N/A':
             cve_id_sanitized = poc_data['cve_id'].replace('-', '_').replace('/','_')
        elif poc_data.get('edb_id'): # 使用 poc_data 中的 edb_id 作为备用
             cve_id_sanitized = f"EDB_{poc_data['edb_id']}"

        # --- 修改: 在文件名中加入 EDB ID (如果提供) ---
        edb_suffix = f"_EDB_{edb_id_for_filename}" if edb_id_for_filename else f"_EDB_{poc_data.get('edb_id', 'UNKNOWN')}"

        file_path = os.path.join(output_dir, f"{cve_id_sanitized}{edb_suffix}.json")

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(poc_data, f, indent=2, ensure_ascii=False)
            logger.info(f"POC (EDB: {edb_id_for_filename or poc_data.get('edb_id')}) 已保存到: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"保存 POC (EDB: {edb_id_for_filename or poc_data.get('edb_id')}) 到 {file_path} 时出错: {e}")
            return ""

    async def _save_page_content(self, page, prefix: str):
        """辅助函数：保存当前页面内容到文件"""
        debug_dir = Path("debug_output")
        debug_dir.mkdir(exist_ok=True)
        timestamp = int(time.time())
        html_file = debug_dir / f"{prefix}_{timestamp}.html"
        screenshot_file = debug_dir / f"{prefix}_{timestamp}.png"
        try:
            content = await page.content()
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(content)
            logger.debug(f"页面 HTML 已保存到: {html_file}")
            await page.screenshot(path=screenshot_file, full_page=True)
            logger.debug(f"页面截图已保存到: {screenshot_file}")
        except Exception as e:
            logger.warning(f"保存调试信息 ({prefix}) 时出错: {e}") 