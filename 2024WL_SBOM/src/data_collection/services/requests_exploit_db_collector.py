#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""使用requests实现的ExploitDB收集器，用于从ExploitDB获取POC信息，不依赖Selenium"""

import os
import re
import json
import time
import random
import logging
import requests
import urllib3
from pathlib import Path
from typing import Dict, List, Optional
from bs4 import BeautifulSoup

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置日志
logger = logging.getLogger("requests_exploit_db_collector")

class RequestsExploitDBCollector:
    """使用requests实现的ExploitDB收集器，用于从ExploitDB获取POC信息"""

    BASE_URL = "https://www.exploit-db.com"
    SEARCH_URL = f"{BASE_URL}/search"
    
    def __init__(self, proxies: Optional[Dict] = None, timeout: int = 30, 
                 mock_mode: bool = False, verbose: bool = False):
        """
        初始化ExploitDB收集器
        
        参数:
            proxies: 代理设置，格式为{"http": "http://host:port", "https": "https://host:port", "socks": "socks5://host:port"}
            timeout: 请求超时时间（秒）
            mock_mode: 是否使用模拟数据（测试用）
            verbose: 是否输出详细日志
        """
        self.proxies = proxies if proxies else {}
        self.timeout = timeout
        self.mock_mode = mock_mode
        self.verbose = verbose
        
        # 设置日志级别
        if verbose:
            logger.setLevel(logging.DEBUG)
        else:
            logger.setLevel(logging.INFO)
        
        # 配置请求代理
        self.request_proxies = {}
        if 'http' in self.proxies:
            self.request_proxies['http'] = self.proxies['http']
            self.request_proxies['https'] = self.proxies['http']
        if 'https' in self.proxies:
            self.request_proxies['https'] = self.proxies['https']
        
        # 获取随机用户代理
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 Edg/92.0.902.67",
        ]
        
        # 初始化session
        self.session = requests.Session()
        
        # 设置session默认参数
        self.session.verify = False  # 禁用SSL验证
        self.session.proxies = self.request_proxies
        
        # 设置默认头信息
        self.session.headers.update({
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive',
            'Cache-Control': 'max-age=0',
        })

    def _search_cve(self, cve_id: str) -> List[int]:
        """
        在ExploitDB上搜索与CVE相关的EDB ID
        
        参数:
            cve_id: CVE ID
            
        返回:
            EDB ID列表
        """
        logger.info(f"搜索与 {cve_id} 相关的EDB ID")
        
        try:
            # 构建搜索URL
            search_url = f"{self.SEARCH_URL}?cve={cve_id}"
            logger.info(f"访问搜索URL: {search_url}")
            
            # 添加随机User-Agent
            headers = self.session.headers.copy()
            headers['User-Agent'] = random.choice(self.user_agents)
            
            # 发送请求
            response = self.session.get(search_url, headers=headers, timeout=self.timeout)
            
            # 检查响应
            if response.status_code != 200:
                logger.error(f"搜索请求失败，状态码: {response.status_code}")
                return []
            
            # 保存页面内容用于调试
            if self.verbose:
                debug_dir = Path("debug_output")
                debug_dir.mkdir(exist_ok=True)
                debug_file = debug_dir / f"search_{cve_id}_{int(time.time())}.html"
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(response.text)
                logger.debug(f"已保存搜索页面内容到 {debug_file}")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'lxml')
            
            # 提取EDB ID
            edb_ids = []
            
            # 方法1: 查找表格中的链接
            links = soup.select("table tbody tr td a[href*='/exploits/']")
            for link in links:
                href = link.get('href')
                if href and "/exploits/" in href:
                    edb_id = href.split("/")[-1]
                    if edb_id.isdigit() and int(edb_id) not in edb_ids:
                        edb_ids.append(int(edb_id))
            
            # 方法2: 如果表格不存在，查找所有链接
            if not edb_ids:
                all_links = soup.select("a[href*='/exploits/']")
                for link in all_links:
                    href = link.get('href')
                    if href and "/exploits/" in href:
                        edb_id = href.split("/")[-1]
                        if edb_id.isdigit() and int(edb_id) not in edb_ids:
                            edb_ids.append(int(edb_id))
            
            # 方法3: 使用正则表达式从HTML中提取
            if not edb_ids:
                pattern = r'/exploits/(\d+)'
                matches = re.findall(pattern, response.text)
                for match in matches:
                    if match.isdigit() and int(match) not in edb_ids:
                        edb_ids.append(int(match))
            
            logger.info(f"找到 {len(edb_ids)} 个与 {cve_id} 相关的EDB-ID: {edb_ids}")
            return edb_ids
            
        except Exception as e:
            logger.error(f"搜索EDB ID时出错: {str(e)}")
            if self.verbose:
                import traceback
                logger.debug(f"异常堆栈: {traceback.format_exc()}")
            return []

    def _get_poc_details(self, edb_id: int, cve_id: str) -> Optional[Dict]:
        """
        获取POC详情
        
        参数:
            edb_id: EDB ID
            cve_id: CVE ID
            
        返回:
            POC数据字典或None
        """
        logger.info(f"获取 EDB-ID {edb_id} 的POC详情")
        
        try:
            # 访问详情页
            url = f"{self.BASE_URL}/exploits/{edb_id}"
            logger.info(f"访问详情页: {url}")
            
            # 添加随机User-Agent
            headers = self.session.headers.copy()
            headers['User-Agent'] = random.choice(self.user_agents)
            
            # 发送请求
            response = self.session.get(url, headers=headers, timeout=self.timeout)
            
            # 检查响应
            if response.status_code != 200:
                logger.error(f"详情请求失败，状态码: {response.status_code}")
                return None
            
            # 保存页面内容用于调试
            if self.verbose:
                debug_dir = Path("debug_output")
                debug_dir.mkdir(exist_ok=True)
                debug_file = debug_dir / f"details_{edb_id}_{int(time.time())}.html"
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(response.text)
                logger.debug(f"已保存详情页面内容到 {debug_file}")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'lxml')
            
            # 提取元数据
            metadata = {}
            
            # 提取标题
            title_element = soup.find('h1')
            metadata['title'] = title_element.text.strip() if title_element else 'Unknown'
            
            # 提取作者
            author_element = soup.find('h4', string=lambda s: s and 'Author:' in s)
            if author_element:
                author_link = author_element.find_next('a')
                metadata['author'] = author_link.text.strip() if author_link else 'Unknown'
            else:
                metadata['author'] = 'Unknown'
            
            # 提取类型
            type_element = soup.find('h4', string=lambda s: s and 'Type:' in s)
            if type_element:
                type_link = type_element.find_next('a')
                metadata['type'] = type_link.text.strip() if type_link else 'Unknown'
            else:
                metadata['type'] = 'Unknown'
            
            # 提取平台
            platform_element = soup.find('h4', string=lambda s: s and 'Platform:' in s)
            if platform_element:
                platform_link = platform_element.find_next('a')
                metadata['platform'] = platform_link.text.strip() if platform_link else 'Unknown'
            else:
                metadata['platform'] = 'Unknown'
            
            # 提取日期
            date_element = soup.find('h4', string=lambda s: s and 'Date:' in s)
            if date_element:
                date_text = date_element.find_next('h6')
                metadata['date'] = date_text.text.strip() if date_text else ''
            else:
                metadata['date'] = ''
            
            # 提取CVE (确认这是正确的CVE)
            cve_element = soup.find('h4', string=lambda s: s and 'CVE:' in s)
            if cve_element:
                cve_link = cve_element.find_next('a')
                if cve_link and cve_link.text.strip() == cve_id:
                    metadata['cve'] = cve_link.text.strip()
                else:
                    metadata['cve'] = 'N/A'  # CVE不匹配
            else:
                metadata['cve'] = 'N/A'
            
            # 提取POC代码 - 从页面直接获取
            code_element = soup.select_one('div.card-body pre code, div.code-view code')
            if code_element:
                poc_code = code_element.text
            else:
                poc_code = ''
            
            # 如果页面中没有代码，尝试获取原始代码
            if not poc_code or len(poc_code) < 50:  # 代码太短可能不完整
                # 访问原始代码页面
                raw_url = f"{self.BASE_URL}/raw/{edb_id}"
                logger.info(f"访问原始代码页面: {raw_url}")
                
                raw_response = self.session.get(raw_url, headers=headers, timeout=self.timeout)
                
                if raw_response.status_code == 200:
                    poc_code = raw_response.text
                else:
                    logger.warning(f"获取原始代码失败，状态码: {raw_response.status_code}")
            
            # 构建结果字典
            result = {
                'edb_id': edb_id,
                'cve_id': cve_id,
                'date': metadata.get('date', ''),
                'title': metadata.get('title', ''),
                'type': metadata.get('type', ''),
                'platform': metadata.get('platform', ''),
                'author': metadata.get('author', ''),
                'poc_code': poc_code,
                'source': 'exploitdb'
            }
            
            logger.info(f"成功获取 EDB-ID {edb_id} 的POC详情")
            return result
            
        except Exception as e:
            logger.error(f"获取 EDB-ID {edb_id} 的POC详情时出错: {str(e)}")
            if self.verbose:
                import traceback
                logger.debug(f"异常堆栈: {traceback.format_exc()}")
            return None

    def _verify_poc_matches_cve(self, poc_data: Dict, cve_id: str) -> bool:
        """
        验证POC是否与CVE匹配
        
        参数:
            poc_data: POC数据
            cve_id: CVE ID
            
        返回:
            是否匹配
        """
        # 检查1: 直接匹配CVE ID
        if poc_data.get('cve_id') == cve_id:
            logger.info(f"POC的CVE ID与目标CVE ID匹配")
            return True
        
        # 检查2: 检查标题中是否包含CVE ID
        if cve_id.lower() in poc_data.get('title', '').lower():
            logger.info(f"POC标题包含目标CVE ID")
            return True
        
        # 检查3: 检查POC代码中是否包含CVE ID
        if cve_id.lower() in poc_data.get('poc_code', '').lower():
            logger.info(f"POC代码包含目标CVE ID")
            return True
        
        # 提取CVE ID的组件名，例如CVE-2021-44228中的log4j
        cve_components = {
            "log4j": ["log4j", "log4shell", "jndi", "lookup"],
            "spring": ["spring", "springboot", "springframework"],
            "struts": ["struts", "struts2", "apache struts"],
            "jenkins": ["jenkins", "jenkin"],
            "wordpress": ["wordpress", "wp", "wp-"],
            "drupal": ["drupal", "drupals"],
            "joomla": ["joomla", "joomlas"]
        }
        
        # 尝试从CVE ID中提取组件名
        found_components = []
        for component, keywords in cve_components.items():
            for keyword in keywords:
                if keyword in cve_id.lower():
                    found_components.append(component)
                    break
        
        # 检查POC内容是否包含与CVE相关的组件名称
        poc_content = (poc_data.get('poc_code', '') + poc_data.get('title', '')).lower()
        for component in found_components:
            for keyword in cve_components[component]:
                if keyword in poc_content:
                    logger.info(f"POC内容包含与CVE相关的组件名称 '{keyword}'")
                    return True
        
        logger.warning(f"POC与目标CVE ID不匹配")
        return False

    def fetch_poc_by_cve(self, cve_id: str) -> Optional[Dict]:
        """
        通过CVE ID获取POC
        
        参数:
            cve_id: CVE ID，例如 CVE-2021-44228
            
        返回:
            POC数据字典或None
        """
        logger.info(f"获取 {cve_id} 的POC")
        
        try:
            # 进行搜索
            edb_ids = self._search_cve(cve_id)
            
            if not edb_ids:
                logger.warning(f"未找到与 {cve_id} 相关的 EDB-ID")
                return None
            
            logger.info(f"找到 {len(edb_ids)} 个与 {cve_id} 相关的 EDB-ID: {edb_ids}")
            
            # 获取每个EDB的POC详情，直到找到有效的
            for attempt, edb_id in enumerate(edb_ids, 1):
                logger.info(f"尝试获取 EDB-ID {edb_id} 的详细信息 (尝试 {attempt}/{len(edb_ids)})")
                
                # 获取POC详情
                poc_data = self._get_poc_details(edb_id, cve_id)
                
                if poc_data and self._verify_poc_matches_cve(poc_data, cve_id):
                    # 确保CVE ID正确
                    poc_data['cve_id'] = cve_id
                    logger.info(f"成功获取并验证 EDB-ID {edb_id} 的详细POC信息")
                    return poc_data
                
                # 添加随机延迟，避免请求过快
                if attempt < len(edb_ids):
                    delay = random.uniform(2.0, 4.0)
                    logger.debug(f"等待 {delay:.2f} 秒后尝试下一个 EDB-ID")
                    time.sleep(delay)
            
            logger.warning(f"无法找到与 {cve_id} 匹配的有效POC，尝试了 {len(edb_ids)} 个 EDB-ID")
            return None
            
        except Exception as e:
            logger.error(f"获取 {cve_id} 的POC时出错: {str(e)}")
            if self.verbose:
                import traceback
                logger.debug(f"异常堆栈: {traceback.format_exc()}")
            return None

    def save_poc_to_json(self, poc_data: Dict, output_dir: str = "data/poc/json") -> str:
        """
        将POC保存为JSON文件
        
        参数:
            poc_data: POC数据
            output_dir: 输出目录
            
        返回:
            保存的文件路径
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        file_path = os.path.join(output_dir, f"{poc_data['cve_id'].replace('-', '_')}.json")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(poc_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"POC已保存到: {file_path}")
        return file_path 