import sqlite3
import os
import sys

# Add project root to Python path to fix imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from abc import ABC, abstractmethod
from source.utils.configSentinal import CONFIG_NEO4J_PATH, ConfigHandler

# Base data model classes
class BaseDataModel:
    """Base class for all data models"""
    pass

class ExploitDetail(BaseDataModel):
    """Data model for ExploitDetail database records"""
    def __init__(self, poc_id, exploit_db_id, title, poc,
                 publication_date, author, platform, cve, t, v):
        self.poc_id = poc_id
        self.exploit_db_id = exploit_db_id
        self.title = title
        self.poc = poc
        # self.complexity = complexity
        # self.exploit_difficulty = exploit_difficulty
        self.publication_date = publication_date
        self.author = author
        self.platform = platform
        self.cve = cve
        self.t = t  # type
        self.v = v  # verified? 1 or 0

class CNNVDDetail(BaseDataModel):
    """Data model for CNNVD database records"""
    def __init__(self, cnnvd_id, vul_name, cve_id, vul_type, 
                 hazard_level, vul_desc, refer_url, patch, update_time):
        self.cnnvd_id = cnnvd_id
        self.vul_name = vul_name
        self.cve_id = cve_id
        self.vul_type = vul_type
        self.hazard_level = hazard_level
        self.vul_desc = vul_desc
        self.refer_url = refer_url
        self.patch = patch
        self.update_time = update_time

class CNVDDetail(BaseDataModel):
    """Data model for CNVD database records"""
    def __init__(self, cnvd_id, vul_name, hazard_level, vul_type, 
                 publish_date, cve_id, vul_desc, vul_solution):
        self.cnvd_id = cnvd_id
        self.vul_name = vul_name
        self.hazard_level = hazard_level
        self.vul_type = vul_type
        self.publish_date = publish_date
        self.cve_id = cve_id
        self.vul_desc = vul_desc
        self.vul_solution = vul_solution

# Abstract converter class
class DatabaseConverter(ABC):
    """Abstract base class for database converters"""
    def __init__(self, driver, db_path, table_name):
        self.driver = driver
        self.db_path = db_path
        self.table_name = table_name
    
    @abstractmethod
    def create_node_in_neo4j(self, driver, data_model):
        """Create a node in Neo4j"""
        pass
    
    @abstractmethod
    def convert_row_to_model(self, row):
        """Convert a database row to a data model"""
        pass
    
    def convert_database(self):
        """Convert the entire database to Neo4j"""
        # Check if database file exists
        if not os.path.exists(self.db_path):
            raise FileNotFoundError(f"Database file not found: {self.db_path}")
            
        # SQLite connection
        sqlite_conn = sqlite3.connect(self.db_path)
        cursor = sqlite_conn.cursor()

        try:
            # Read data from SQLite
            cursor.execute(f"SELECT * FROM {self.table_name}")
            rows = cursor.fetchall()
            
            print(f"Converting {len(rows)} records from {self.table_name}")
            
            for i, row in enumerate(rows):
                try:
                    data_model = self.convert_row_to_model(row)
                    self.create_node_in_neo4j(self.driver, data_model)
                    # Print progress every 100 records
                    if (i + 1) % 100 == 0:
                        print(f"Processed {i + 1}/{len(rows)} records")
                except Exception as e:
                    print(f"Error processing record {i}: {e}")
            
            print(f"Finished converting {self.table_name}")
        finally:
            # Close connections
            cursor.close()
            sqlite_conn.close()

# Specific converter implementations
class ExploitDetailConverter(DatabaseConverter):
    """Converter for ExploitDetail database"""
    def create_node_in_neo4j(self, driver, data_model):
        with driver.session() as session:
            # Create POC node
            session.run("""
            MERGE (p:POC {poc_id: $poc_id})
            SET p.code_snippet = $code_snippet,
                p.exploit_db_id = $exploit_db_id,
                p.publication_date = $publication_date,
                p.author = $author,
                p.platform = $platform,
                p.title = $title,
                p.cve = $cve,
                p.type = $type,
                p.verified = $verified
            """,
                poc_id=data_model.poc_id,
                exploit_db_id=data_model.exploit_db_id,
                code_snippet=data_model.poc,
                publication_date=data_model.publication_date,
                author=data_model.author,
                platform=data_model.platform,
                title=data_model.title,
                cve=data_model.cve,
                type=data_model.t,
                verified=data_model.v
            )
    
    def convert_row_to_model(self, row):
        # Handle 'N/A' CVE values
        tmp = row[7]  # CVE is at index 7 in exploitDetail
        if tmp == 'N/A':
            tmp = "null"
        
        # Create ExploitDetail object
        return ExploitDetail(
            poc_id='POC-' + tmp + '-' + str(row[0]),
            exploit_db_id=row[0],
            title=row[3],
            author=row[6],
            platform=row[5],
            t=row[4],
            cve=tmp,
            v=row[2],
            publication_date=row[1],
            poc=row[8]
        )

class CNNVDConverter(DatabaseConverter):
    """Converter for CNNVD database"""
    def create_node_in_neo4j(self, driver, data_model):
        with driver.session() as session:
            # Create Vulnerability node with CNNVD data
            session.run("""
            MERGE (v:Vulnerability {vuln_id: $cnnvd_id})
            SET v.name = $vul_name,
                v.cve_id = $cve_id,
                v.type = $vul_type,
                v.hazard_level = $hazard_level,
                v.description = $vul_desc,
                v.reference_url = $refer_url,
                v.patch = $patch,
                v.update_time = $update_time,
                v.source = 'CNNVD'
            """,
                cnnvd_id=data_model.cnnvd_id,
                vul_name=data_model.vul_name,
                cve_id=data_model.cve_id,
                vul_type=data_model.vul_type,
                hazard_level=data_model.hazard_level,
                vul_desc=data_model.vul_desc,
                refer_url=data_model.refer_url,
                patch=data_model.patch,
                update_time=data_model.update_time
            )
    
    def convert_row_to_model(self, row):
        # Create CNNVDDetail object
        return CNNVDDetail(
            cnnvd_id=row[0],
            vul_name=row[1],
            cve_id=row[2],
            vul_type=row[3],
            hazard_level=row[4],
            vul_desc=row[5],
            refer_url=row[6],
            patch=row[7],
            update_time=row[8]
        )

class CNVDConverter(DatabaseConverter):
    """Converter for CNVD database"""
    def create_node_in_neo4j(self, driver, data_model):
        with driver.session() as session:
            # Create Vulnerability node with CNVD data
            session.run("""
            MERGE (v:Vulnerability {vuln_id: $cnvd_id})
            SET v.name = $vul_name,
                v.hazard_level = $hazard_level,
                v.type = $vul_type,
                v.publish_date = $publish_date,
                v.cve_id = $cve_id,
                v.description = $vul_desc,
                v.solution = $vul_solution,
                v.source = 'CNVD'
            """,
                cnvd_id=data_model.cnvd_id,
                vul_name=data_model.vul_name,
                hazard_level=data_model.hazard_level,
                vul_type=data_model.vul_type,
                publish_date=data_model.publish_date,
                cve_id=data_model.cve_id,
                vul_desc=data_model.vul_desc,
                vul_solution=data_model.vul_solution
            )
    
    def convert_row_to_model(self, row):
        # This implementation may need adjustment based on exact schema
        return CNVDDetail(
            cnvd_id=row[0],
            vul_name=row[1],
            hazard_level=row[2],
            vul_type=row[3],
            publish_date=row[4],
            cve_id=row[5] if row[5] != 'N/A' else "null",
            vul_desc=row[6],
            vul_solution=row[7]
        )

# Factory to create the appropriate converter
class ConverterFactory:
    """Factory class to create database converters"""
    @staticmethod
    def get_converter(db_choice, driver):
        """Get the appropriate converter based on user choice"""
        # Use the project_root defined at the module level
        data_dir = os.path.join(project_root, "data")
        os.makedirs(data_dir, exist_ok=True)  # Ensure data directory exists
        
        if db_choice == '1':
            # CNNVD data
            db_path = os.path.join(data_dir, "cnnvd_data.db")
            # Check current directory as fallback
            if not os.path.exists(db_path):
                local_path = os.path.join(current_dir, "cnnvd_data.db")
                if os.path.exists(local_path):
                    db_path = local_path
                else:
                    local_path = os.path.join(os.getcwd(), "cnnvd_data.db")
                    if os.path.exists(local_path):
                        db_path = local_path
            return CNNVDConverter(driver, db_path, "cnnvd_data")
        elif db_choice == '2':
            # CNVD vulnerabilities
            db_path = os.path.join(data_dir, "cnvd_vulnerabilities.db")
            # Check current directory as fallback
            if not os.path.exists(db_path):
                local_path = os.path.join(current_dir, "cnvd_vulnerabilities.db")
                if os.path.exists(local_path):
                    db_path = local_path
                else:
                    local_path = os.path.join(os.getcwd(), "cnvd_vulnerabilities.db")
                    if os.path.exists(local_path):
                        db_path = local_path
            return CNVDConverter(driver, db_path, "cnvd_vulnerabilities")
        elif db_choice == '3':
            # ExploitDetailed
            db_path = os.path.join(data_dir, "exploitDetailed.db")
            # Check current directory as fallback
            if not os.path.exists(db_path):
                local_path = os.path.join(current_dir, "exploitDetailed.db")
                if os.path.exists(local_path):
                    db_path = local_path
                else:
                    local_path = os.path.join(os.getcwd(), "exploitDetailed.db")
                    if os.path.exists(local_path):
                        db_path = local_path
            return ExploitDetailConverter(driver, db_path, "exploitDetail")
        else:
            raise ValueError("Invalid database choice")

def main():
    """Main function to run the database conversion"""
    print("SQLite to Neo4j Converter")
    print("-------------------------")
    print("Select database to convert:")
    print("1 - CNNVD Vulnerability Data")
    print("2 - CNVD Vulnerabilities")
    print("3 - Exploit Detailed")
    
    choice = input("Enter your choice (1-3): ")
    
    # Validate input
    if choice not in ['1', '2', '3']:
        print("Invalid choice. Please select 1, 2, or 3.")
        return
    
    try:
        # Get Neo4j connection
        print("Connecting to Neo4j...")
        handler = ConfigHandler(CONFIG_NEO4J_PATH)
        driver = handler.driver
        
        # Test Neo4j connection
        try:
            with driver.session() as session:
                result = session.run("RETURN 1 AS test")
                result.single()
                print("Neo4j connection successful!")
        except Exception as e:
            print(f"Error connecting to Neo4j: {e}")
            print(f"Please check your Neo4j configuration in {CONFIG_NEO4J_PATH}")
            print("Make sure Neo4j is running and the connection details are correct.")
            return
        
        # Create converter based on choice
        try:
            converter = ConverterFactory.get_converter(choice, driver)
        except FileNotFoundError as e:
            print(f"Error: {e}")
            print("Please make sure the database file exists.")
            return
        
        # Run conversion
        print(f"Starting conversion from database file: {converter.db_path}")
        print(f"Table: {converter.table_name}")
        converter.convert_database()
        
        print("Conversion completed successfully!")
    except Exception as e:
        print(f"Error during conversion: {e}")
    finally:
        # Close Neo4j connection if it was created
        if 'driver' in locals():
            driver.close()

if __name__ == '__main__':
    main()
