#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动化漏洞监控和预警系统入口

提供了系统各功能的统一入口点，支持通过命令行参数调用不同的服务。
"""

import os
import sys
import argparse
from datetime import datetime, timedelta
import logging
import re
import urllib3
import json

# 禁用SSL验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 设置环境变量，确保正确的编码
os.environ["PYTHONIOENCODING"] = "utf-8"

# 直接控制台输出，确保可见
print("\n" + "="*80)
print("自动化漏洞监控和预警系统启动中...")
print("="*80 + "\n")

# --- 修正 sys.path 设置 --- 
# Explicitly add the project root to sys.path
project_root = "/home/<USER>"
if project_root not in sys.path:
    sys.path.insert(0, project_root)
# --- 结束修正 ---

# 现在可以安全地进行基于 src 的导入了
from src.data_collection.services.collector import CollectorService
# 引入分析服务
from src.data_collection.services.analyzer import AnalyzerService
from src.data_collection.services.analyzer import AnalyzerService
# 引入导出服务
from src.data_collection.services.exporter import ExporterService
from src.data_collection.services.exporter import ExporterService
# 引入图数据导入服务
from src.data_collection.services.graph_importer import GraphImporter
from src.data_collection.services.graph_store import GraphStore
# 工具类在 src/data_collection/utils 下
from src.data_collection.utils.logger import setup_logger
# --- 修正 POC 服务的导入路径 (应该在 src/services) ---
from src.data_collection.services.poc_collection_service import PocCollectionService
# --- 修正 DB 工具的导入路径 (在 src/data_collection/utils) ---
from src.data_collection.utils.db_utils import get_cves_in_daterange, get_latest_collected_cves
# 导入补丁跟踪器相关模块
from src.data_collection.services.patch_tracer import (
    PatchTracer,
    CVEReferenceFetcher,
    get_initial_references,
    prepare_references_for_tracer,
    prioritize_initial_references
)
# 导入CNNVD爬虫相关模块
from src.data_collection.collectors.cnnvd import CNNVDCrawler
# 导入CNVD爬虫
from src.data_collection.collectors.cnvd import run_cnvd_crawler
# 引入风险评估服务
from src.data_collection.services.risk_assessment import RiskAssessmentService

def setup_logging(args):
    """
    设置日志配置
    
    Args:
        args: 命令行参数
    """
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 根据参数设置日志级别
    console_level = "INFO"  # 默认控制台级别
    
    # 如果指定了详细模式，设置为DEBUG级别
    if args.verbose:
        console_level = "DEBUG"
    
    # 如果指定了安静模式，设置为WARNING级别
    if args.quiet:
        console_level = "WARNING"
    
    # 配置日志记录器
    logger = setup_logger(
        log_dir="logs",
        console_level=console_level,
        file_level="DEBUG"
    )
    
    print(f"日志级别设置为: 控制台={console_level}, 文件=DEBUG")
    return logger

def setup_paths():
    """设置路径"""
    # 确保工作目录是项目根目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 添加当前目录到路径
    if '' not in sys.path:
        sys.path.insert(0, '')

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='自动化漏洞监控和预警系统')
    
    # 全局选项
    parser.add_argument('--config', type=str, default='config/sources.json',
                       help='配置文件路径 (默认: config/sources.json)')
    parser.add_argument('--db-url', type=str, default='sqlite:////home/<USER>/data/vulnerabilities.db',
                       help='主漏洞数据库连接URL (默认: sqlite:////home/<USER>/data/vulnerabilities.db)')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='详细输出模式')
    parser.add_argument('-q', '--quiet', action='store_true',
                       help='安静模式，只显示警告和错误')
    
    # 创建子命令解析器
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 数据采集命令
    collect_parser = subparsers.add_parser('collect', help='漏洞数据采集')
    collect_parser.add_argument('--sources', nargs='+', 
                              choices=['nvd', 'github', 'redhat', 'debian', 'all'],
                              default=['all'],
                              help='要采集的数据源 (默认: all)')
    collect_parser.add_argument('--start-date', type=str,
                              help='开始日期 (格式: YYYY-MM-DD，默认: 7天前)')
    collect_parser.add_argument('--end-date', type=str,
                              help='结束日期 (格式: YYYY-MM-DD，默认: 当前日期)')
    collect_parser.add_argument('--days', type=int, default=7,
                              help='采集最近几天的数据 (默认: 7)')
    collect_parser.add_argument('--full', action='store_true',
                              help='完整更新模式 (默认: 增量更新)')
    
    # --- 添加 POC 收集相关参数 ---
    collect_parser.add_argument('--collect-poc', action='store_true', default=False,
                               help='在漏洞数据采集后自动收集 POC')
    collect_parser.add_argument('--poc-db-path', type=str, default='/home/<USER>/data/poc/exploitDetailed.db',
                               help='用于存储 POC 的 SQLite 数据库路径')
    collect_parser.add_argument('--save-poc-to-json', action='store_true', default=False,
                               help='将收集到的 POC 保存为 JSON 文件')
    collect_parser.add_argument('--poc-json-output-dir', type=str, default='/home/<USER>/data/poc/json',
                               help='POC JSON 文件的输出目录')
    collect_parser.add_argument('--save-poc-to-neo4j', action='store_true', default=False,
                               help='将收集到的 POC 保存到 Neo4j (需要配置 Neo4j 环境变量)')
    collect_parser.add_argument('--use-llm-for-poc', action='store_true', default=False,
                               help='如果 ExploitDB 未找到 POC，则尝试使用 LLM 生成')
    collect_parser.add_argument('--force-poc-fetch', action='store_true', default=False,
                               help='强制从网络获取 POC，忽略本地 POC SQLite 缓存')
    collect_parser.add_argument('--poc-timeout', type=int, default=60,
                               help='POC 收集操作（如 Playwright）的超时时间（秒）')
    collect_parser.add_argument('--poc-delay', type=float, default=0,
                               help='处理每个 CVE 的 POC 后添加的随机延迟（秒）')
    collect_parser.add_argument('--no-nvd-api', action='store_false', dest='use_nvd_api', default=True,
                               help='不使用 NVD API 获取 CVE 详情（生成 POC 时）')
    # 注意：代理参数 (--proxy, --socks-proxy) 和 --verbose 已在全局参数中定义，可复用
    # ---------------------------
    
    # 数据分析命令
    analyze_parser = subparsers.add_parser('analyze', help='漏洞数据分析')
    analyze_parser.add_argument('--graph-url', type=str, default='bolt://localhost:7687',
                               help='图数据库连接URL (默认: bolt://localhost:7687)')
    analyze_parser.add_argument('--graph-user', type=str, default='neo4j',
                               help='图数据库用户名 (默认: neo4j)')
    analyze_parser.add_argument('--graph-password', type=str, default='password',
                               help='图数据库密码 (默认: password)')
    analyze_parser.add_argument('--vuln-id', type=str,
                              help='漏洞ID (CVE ID或其他标识)')
    analyze_parser.add_argument('--component', type=str,
                              help='组件名称')
    analyze_parser.add_argument('--start-component', type=str,
                              help='起始组件 (用于路径分析)')
    analyze_parser.add_argument('--end-component', type=str,
                              help='目标组件 (用于路径分析)')
    analyze_parser.add_argument('--output', type=str, default='/home/<USER>/data/analysis',
                               help='分析结果输出目录 (默认: /home/<USER>/data/analysis)')
    
    # 导出命令
    export_parser = subparsers.add_parser('export', help='导出数据')
    export_parser.add_argument('--format', choices=['json', 'csv', 'xlsx', 'html', 'markdown'], default='json',
                             help='导出格式 (默认: json)')
    export_parser.add_argument('--output', type=str, default='/home/<USER>/data/export',
                             help='输出目录 (默认: /home/<USER>/data/export)')
    export_parser.add_argument('--source', type=str,
                             help='按数据源筛选 (例如: nvd, github)')
    export_parser.add_argument('--start-date', type=str,
                             help='开始日期 (格式: YYYY-MM-DD)')
    export_parser.add_argument('--end-date', type=str,
                             help='结束日期 (格式: YYYY-MM-DD)')
    export_parser.add_argument('--days', type=int,
                             help='最近几天的数据')
    export_parser.add_argument('--template', type=str,
                             help='HTML或Markdown模板文件 (用于html或markdown格式)')
    
    # 图数据导入命令
    graph_import_parser = subparsers.add_parser('graph-import', help='导入数据到图数据库')
    graph_import_parser.add_argument('--graph-url', type=str, default='bolt://localhost:7687',
                                   help='图数据库连接URL (默认: bolt://localhost:7687)')
    graph_import_parser.add_argument('--graph-user', type=str, default='neo4j',
                                   help='图数据库用户名 (默认: neo4j)')
    graph_import_parser.add_argument('--graph-password', type=str, default='password',
                                   help='图数据库密码 (默认: password)')
    graph_import_parser.add_argument('--source', type=str,
                                   help='按数据源筛选导入 (例如: nvd, github)')
    graph_import_parser.add_argument('--start-date', type=str,
                                   help='开始日期 (格式: YYYY-MM-DD)')
    graph_import_parser.add_argument('--end-date', type=str,
                                   help='结束日期 (格式: YYYY-MM-DD)')
    graph_import_parser.add_argument('--batch-size', type=int, default=100,
                                   help='批量导入大小 (默认: 100)')
    graph_import_parser.add_argument('--test-mode', action='store_true',
                                   help='测试模式，不实际连接Neo4j')
    
    # POC测试命令
    poc_test_parser = subparsers.add_parser('test-poc', help='测试单个或多个CVE的POC搜索功能')
    poc_test_parser.add_argument('--cve-ids', nargs='+', required=True,
                               help='要测试POC搜索的CVE ID列表，例如 CVE-2021-44228 CVE-2021-45046')
    poc_test_parser.add_argument('--poc-db-path', type=str, default='/home/<USER>/data/poc/exploitDetailed.db',
                               help='用于存储POC的SQLite数据库路径')
    poc_test_parser.add_argument('--save-poc-to-json', action='store_true', default=False,
                               help='将收集到的POC保存为JSON文件')
    poc_test_parser.add_argument('--poc-json-output-dir', type=str, default='/home/<USER>/data/poc/json',
                               help='POC JSON文件的输出目录')
    poc_test_parser.add_argument('--save-poc-to-neo4j', action='store_true', default=False,
                               help='将收集到的POC保存到Neo4j (需要配置Neo4j环境变量)')
    poc_test_parser.add_argument('--use-llm-for-poc', action='store_true', default=False,
                               help='如果ExploitDB未找到POC，则尝试使用LLM生成')
    poc_test_parser.add_argument('--force-poc-fetch', action='store_true', default=False,
                               help='强制从网络获取POC，忽略本地POC SQLite缓存')
    poc_test_parser.add_argument('--poc-timeout', type=int, default=60,
                               help='POC收集操作（如Playwright）的超时时间（秒）')
    poc_test_parser.add_argument('--poc-delay', type=float, default=0,
                               help='处理每个CVE的POC后添加的随机延迟（秒）')
    poc_test_parser.add_argument('--no-nvd-api', action='store_false', dest='use_nvd_api', default=True,
                               help='不使用NVD API获取CVE详情（生成POC时）')
    
    # 补丁跟踪器命令
    patch_trace_parser = subparsers.add_parser('patch-trace', help='CVE补丁跟踪器')
    
    # 补丁跟踪模式选择（单个CVE或时间范围）
    patch_trace_mode = patch_trace_parser.add_mutually_exclusive_group(required=True)
    patch_trace_mode.add_argument('--cve', '-c', type=str,
                              help='要分析的CVE ID，例如：CVE-2021-44832')
    patch_trace_mode.add_argument('--start-date', type=str,
                              help='开始日期 (YYYY-MM-DD)')
    
    # 时间范围相关参数
    patch_trace_parser.add_argument('--end-date', type=str,
                              help='结束日期 (YYYY-MM-DD)，默认为今天')
    patch_trace_parser.add_argument('--date-type', choices=['published', 'modified'],
                              default='published', help='日期类型，默认为published')
    patch_trace_parser.add_argument('--max-cves', type=int, default=0,
                              help='最多处理的CVE数量，默认不限制')
    patch_trace_parser.add_argument('--newest-first', action='store_true',
                              help='优先处理最新的CVE')
    
    # 基本参数
    patch_trace_parser.add_argument('--data-dir', '-d', type=str, default='/home/<USER>/data/patch',
                              help='数据保存目录，默认为/home/<USER>/data/patch')
    patch_trace_parser.add_argument('--config-path', type=str, default=None,
                              help='配置文件路径，默认自动查找')
    patch_trace_parser.add_argument('--depth', type=int, default=3,
                              help='最大引用深度，默认为3')
    patch_trace_parser.add_argument('--delay', type=float, default=0.5,
                              help='请求延迟(秒)，默认为0.5秒')
    patch_trace_parser.add_argument('--github-token', type=str, default=None,
                              help='GitHub API令牌，默认从配置文件获取')
    patch_trace_parser.add_argument('--confidence', type=float, default=0.7,
                              help='补丁置信度阈值，默认0.7')
    patch_trace_parser.add_argument('--no-verify-ssl', action='store_true',
                              help='禁用SSL证书验证')
    patch_trace_parser.add_argument('--skip-url-check', action='store_true',
                              help='跳过URL可访问性检查')
    patch_trace_parser.add_argument('--use-cache', action='store_true', default=True,
                              help='使用缓存数据（如果存在）')
    patch_trace_parser.add_argument('--retries', type=int, default=3,                              help='请求重试次数，默认3次')
    
    # 优先级相关参数
    patch_trace_parser.add_argument('--no-priority', action='store_true',
                              help='禁用引用优先级排序')
    patch_trace_parser.add_argument('--min-priority', choices=['critical', 'high', 'medium', 'low', 'very_low'],
                              default='medium', help='最低包含的优先级(默认medium)')
    
    # 语言筛选参数
    patch_trace_parser.add_argument('--language', type=str,
                              help='按编程语言筛选补丁(如java,python,c++)')
    
    # CNNVD爬虫命令
    cnnvd_parser = subparsers.add_parser('cnnvd', help='CNNVD漏洞数据采集')
    cnnvd_subparsers = cnnvd_parser.add_subparsers(dest='cnnvd_command', help='CNNVD爬虫子命令')
    
    # 基本爬取命令
    cnnvd_crawl_parser = cnnvd_subparsers.add_parser('crawl', help='爬取CNNVD数据')
    cnnvd_crawl_parser.add_argument('--start-page', '-s', type=int, default=1, 
                                  help='起始页码 (默认: 1)')
    cnnvd_crawl_parser.add_argument('--end-page', '-e', type=int, default=3, 
                                  help='结束页码 (默认: 3)')
    cnnvd_crawl_parser.add_argument('--threads', '-t', type=int, default=5, 
                                  help='线程数量 (默认: 5)')
    cnnvd_crawl_parser.add_argument('--db-path', type=str, 
                                  default='/home/<USER>/data/cnnvd_data.db',
                                  help='数据库路径 (默认: /home/<USER>/data/cnnvd_data.db)')
    cnnvd_crawl_parser.add_argument('--delay', type=float, default=1.0,
                                  help='请求延迟(秒) (默认: 1.0)')
    
    # 搜索命令
    cnnvd_search_parser = cnnvd_subparsers.add_parser('search', help='搜索CNNVD数据')
    cnnvd_search_parser.add_argument('--keyword', '-k', type=str, required=True,
                                   help='搜索关键词 (必需)')
    cnnvd_search_parser.add_argument('--hazard-level', type=str, default='',
                                   help='危害等级 (1:严重; 2:高危; 3:中危; 4:低危)')
    cnnvd_search_parser.add_argument('--db-path', type=str, 
                                   default='/home/<USER>/data/cnnvd_data.db',
                                   help='数据库路径 (默认: /home/<USER>/data/cnnvd_data.db)')
    
    # 导出命令
    cnnvd_export_parser = cnnvd_subparsers.add_parser('export', help='导出CNNVD数据')
    cnnvd_export_parser.add_argument('--output', '-o', type=str, required=True,
                                   help='导出文件路径 (必需)')
    cnnvd_export_parser.add_argument('--format', '-f', choices=['json', 'csv'], default='json',
                                   help='导出格式 (默认: json)')
    cnnvd_export_parser.add_argument('--db-path', type=str, 
                                   default='/home/<USER>/data/cnnvd_data.db',
                                   help='数据库路径 (默认: /home/<USER>/data/cnnvd_data.db)')
    cnnvd_export_parser.add_argument('--limit', '-l', type=int, default=0,
                                   help='导出数量限制 (默认: 0, 导出全部)')
    
    # CNVD爬虫命令
    cnvd_parser = subparsers.add_parser('cnvd', help='CNVD漏洞数据采集')
    cnvd_parser.add_argument('--start-page', '-s', type=int, default=1, 
                            help='起始页码 (默认: 1)')
    cnvd_parser.add_argument('--end-page', '-e', type=int, default=3, 
                            help='结束页码 (默认: 3)')
    cnvd_parser.add_argument('--sleep-time', '-t', type=float, default=4, 
                            help='爬取延迟(秒) (默认: 4)')
    cnvd_parser.add_argument('--db-path', type=str, 
                            default='/home/<USER>/data/cnvd_vulnerabilities.db',
                            help='数据库路径 (默认: /home/<USER>/data/cnvd_vulnerabilities.db)')
    
    # 风险评估命令
    risk_parser = subparsers.add_parser('assess-risk', help='漏洞风险评估')
    risk_parser.add_argument('--cve', type=str, required=True,
                           help='要评估的CVE ID，例如：CVE-2021-44228')
    risk_parser.add_argument('--output', type=str, 
                           default='/home/<USER>/data/risk_assessment',
                           help='风险评估结果输出目录 (默认: /home/<USER>/data/risk_assessment)')
    risk_parser.add_argument('--graph-url', type=str, default='bolt://localhost:7687',
                           help='图数据库连接URL (默认: bolt://localhost:7687)')
    risk_parser.add_argument('--graph-user', type=str, default='neo4j',
                           help='图数据库用户名 (默认: neo4j)')
    risk_parser.add_argument('--graph-password', type=str, default='password',
                           help='图数据库密码 (默认: password)')
    risk_parser.add_argument('--poc-db-path', type=str, 
                           default='/home/<USER>/data/poc/exploitDetailed.db',
                           help='POC数据库路径 (默认: /home/<USER>/data/poc/exploitDetailed.db)')
    risk_parser.add_argument('--no-verify-ssl', action='store_true',
                           help='禁用SSL证书验证')
    risk_parser.add_argument('--quiet-warnings', action='store_true',
                           help='隐藏警告和非关键错误消息')
    
    return parser.parse_args()

def run_collect(args, logger):
    """采集子命令"""
    # 解析日期
    start_date = None
    end_date = None
    
    if args.start_date:
        try:
            start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        except ValueError:
            logger.error(f"无效的开始日期格式: {args.start_date}，应为 YYYY-MM-DD")
            return
    
    if args.end_date:
        try:
            end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        except ValueError:
            logger.error(f"无效的结束日期格式: {args.end_date}，应为 YYYY-MM-DD")
            return

    # 如果指定了start_date但没有指定end_date，默认end_date为当前日期
    if start_date and not end_date:
        end_date = datetime.now()
    
    # 初始化服务
    service = CollectorService(
        config_path=args.config,
        db_url=args.db_url
    )
    
    # 解析数据源
    sources = None if 'all' in args.sources else args.sources
    
    # 全量更新
    if args.full:
        logger.info(f"运行全量更新，数据源: {sources or '所有'}")
        stats = service.run_full(start_date, end_date, sources)
    # 增量更新
    else:
        if start_date and end_date:
            # 如果提供了明确的日期范围，直接传递这些日期
            logger.info(f"运行增量更新，时间区间: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}，数据源: {sources or '所有'}")
            stats = service.run_incremental(days=args.days, sources=sources, start_date=start_date, end_date=end_date)
        elif start_date and not end_date:
            # 如果只提供了开始日期，结束日期将是当前日期
            end_date = datetime.now()
            logger.info(f"运行增量更新，时间区间: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}，数据源: {sources or '所有'}")
            stats = service.run_incremental(days=args.days, sources=sources, start_date=start_date, end_date=end_date)
        elif not start_date and end_date:
            # 如果只提供了结束日期，开始日期将是结束日期减去days天
            start_date = end_date - timedelta(days=args.days)
            logger.info(f"运行增量更新，时间区间: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}，数据源: {sources or '所有'}")
            stats = service.run_incremental(days=args.days, sources=sources, start_date=start_date, end_date=end_date)
        else:
            # 否则使用days参数
            logger.info(f"运行增量更新，最近 {args.days} 天，数据源: {sources or '所有'}")
            stats = service.run_incremental(days=args.days, sources=sources)
    
    # 打印结果
    total = sum(stats.values())
    logger.info(f"采集完成: 共 {total} 个漏洞，其中 {stats}")

    # --- 添加 POC 收集流程 --- 
    if args.collect_poc:
        logger.info("开始执行 POC 收集流程...")
        
        # 直接从数据库获取最近采集的CVE，而不是基于日期范围
        # 这将获取刚刚采集完成并保存到数据库中的CVE
        
        # 获取最近采集并保存到数据库的CVE ID
        cve_list_to_process = get_latest_collected_cves(args.db_url)
        
        if not cve_list_to_process:
            logger.info("未找到需要处理的 CVE，跳过 POC 收集。")
        else:
            logger.info(f"准备为 {len(cve_list_to_process)} 个最新采集的 CVE 收集 POC: {cve_list_to_process[:10]}... (最多显示10个)")
            
            poc_service = None # 确保在 finally 中可以检查
            try:
                # 实例化 POC 收集服务 (传递 main 的 args)
                poc_service = PocCollectionService(args)
                
                poc_success_count = 0
                poc_failure_count = 0
                
                # 循环处理 CVE
                for i, cve_id in enumerate(cve_list_to_process, 1):
                    logger.info(f"--- 开始处理第 {i}/{len(cve_list_to_process)} 个 CVE: {cve_id} ---")
                    if poc_service.process_cve(cve_id):
                        poc_success_count += 1
                    else:
                        poc_failure_count += 1
                
                # 报告 POC 收集结果
                logger.info("POC 收集流程完成。")
                logger.info(f"成功处理 {poc_success_count} 个 CVE 的 POC。")
                logger.info(f"处理失败 {poc_failure_count} 个 CVE 的 POC。")
                
            except Exception as e:
                 logger.error(f"POC 收集流程中发生严重错误: {e}", exc_info=True)
            finally:
                # 关闭 Neo4j 连接（如果已打开）
                if poc_service:
                     poc_service.close_neo4j_driver()
    # -------------------------

def run_analyze(args, logger):
    """运行数据分析"""
    import json
    import os
    from datetime import datetime
    
    # 确保输出目录存在
    os.makedirs(args.output, exist_ok=True)
    
    # 创建分析服务实例
    service = AnalyzerService(
        db_url=args.db_url,
        graph_url=args.graph_url,
        graph_user=args.graph_user,
        graph_password=args.graph_password
    )
    
    # 生成输出文件名
    now = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 根据参数决定分析类型
    if args.vuln_id:
        # 漏洞影响分析
        logger.info(f"正在分析漏洞 {args.vuln_id} 的影响范围...")
        result = service.analyze_vulnerability_impact(args.vuln_id)
        output_file = os.path.join(args.output, f"vuln_impact_{args.vuln_id}_{now}.json")
        
        if 'error' in result:
            logger.error(f"分析失败: {result['error']}")
        else:
            # 保存结果到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"漏洞影响分析完成，结果已保存到: {output_file}")
            logger.info(f"直接影响组件数: {len(result.get('direct_affected', []))}")
            logger.info(f"间接影响组件数: {len(result.get('indirect_affected', []))}")
    
    elif args.component:
        # 组件风险评估
        logger.info(f"正在评估组件 {args.component} 的风险...")
        result = service.assess_risk(args.component)
        output_file = os.path.join(args.output, f"component_risk_{args.component}_{now}.json")
        
        # 保存结果到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"组件风险评估完成，结果已保存到: {output_file}")
        logger.info(f"风险等级: {result.get('risk_level', 'unknown')}")
        logger.info(f"风险得分: {result.get('risk_score', 0)}")
        logger.info(f"活跃漏洞数: {result.get('active_vulnerabilities', 0)}")
        logger.info(f"已修复漏洞数: {result.get('fixed_vulnerabilities', 0)}")
        logger.info(f"建议: {result.get('recommendation', '')}")
    
    elif args.start_component and args.end_component:
        # 漏洞传播路径分析
        logger.info(f"正在分析从 {args.start_component} 到 {args.end_component} 的漏洞传播路径...")
        result = service.find_vulnerability_paths(args.start_component, args.end_component)
        output_file = os.path.join(args.output, f"vuln_paths_{args.start_component}_to_{args.end_component}_{now}.json")
        
        if 'error' in result:
            logger.error(f"分析失败: {result['error']}")
        else:
            # 保存结果到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"漏洞传播路径分析完成，结果已保存到: {output_file}")
            logger.info(f"找到 {result.get('count', 0)} 条传播路径")
    
    else:
        # 通用统计分析
        logger.info("正在生成漏洞统计分析...")
        result = service.analyze_statistics()
        output_file = os.path.join(args.output, f"vulnerability_statistics_{now}.json")
        
        # 保存结果到文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"统计分析完成，结果已保存到: {output_file}")
        logger.info(f"总漏洞数: {result.get('total_vulnerabilities', 0)}")
        
        # 显示严重程度分布
        severity_dist = result.get('severity_distribution', {})
        if severity_dist:
            logger.info("严重程度分布:")
            for severity, count in severity_dist.items():
                logger.info(f"  {severity}: {count}")
        
        # 显示生态系统分布
        ecosystem_dist = result.get('ecosystem_distribution', {})
        if ecosystem_dist:
            logger.info("生态系统分布:")
            for eco, count in sorted(ecosystem_dist.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.info(f"  {eco}: {count}")
        
        # 显示最常受影响的包
        top_packages = result.get('top_affected_packages', [])
        if top_packages:
            logger.info("最常受影响的包:")
            for pkg in top_packages[:5]:
                logger.info(f"  {pkg['name']}: {pkg['count']}")

def run_export(args, logger):
    """运行数据导出"""
    # 创建导出服务
    service = ExporterService(
        db_url=args.db_url,
        output_dir=args.output
    )
    
    # 确保输出目录存在
    os.makedirs(args.output, exist_ok=True)
    
    # 处理日期参数
    start_date = None
    end_date = None
    
    if args.start_date:
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
    elif args.days:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=args.days)
        
    if args.end_date:
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
    
    # 生成输出文件名
    timestamp = datetime.now().strftime('%Y%m%d')
    filename = f"vulnerabilities_{timestamp}.{args.format}"
    output_path = os.path.join(args.output, filename)
    
    logger.info(f"正在导出漏洞数据，格式: {args.format}")
    
    # 根据格式选择导出方法
    if args.format == 'json':
        output_file = service.export_json(
            start_date=start_date,
            end_date=end_date,
            source=args.source,
            output_file=output_path
        )
    elif args.format == 'csv':
        output_file = service.export_csv(
            start_date=start_date,
            end_date=end_date,
            source=args.source,
            output_file=output_path
        )
    elif args.format == 'xlsx':
        output_file = service.export_excel(
            start_date=start_date,
            end_date=end_date,
            source=args.source,
            output_file=output_path
        )
    elif args.format == 'html':
        template = args.template or "report.html"
        output_file = service.export_html(
            start_date=start_date,
            end_date=end_date,
            source=args.source,
            template=template,
            output_file=output_path
        )
    elif args.format == 'markdown':
        template = args.template or "report.md"
        output_file = service.export_markdown(
            start_date=start_date,
            end_date=end_date,
            source=args.source,
            template=template,
            output_file=output_path
        )
    
    logger.info(f"数据导出完成，文件已保存到: {output_file}")

def run_graph_import(args, logger):
    """运行图数据导入"""
    # 创建图数据库连接
    graph_store = GraphStore(
        uri=args.graph_url,
        username=args.graph_user,
        password=args.graph_password,
        test_mode=args.test_mode
    )
    
    # 创建图数据导入服务
    importer = GraphImporter(
        db_url=args.db_url,
        graph_store=graph_store,
        batch_size=args.batch_size
    )
    
    # 执行导入
    try:
        if args.source:
            # 按数据源导入
            logger.info(f"正在按数据源 '{args.source}' 导入漏洞数据到图数据库...")
            result = importer.import_vulnerabilities_by_source(args.source)
        elif args.start_date and args.end_date:
            # 按日期范围导入
            start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
            end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
            logger.info(f"正在导入 {args.start_date} 至 {args.end_date} 期间的漏洞数据到图数据库...")
            result = importer.import_vulnerabilities_by_date_range(start_date, end_date)
        else:
            # 导入所有数据
            logger.info("正在导入所有漏洞数据到图数据库...")
            result = importer.import_all_vulnerabilities()
        
        # 处理结果
        if "error" in result:
            logger.error(f"导入失败: {result['error']}")
        else:
            logger.info("导入完成，统计信息:")
            for key, value in result.items():
                logger.info(f"  {key}: {value}")
    except Exception as e:
        logger.error(f"导入过程中发生错误: {str(e)}")
    finally:
        # 关闭连接
        importer.close()
        graph_store.close()

def run_test_poc(args, logger):
    """测试POC搜索子命令"""
    logger.info(f"开始测试POC搜索功能，指定的CVE ID: {args.cve_ids}")
    
    poc_service = None
    try:
        # 实例化POC收集服务
        poc_service = PocCollectionService(args)
        
        poc_success_count = 0
        poc_failure_count = 0
        
        # 循环处理指定的CVE ID
        for i, cve_id in enumerate(args.cve_ids, 1):
            logger.info(f"--- 开始处理第 {i}/{len(args.cve_ids)} 个CVE: {cve_id} ---")
            if poc_service.process_cve(cve_id):
                poc_success_count += 1
            else:
                poc_failure_count += 1
        
        # 报告POC收集结果
        logger.info("POC搜索测试完成")
        logger.info(f"成功处理 {poc_success_count} 个CVE的POC")
        logger.info(f"处理失败 {poc_failure_count} 个CVE的POC")
        
    except Exception as e:
        logger.error(f"POC搜索测试过程中发生严重错误: {e}", exc_info=True)
    finally:
        # 关闭Neo4j连接（如果已打开）
        if poc_service:
            poc_service.close_neo4j_driver()

def run_patch_trace(args, logger):
    """补丁跟踪子命令"""
    logger.info("开始执行补丁跟踪功能")
    
    try:
        # 创建数据目录
        data_dir = args.data_dir
        os.makedirs(data_dir, exist_ok=True)
        
        # 尝试从配置文件加载GitHub令牌
        github_token = args.github_token
        if not github_token:
            # 如果没有指定配置文件路径，尝试自动查找
            config_paths = [
                args.config_path,  # 首先检查用户指定的路径
                args.config,  # 全局配置路径
                os.path.join(project_root, "src/data_collection/config/sources.json"),
                os.path.join(project_root, "config/sources.json"),
                "/home/<USER>/src/data_collection/config/sources.json"
            ]
            
            # 尝试每个可能的配置文件路径
            for config_path in config_paths:
                if config_path and os.path.exists(config_path):
                    try:
                        import json
                        with open(config_path, 'r') as f:
                            config = json.load(f)
                            if 'sources' in config and 'GitHub' in config['sources']:
                                github_token = config['sources']['GitHub'].get('api_key')
                                if github_token:
                                    logger.info(f"从配置文件 {config_path} 加载GitHub令牌: {github_token[:4]}...")
                                    break
                    except Exception as e:
                        logger.error(f"加载配置文件 {config_path} 失败: {str(e)}")
                        continue
        
        # 根据模式选择执行逻辑
        if args.cve:
            # 单个CVE模式
            cve_id = args.cve.upper()
            
            # 验证CVE ID格式
            if not re.match(r'^CVE-\d{4}-\d{4,}$', cve_id):
                logger.error(f"无效的CVE ID格式: {cve_id}，示例：CVE-2021-44832")
                return 1
                
            # 跟踪单个CVE
            trace_single_cve(cve_id, data_dir, args.depth, args, github_token, logger)
        else:
            # 时间范围模式
            try:
                start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
                
                if args.end_date:
                    end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
                else:
                    # 如果只指定了开始日期，使用今天作为结束日期
                    end_date = datetime.now()
                    logger.info(f"未指定结束日期，使用今天: {end_date.strftime('%Y-%m-%d')}")
                
                # 检查日期是否有效
                if start_date > end_date:
                    logger.error("开始日期不能晚于结束日期")
                    return 1
                    
                # 创建时间范围子目录
                timerange_dir = os.path.join(
                    data_dir, 
                    f"timerange_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
                )
                os.makedirs(timerange_dir, exist_ok=True)
                
                # 跟踪时间范围内的CVE
                trace_timerange(start_date, end_date, timerange_dir, args.depth, args, github_token, logger)
                
            except ValueError as e:
                logger.error(f"日期格式错误，请使用YYYY-MM-DD格式: {str(e)}")
                return 1
        
        return 0
    except Exception as e:
        logger.error(f"补丁跟踪过程中发生错误: {str(e)}", exc_info=True)
        return 1

def trace_single_cve(cve_id, data_dir, depth, args, github_token, logger):
    """对单个CVE进行补丁跟踪"""
    # 获取引用链接
    logger.info(f"获取 {cve_id} 的初始引用链接")
    references_by_source = get_initial_references(
        cve_id=cve_id,
        data_dir=data_dir,
        config_path=args.config_path or args.config,
        use_cache=args.use_cache
    )
    
    # 打印各来源引用数量
    for source, refs in references_by_source.items():
        logger.info(f"{source} 引用数量: {len(refs)}")
    
    # 准备引用链接
    initial_references, source_mapping = prepare_references_for_tracer(references_by_source)
    
    # 应用优先级排序机制（除非用户选择不使用）
    if not args.no_priority:
        logger.info(f"对初始引用链接应用优先级排序（最低优先级: {args.min_priority}）...")
        initial_references, source_mapping = prioritize_initial_references(
            initial_references, 
            source_mapping,
            min_priority=args.min_priority
        )
        logger.info(f"优先级排序后选择了 {len(initial_references)} 个引用进行处理")
    
    # 初始化补丁跟踪器
    tracer = PatchTracer(
        output_dir=data_dir,
        max_depth=depth,
        request_delay=args.delay,
        github_token=github_token,  # 使用从配置文件或命令行获取的令牌
        confidence_threshold=args.confidence,
        verify_ssl=not args.no_verify_ssl,
        max_retries=args.retries
    )
    
    # 执行补丁跟踪
    logger.info(f"开始跟踪 {cve_id} 的补丁...")
    if args.language:
        logger.info(f"启用语言筛选: {args.language}")
    result = tracer.trace(
        cve_id=cve_id,
        initial_references=initial_references,
        sources=source_mapping,
        language=args.language,
        skip_url_check=args.skip_url_check
    )
    
    # 打印结果摘要
    print(f"\n=== {cve_id} 补丁跟踪结果摘要 ===")
    print(f"节点总数: {result['network_stats']['total_nodes']}")
    print(f"边总数: {result['network_stats']['total_edges']}")
    print(f"问题节点数: {result['network_stats']['issue_nodes']}")
    print(f"补丁节点数: {result['network_stats']['patch_nodes']}")
    print(f"发现潜在补丁数: {len(result['all_patches'])}")
    print(f"高置信度补丁数: {len(result['high_confidence_patches'])}")
    
    if result['high_confidence_patches']:
        print("\n高置信度补丁:")
        for i, patch in enumerate(result['high_confidence_patches'], 1):
            confidence = patch.get('confidence', 0)
            url = patch.get('url', '')
            print(f"{i}. [置信度: {confidence:.2f}] {url}")
    else:
        print("\n未找到高置信度补丁")
        
    print(f"\n结果保存在: {data_dir}/{cve_id.replace('-', '_')}/")
    
    return result

def trace_timerange(start_date, end_date, data_dir, depth, args, github_token, logger):
    """对指定时间范围内的CVE进行批量补丁跟踪"""
    logger.info(f"准备扫描 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 期间的CVE")
    
    # 初始化CVE引用信息获取器
    config_path = args.config_path or args.config
    if not config_path:
        # 如果未指定配置文件路径，尝试自动查找
        possible_paths = [
            os.path.join(project_root, "src/data_collection/config/sources.json"),
            os.path.join(project_root, "config/sources.json"),
            "/home/<USER>/src/data_collection/config/sources.json"
        ]
        
        for path in possible_paths:
            if path and os.path.exists(path):
                config_path = path
                logger.info(f"找到配置文件: {path}")
                break
    
    fetcher = CVEReferenceFetcher(config_path)
    
    # 获取时间范围内的CVE
    cve_dir = os.path.join(data_dir, "cve_data")
    os.makedirs(cve_dir, exist_ok=True)
    
    logger.info(f"正在获取时间范围内的CVE数据...")
    cve_list = fetcher.get_references_by_timerange(
        start_date=start_date,
        end_date=end_date,
        date_type=args.date_type,
        save_individual=True,
        output_dir=cve_dir
    )
    
    if not cve_list:
        logger.warning(f"在指定时间范围内未找到CVE，跳过跟踪")
        return {"cve_count": 0, "results": {}}
    
    logger.info(f"找到 {len(cve_list)} 个CVE，准备跟踪...")
    
    # 跟踪结果汇总
    summary = {
        "time_range": {
            "start": start_date.strftime('%Y-%m-%d'),
            "end": end_date.strftime('%Y-%m-%d'),
            "date_type": args.date_type
        },
        "cve_count": len(cve_list),
        "traced_count": 0,
        "success_count": 0,
        "has_patches_count": 0,
        "high_confidence_patches_count": 0,
        "results": {}
    }
    
    # 跟踪范围限制
    if args.max_cves and args.max_cves < len(cve_list):
        logger.info(f"由于设置了最大CVE数量限制({args.max_cves})，将只跟踪部分CVE")
        cve_list = cve_list[:args.max_cves]
        summary["cve_count"] = len(cve_list)
    
    # 按照发布时间排序，先处理最新的
    if args.newest_first:
        cve_list.sort(key=lambda x: x.get('published_date', ''), reverse=True)
        logger.info("按发布时间从新到旧排序")
    
    # 遍历CVE进行跟踪
    for i, cve_info in enumerate(cve_list, 1):
        cve_id = cve_info.get('cve_id')
        if not cve_id:
            logger.warning(f"第 {i} 个CVE没有ID，跳过")
            continue
        
        try:
            logger.info(f"[{i}/{len(cve_list)}] 开始跟踪 {cve_id}")
            
            # 为每个CVE创建单独的子目录
            cve_output_dir = os.path.join(data_dir, cve_id.replace('-', '_'))
            os.makedirs(cve_output_dir, exist_ok=True)
            
            # 跟踪这个CVE
            result = trace_single_cve(
                cve_id=cve_id, 
                data_dir=data_dir, 
                depth=depth, 
                args=args,
                github_token=github_token,
                logger=logger
            )
            
            # 更新统计信息
            summary["traced_count"] += 1
            summary["success_count"] += 1
            
            # 统计有补丁的CVE数量
            has_patches = len(result.get('all_patches', [])) > 0
            has_high_confidence = len(result.get('high_confidence_patches', [])) > 0
            
            if has_patches:
                summary["has_patches_count"] += 1
                
            if has_high_confidence:
                summary["high_confidence_patches_count"] += 1
                
            # 记录详细结果
            summary["results"][cve_id] = {
                "status": "success",
                "has_patches": has_patches,
                "has_high_confidence_patches": has_high_confidence,
                "patch_count": len(result.get('all_patches', [])),
                "high_confidence_patch_count": len(result.get('high_confidence_patches', []))
            }
            
            # 保存每个CVE的高置信度补丁
            if has_high_confidence:
                summary["results"][cve_id]["high_confidence_patches"] = [
                    {"url": p.get('url', ''), "confidence": p.get('confidence', 0)}
                    for p in result.get('high_confidence_patches', [])
                ]
            
            # 每处理10个CVE，输出一次进度报告
            if i % 10 == 0 or i == len(cve_list):
                logger.info(f"已完成 {i}/{len(cve_list)} 个CVE的跟踪")
                logger.info(f"发现补丁的CVE: {summary['has_patches_count']}, 有高置信度补丁的CVE: {summary['high_confidence_patches_count']}")
                
                # 保存临时汇总报告
                summary_file = os.path.join(data_dir, "timerange_summary.json")
                import json
                with open(summary_file, 'w', encoding='utf-8') as f:
                    json.dump(summary, f, indent=2, ensure_ascii=False)
                logger.info(f"已保存临时汇总报告到: {summary_file}")
                
        except Exception as e:
            logger.error(f"跟踪 {cve_id} 时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # 记录错误信息
            summary["results"][cve_id] = {
                "status": "error",
                "error": str(e)
            }
            continue
    
    # 保存最终汇总报告
    summary_file = os.path.join(data_dir, "timerange_summary.json")
    import json
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    logger.info(f"已保存最终汇总报告到: {summary_file}")
    
    # 输出汇总统计
    print("\n=== 时间范围跟踪结果汇总 ===")
    print(f"时间范围: {summary['time_range']['start']} 到 {summary['time_range']['end']} ({summary['time_range']['date_type']})")
    print(f"总CVE数量: {summary['cve_count']}")
    print(f"成功跟踪数量: {summary['success_count']}")
    print(f"发现补丁的CVE数量: {summary['has_patches_count']}")
    print(f"有高置信度补丁的CVE数量: {summary['high_confidence_patches_count']}")
    print(f"详细结果保存在: {summary_file}")
    
    return summary

def run_cnnvd(args, logger):
    """CNNVD爬虫子命令"""
    logger.info(f"开始执行CNNVD爬虫功能，子命令: {args.cnnvd_command}")
    
    try:
        # 创建CNNVD爬虫实例
        crawler = CNNVDCrawler()
        
        # 设置数据库路径（如果指定）
        if hasattr(args, 'db_path') and args.db_path:
            crawler.database_path = args.db_path
            # 确保数据目录存在
            os.makedirs(os.path.dirname(args.db_path), exist_ok=True)
        
        # 执行相应的子命令
        if args.cnnvd_command == 'crawl':
            # 爬取数据
            logger.info(f"开始爬取CNNVD数据，页面范围: {args.start_page} - {args.end_page}，线程数: {args.threads}")
            
            # 设置线程数
            crawler.Task.threads = args.threads
            
            # 设置请求延迟
            if args.delay:
                crawler.Time_Delay = args.delay
            
            # 使用多线程模式爬取数据
            crawler.get_all(args.start_page, args.end_page)
            
            logger.info(f"爬取完成，成功获取 {crawler.Task.count} 条记录，跳过 {crawler.Task.skipped_count} 条重复记录")
            
        elif args.cnnvd_command == 'search':
            # 搜索数据
            logger.info(f"开始搜索CNNVD数据，关键词: {args.keyword}")
            
            # 连接数据库
            conn, cursor = crawler.connect_database()
            
            try:
                # 构建SQL语句
                sql = f"SELECT cnnvd_id, vulName, cve_id, vulType, hazardLevel, updateTime FROM {crawler.tablename} WHERE vulName LIKE ? OR cnnvd_id LIKE ? OR cve_id LIKE ?"
                params = [f"%{args.keyword}%", f"%{args.keyword}%", f"%{args.keyword}%"]
                
                # 添加危害等级筛选
                if args.hazard_level:
                    sql += " AND hazardLevel = ?"
                    params.append(args.hazard_level)
                
                # 执行查询
                cursor.execute(sql, params)
                results = cursor.fetchall()
                
                # 输出结果
                if results:
                    logger.info(f"找到 {len(results)} 条匹配记录:")
                    print("\n搜索结果:")
                    print("-" * 80)
                    for i, result in enumerate(results, 1):
                        print(f"{i}. {result[0]} | {result[1]}")
                        print(f"   CVE: {result[2] or '无'} | 类型: {result[3] or '未知'} | 危害等级: {result[4] or '未知'}")
                        print(f"   更新时间: {result[5]}")
                        print("-" * 80)
                else:
                    logger.info("未找到匹配记录")
                    print("\n未找到匹配记录")
            finally:
                # 关闭数据库连接
                crawler.close_database(conn, cursor)
                
        elif args.cnnvd_command == 'export':
            # 导出数据
            logger.info(f"开始导出CNNVD数据到: {args.output}，格式: {args.format}")
            
            # 连接数据库
            conn, cursor = crawler.connect_database()
            
            try:
                # 构建SQL语句
                sql = f"SELECT * FROM {crawler.tablename}"
                if args.limit > 0:
                    sql += f" LIMIT {args.limit}"
                
                # 执行查询
                cursor.execute(sql)
                results = cursor.fetchall()
                
                # 获取列名
                cursor.execute(f"PRAGMA table_info({crawler.tablename})")
                columns = [column[1] for column in cursor.fetchall()]
                
                # 导出数据
                if args.format == 'json':
                    import json
                    # 构建JSON格式数据
                    data = []
                    for row in results:
                        item = {}
                        for i, column in enumerate(columns):
                            item[column] = row[i]
                        data.append(item)
                    
                    # 保存到文件
                    with open(args.output, 'w', encoding='utf-8') as f:
                        json.dump(data, f, indent=2, ensure_ascii=False)
                    
                elif args.format == 'csv':
                    import csv
                    # 保存到CSV文件
                    with open(args.output, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        # 写入表头
                        writer.writerow(columns)
                        # 写入数据
                        writer.writerows(results)
                
                logger.info(f"成功导出 {len(results)} 条记录")
                print(f"\n成功导出 {len(results)} 条记录到 {args.output}")
                
            finally:
                # 关闭数据库连接
                crawler.close_database(conn, cursor)
        
        else:
            logger.error(f"未知的CNNVD子命令: {args.cnnvd_command}")
            return 1
        
        return 0
        
    except Exception as e:
        logger.error(f"CNNVD爬虫执行过程中发生错误: {str(e)}", exc_info=True)
        return 1

def run_cnvd(args, logger):
    """CNVD爬虫子命令"""
    logger.info(f"开始执行CNVD爬虫功能")
    
    try:
        # 执行CNVD爬虫
        logger.info(f"开始爬取CNVD数据，页面范围: {args.start_page} - {args.end_page}，延迟: {args.sleep_time}秒")
        
        # 运行CNVD爬虫
        result = run_cnvd_crawler(
            start_page=args.start_page,
            end_page=args.end_page,
            sleep_time=args.sleep_time,
            db_path=args.db_path
        )
        
        # 处理结果
        if result.get("success", False):
            logger.info(f"CNVD爬虫执行成功，爬取了 {result.get('vulnerabilities', 0)} 条漏洞数据")
            print(f"\n爬取完成，共获取 {result.get('vulnerabilities', 0)} 条CNVD漏洞记录")
        else:
            logger.error(f"CNVD爬虫执行失败: {result.get('message', '')}")
            logger.error(f"错误详情: {result.get('error', '未知错误')}")
            print(f"\n爬取失败: {result.get('message', '')}")
            return 1
        
        return 0
    except Exception as e:
        logger.error(f"CNVD爬虫执行过程中发生错误: {str(e)}", exc_info=True)
        return 1

def run_risk_assessment(args, logger):
    """运行漏洞风险评估"""
    import json  # 添加json模块导入
    logger.info(f"开始评估漏洞 {args.cve} 的风险")
    
    # 确保输出目录存在
    os.makedirs(args.output, exist_ok=True)
    
    # 如果指定了静默警告，调整日志级别
    if args.quiet_warnings:
        # 临时调整Neo4j和SQLite相关模块的日志级别
        logging.getLogger('neo4j').setLevel(logging.ERROR)
        logging.getLogger('src.data_collection.services.graph_store').setLevel(logging.INFO)
        
        # 创建一个过滤器，过滤掉包含特定字符串的日志
        class MessageFilter(logging.Filter):
            def filter(self, record):
                # 过滤掉包含以下字符串的日志消息
                filter_strings = [
                    "Received notification from DBMS server", 
                    "查询POC数据库时出错", 
                    "no such table", 
                    "Neo4j数据库中不存在", 
                    "The provided relationship type"
                ]
                for s in filter_strings:
                    if s in record.getMessage():
                        return False
                return True
        
        # 将过滤器应用到根日志记录器
        root_logger = logging.getLogger()
        root_logger.addFilter(MessageFilter())
        
        logger.debug("已启用静默警告模式，将隐藏非关键错误和警告消息")
    
    # 创建风险评估服务
    service = RiskAssessmentService(
        config_path=args.config,
        graph_url=args.graph_url,
        graph_user=args.graph_user,
        graph_password=args.graph_password,
        poc_db_path=args.poc_db_path,
        verify_ssl=not args.no_verify_ssl
    )
    
    try:
        # 评估CVE风险
        result = service.assess_cve_risk(args.cve)
        
        # 生成输出文件名
        now = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(args.output, f"{args.cve}_risk_assessment_{now}.json")
        
        # 保存评估结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 输出结果摘要
        logger.info(f"风险评估完成，结果已保存到: {output_file}")
        logger.info(f"风险等级: {result.get('risk_level', '未知')}")
        logger.info(f"风险评分: {result.get('risk_score', '未知')}")
        logger.info(f"CVSS分数: {result.get('cvss_score', '未知')}")
        logger.info(f"EPSS分数: {result.get('epss_score', '未知')}")
        logger.info(f"存在POC: {result.get('has_poc', False)}")
        logger.info(f"处置建议: {result.get('recommendation', '无')}")
        
    except Exception as e:
        logger.error(f"风险评估过程中发生错误: {str(e)}")
        import traceback  # 添加错误堆栈跟踪
        logger.error(traceback.format_exc())
    finally:
        # 关闭资源
        service.close()
        
        # 如果使用了过滤器，恢复原始设置
        if args.quiet_warnings:
            # 移除我们添加的过滤器
            root_logger = logging.getLogger()
            for f in root_logger.filters[:]:
                if isinstance(f, MessageFilter):
                    root_logger.removeFilter(f)
            
            # 恢复日志级别
            logging.getLogger('neo4j').setLevel(logging.WARNING)
            logging.getLogger('src.data_collection.services.graph_store').setLevel(logging.WARNING)

def main():
    """主函数"""
    # 设置路径
    setup_paths()
    
    # 解析命令行参数
    args = parse_args()
    
    # 设置日志配置
    global logger
    logger = setup_logging(args)
    
    # 确保数据目录存在
    os.makedirs("data", exist_ok=True)
    
    # 根据命令执行相应功能
    if args.command == 'collect':
        run_collect(args, logger)
    elif args.command == 'analyze':
        run_analyze(args, logger)
    elif args.command == 'export':
        run_export(args, logger)
    elif args.command == 'graph-import':
        run_graph_import(args, logger)
    elif args.command == 'test-poc':
        run_test_poc(args, logger)
    elif args.command == 'patch-trace':
        run_patch_trace(args, logger)
    elif args.command == 'cnnvd':
        run_cnnvd(args, logger)
    elif args.command == 'cnvd':
        run_cnvd(args, logger)
    elif args.command == 'assess-risk':
        run_risk_assessment(args, logger)
    else:
        logger.error("未知命令，请使用 --help 查看帮助")
        return 1
    
    return 0

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n程序发生错误: {str(e)}")
        sys.exit(1) 