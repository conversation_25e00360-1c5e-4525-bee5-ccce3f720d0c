"""
全局配置文件
"""

import os
import json
from pathlib import Path

# 项目根目录
ROOT_DIR = Path(__file__).parent.parent

# 数据目录
DATA_DIR = ROOT_DIR / 'data'
RAW_DATA_DIR = DATA_DIR / 'raw'
PROCESSED_DATA_DIR = DATA_DIR / 'processed'

# 日志目录
LOG_DIR = ROOT_DIR / 'logs'

# 确保目录存在
for dir_path in [RAW_DATA_DIR, PROCESSED_DATA_DIR, LOG_DIR]:
    dir_path.mkdir(parents=True, exist_ok=True)

# 数据源配置
def load_sources_config():
    config_file = ROOT_DIR / 'config' / 'sources.json'
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {}

# 日志配置
def load_logging_config():
    config_file = ROOT_DIR / 'config' / 'logging.json'
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'standard': {
                'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
            },
        },
        'handlers': {
            'default': {
                'level': 'INFO',
                'formatter': 'standard',
                'class': 'logging.StreamHandler',
            },
            'file': {
                'level': 'INFO',
                'formatter': 'standard',
                'class': 'logging.FileHandler',
                'filename': str(LOG_DIR / 'app.log'),
                'mode': 'a',
            },
        },
        'loggers': {
            '': {
                'handlers': ['default', 'file'],
                'level': 'INFO',
                'propagate': True
            }
        }
    }

# 加载配置
SOURCES_CONFIG = load_sources_config()
LOGGING_CONFIG = load_logging_config()

# Neo4j配置
def load_neo4j_config():
    """加载Neo4j配置"""
    config_file = ROOT_DIR / 'config' / 'neo4j_config.json'
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {
        'neo4j': {
            'host': 'bolt://localhost:7687',
            'username': 'neo4j',
            'password': 'password'
        }
    }

# 加载Neo4j配置
NEO4J_CONFIG = load_neo4j_config() 