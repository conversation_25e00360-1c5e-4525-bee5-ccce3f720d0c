#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""数据库相关工具函数 (用于数据收集)"""

import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List
from urllib.parse import urlparse, unquote
import os # 添加 os 模块用于处理路径
from pathlib import Path

# 从项目日志配置中导入logger
from ..utils.logger import logger

def get_cves_in_daterange(db_url: str, start_date: datetime, end_date: datetime) -> List[str]:
    """
    从主漏洞数据库查询指定日期范围内发布或修改的 CVE ID 列表。

    参数:
        db_url: 数据库连接 URL (目前仅支持 sqlite)
        start_date: 开始日期 (包含)
        end_date: 结束日期 (包含)

    返回:
        CVE ID 列表
    """
    cve_list = []
    conn = None
    parsed_url = urlparse(db_url)
    if parsed_url.scheme != 'sqlite':
        logger.error(f"目前 get_cves_in_daterange 仅支持 sqlite 数据库 URL，收到: {db_url}")
        return []
    
    # --- 修正路径解析逻辑，更健壮地处理 sqlite:////... --- 
    db_path = unquote(parsed_url.path) 
    # For sqlite:////abs/path, path becomes //abs/path
    # For sqlite:///rel/path, path becomes /rel/path
    # For sqlite://rel/path (less common), path becomes //rel/path
    if db_path.startswith('//'):
        # This handles sqlite:////abs/path and sqlite://rel/path
        # --- 修正: 对于 //// 开头的绝对路径，只移除一个 / --- 
        # Remove the leading '//' to get the actual path
        # db_path = db_path[2:] 
        db_path = db_path[1:] # Correct: Keep one leading / for absolute paths
    elif db_path.startswith('/'):
        # This handles sqlite:///rel/path (relative to root, unusual) 
        # or potentially sqlite:///abs/path if authority was omitted
        # Assume it's intended as an absolute path if it starts with /
        pass # Keep the leading /
    # else: path doesn't start with /, likely relative path without leading /

    # Ensure it's an absolute path if it looks like one, otherwise resolve relative to CWD
    # However, assuming URLs passed are generally absolute for simplicity here
    if not os.path.isabs(db_path) and db_path.startswith('/'):
         logger.warning(f"Interpreting path starting with / but not absolute as absolute: {db_path}")
         # This case might need adjustment based on expected URL formats

    # 之前的复杂分平台逻辑可以简化，因为 unquote(parsed_url.path) 已经处理了大部分情况
    # if os.name == 'nt': ... 
    # else: ...
    # --- 结束修正 --- 

    logger.info(f"从数据库 {db_path} 查询日期范围 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的 CVE")

    try:
        # --- 添加打印语句，确认实际使用的路径 ---
        logger.debug(f"Attempting to connect to SQLite DB at resolved path: [{db_path}]")
        print(f"DEBUG: Attempting to connect to SQLite DB at resolved path: [{db_path}]") # 直接输出到控制台以防日志级别问题
        # --- 结束添加 ---
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        # 假设表名为 'vulnerabilities'
        query = """
            SELECT DISTINCT vuln_id 
            FROM vulnerabilities 
            WHERE date(last_modified_date) >= date(?) 
              AND date(last_modified_date) <= date(?)
        """
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        cursor.execute(query, (start_date_str, end_date_str))
        rows = cursor.fetchall()
        cve_list = [row[0] for row in rows if row[0]]
        logger.info(f"查询到 {len(cve_list)} 个在此日期范围内的 CVE ID")
    except sqlite3.OperationalError as e:
        logger.error(f"查询数据库 {db_path} 时出错: {e} - 表或列可能不存在或名称错误。")
    except sqlite3.Error as e:
        logger.error(f"查询数据库 {db_path} 时出错: {e}")
    except Exception as e:
         logger.error(f"处理数据库查询时发生未知错误: {e}")
    finally:
        if conn:
            conn.close()
    return cve_list 

def get_latest_collected_cves(db_url: str, time_window_minutes: int = 10) -> List[str]:
    """
    获取最近一段时间内采集或更新的CVE ID列表
    
    Args:
        db_url: 数据库连接URL
        time_window_minutes: 时间窗口（分钟），默认10分钟，表示获取最近多少分钟内更新的CVE
        
    Returns:
        CVE ID列表
    """
    logger.info(f"获取最近 {time_window_minutes} 分钟内采集或更新的CVE ID列表")
    
    # 解析SQLite数据库路径
    db_path = ""
    if db_url.startswith('sqlite:///'):
        db_path = db_url[10:]
    elif db_url.startswith('sqlite:////'):
        db_path = db_url[11:]
    else:
        logger.error(f"不支持的数据库URL格式: {db_url}")
        return []
    
    # 确保路径存在
    if not db_path or not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return []
    
    # 计算时间窗口
    now = datetime.now()
    cutoff_time = now - timedelta(minutes=time_window_minutes)
    cutoff_time_str = cutoff_time.strftime('%Y-%m-%d %H:%M:%S')
    
    cve_list = []
    conn = None
    
    try:
        logger.debug(f"连接到SQLite数据库: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询最近更新的CVE ID
        query = """
            SELECT DISTINCT vuln_id 
            FROM vulnerabilities 
            WHERE datetime(last_modified_date) >= datetime(?)
            OR datetime(published_date) >= datetime(?)
        """
        
        cursor.execute(query, (cutoff_time_str, cutoff_time_str))
        rows = cursor.fetchall()
        
        # 如果没有找到最近更新的CVE，则获取最新添加的100个CVE
        if not rows:
            logger.info(f"在过去 {time_window_minutes} 分钟内没有更新的CVE，将获取最新添加的100个CVE")
            query = """
                SELECT DISTINCT vuln_id 
                FROM vulnerabilities 
                ORDER BY id DESC 
                LIMIT 100
            """
            cursor.execute(query)
            rows = cursor.fetchall()
        
        cve_list = [row[0] for row in rows if row[0]]
        logger.info(f"查询到 {len(cve_list)} 个最近的CVE ID")
        
    except sqlite3.Error as e:
        logger.error(f"查询数据库时出错: {e}")
    except Exception as e:
        logger.error(f"处理最近CVE查询时出错: {e}")
    finally:
        if conn:
            conn.close()
    
    return cve_list 