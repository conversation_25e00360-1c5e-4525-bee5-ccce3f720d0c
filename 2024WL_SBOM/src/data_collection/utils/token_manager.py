#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub Token管理器

支持多个GitHub token的轮询使用，避免API限制
"""

import json
import time
import logging
from typing import List, Optional, Dict, Any
from threading import Lock
import requests

logger = logging.getLogger(__name__)

class GitHubTokenManager:
    """GitHub Token管理器，支持多token轮询"""
    
    def __init__(self, config_path: str):
        """
        初始化Token管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.tokens = []
        self.current_index = 0
        self.lock = Lock()
        self.token_status = {}  # 记录每个token的状态
        self.load_tokens()
    
    def load_tokens(self):
        """从配置文件加载GitHub tokens"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            github_config = config.get('sources', {}).get('GitHub', {})
            
            # 支持单个token和多个tokens
            if 'api_keys' in github_config:
                self.tokens = github_config['api_keys']
            elif 'api_key' in github_config:
                self.tokens = [github_config['api_key']]
            
            if not self.tokens:
                logger.warning("未找到GitHub API tokens")
                return
            
            # 初始化token状态
            for token in self.tokens:
                self.token_status[token] = {
                    'remaining': 5000,  # 默认限制
                    'reset_time': 0,
                    'last_used': 0
                }
            
            logger.info(f"加载了 {len(self.tokens)} 个GitHub tokens")
            
        except Exception as e:
            logger.error(f"加载GitHub tokens失败: {str(e)}")
            self.tokens = []
    
    def get_next_token(self) -> Optional[str]:
        """
        获取下一个可用的token
        
        Returns:
            可用的token，如果没有可用token返回None
        """
        if not self.tokens:
            return None
        
        with self.lock:
            # 检查所有token的状态，选择最佳的
            best_token = None
            best_remaining = -1
            
            for i, token in enumerate(self.tokens):
                status = self.token_status[token]
                current_time = time.time()
                
                # 如果token已重置，更新状态
                if current_time > status['reset_time']:
                    status['remaining'] = 5000
                
                # 选择剩余请求数最多的token
                if status['remaining'] > best_remaining:
                    best_remaining = status['remaining']
                    best_token = token
                    self.current_index = i
            
            if best_token and best_remaining > 0:
                # 更新使用状态
                self.token_status[best_token]['last_used'] = time.time()
                return best_token
            
            return None
    
    def update_token_status(self, token: str, headers: Dict[str, str]):
        """
        根据API响应头更新token状态
        
        Args:
            token: 使用的token
            headers: API响应头
        """
        if token not in self.token_status:
            return
        
        try:
            remaining = int(headers.get('X-RateLimit-Remaining', 0))
            reset_time = int(headers.get('X-RateLimit-Reset', 0))
            
            with self.lock:
                self.token_status[token]['remaining'] = remaining
                self.token_status[token]['reset_time'] = reset_time
                
            logger.debug(f"Token状态更新: 剩余 {remaining} 请求，重置时间 {reset_time}")
            
        except (ValueError, TypeError) as e:
            logger.warning(f"解析API限制头失败: {str(e)}")
    
    def get_token_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有token的状态
        
        Returns:
            token状态字典
        """
        with self.lock:
            return {
                token[:10] + "...": {
                    'remaining': status['remaining'],
                    'reset_time': status['reset_time'],
                    'last_used': status['last_used']
                }
                for token, status in self.token_status.items()
            }
    
    def wait_for_reset(self, token: str) -> bool:
        """
        等待token重置
        
        Args:
            token: 要等待的token
            
        Returns:
            是否成功等待
        """
        if token not in self.token_status:
            return False
        
        status = self.token_status[token]
        current_time = time.time()
        
        if current_time < status['reset_time']:
            wait_time = status['reset_time'] - current_time
            if wait_time > 0 and wait_time < 3600:  # 最多等待1小时
                logger.info(f"等待token重置，剩余时间: {wait_time:.0f}秒")
                time.sleep(wait_time + 1)  # 多等1秒确保重置
                return True
        
        return False
    
    def check_token_validity(self, token: str) -> bool:
        """
        检查token是否有效
        
        Args:
            token: 要检查的token
            
        Returns:
            token是否有效
        """
        try:
            headers = {
                'Authorization': f'Bearer {token}',
                'Accept': 'application/vnd.github.v3+json'
            }
            
            response = requests.get(
                'https://api.github.com/user',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                # 更新token状态
                self.update_token_status(token, response.headers)
                return True
            else:
                logger.warning(f"Token验证失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Token验证出错: {str(e)}")
            return False
    
    def get_valid_tokens_count(self) -> int:
        """
        获取有效token数量
        
        Returns:
            有效token数量
        """
        valid_count = 0
        for token in self.tokens:
            if self.check_token_validity(token):
                valid_count += 1
        return valid_count
