# -*- coding: utf-8 -*-

"""
数据模型模块

包含系统的数据模型定义:
1. entities - 基础实体模型
2. database - 数据库模型和操作
3. nvd - NVD数据模型
4. graph - 图数据库模型
"""

from .entities import Vulnerability, Reference, Package
from .graph import (
    NodeType, RelationType, 
    VulnerabilityNode, ComponentNode, VersionNode, PatchNode, WeaknessNode,
    AffectsRelation, HasVersionRelation, FixedByRelation, DependsOnRelation, HasWeaknessRelation
)

__all__ = [
    'Vulnerability', 
    'Reference', 
    'Package',
    'NodeType',
    'RelationType',
    'VulnerabilityNode',
    'ComponentNode',
    'VersionNode',
    'PatchNode',
    'WeaknessNode',
    'AffectsRelation',
    'HasVersionRelation',
    'FixedByRelation',
    'DependsOnRelation',
    'HasWeaknessRelation'
] 