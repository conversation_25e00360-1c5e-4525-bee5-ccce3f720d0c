#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j图数据库模型模块

定义了图数据库中的节点类型和关系类型:

节点类型:
1. Vulnerability - 漏洞节点
2. Component - 组件节点
3. Version - 版本节点
4. Patch - 补丁节点
5. Weakness - 弱点节点

关系类型:
1. AFFECTS - 漏洞影响组件关系
2. HAS_VERSION - 组件包含版本关系
3. FIXED_BY - 漏洞被补丁修复关系
4. DEPENDS_ON - 组件依赖关系
5. HAS_WEAKNESS - 漏洞与弱点关系
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

class NodeType(Enum):
    """节点类型枚举"""
    VULNERABILITY = "Vulnerability"
    COMPONENT = "Component"
    VERSION = "Version"
    PATCH = "Patch"
    WEAKNESS = "Weakness"

class RelationType(Enum):
    """关系类型枚举"""
    AFFECTS = "AFFECTS"
    HAS_VERSION = "HAS_VERSION"
    FIXED_BY = "FIXED_BY"
    DEPENDS_ON = "DEPENDS_ON"
    HAS_WEAKNESS = "HAS_WEAKNESS"

@dataclass
class GraphNode:
    """图数据库节点基类"""
    id: str
    node_type: NodeType
    properties: Dict[str, Any] = field(default_factory=dict)

@dataclass
class GraphRelation:
    """图数据库关系基类"""
    source_id: str
    target_id: str
    relation_type: RelationType
    properties: Dict[str, Any] = field(default_factory=dict)

@dataclass
class VulnerabilityNode(GraphNode):
    """漏洞节点"""
    def __init__(self, vuln_id: str, **kwargs):
        """
        初始化漏洞节点
        
        Args:
            vuln_id: 漏洞ID
            **kwargs: 其他属性
        """
        super().__init__(
            id=vuln_id,
            node_type=NodeType.VULNERABILITY,
            properties={
                'source': kwargs.get('source'),
                'title': kwargs.get('title'),
                'description': kwargs.get('description'),
                'severity': kwargs.get('severity'),
                'published_date': kwargs.get('published_date'),
                'last_modified_date': kwargs.get('last_modified_date'),
                'cvss_v3_score': kwargs.get('cvss_v3_score'),
                'cvss_v2_score': kwargs.get('cvss_v2_score')
            }
        )

@dataclass
class ComponentNode(GraphNode):
    """组件节点"""
    def __init__(self, name: str, **kwargs):
        """
        初始化组件节点
        
        Args:
            name: 组件名称
            **kwargs: 其他属性
        """
        super().__init__(
            id=name,
            node_type=NodeType.COMPONENT,
            properties={
                'ecosystem': kwargs.get('ecosystem'),
                'platform': kwargs.get('platform'),
                'description': kwargs.get('description'),
                'latest_version': kwargs.get('latest_version')
            }
        )

@dataclass
class VersionNode(GraphNode):
    """版本节点"""
    def __init__(self, component_name: str, version: str, **kwargs):
        """
        初始化版本节点
        
        Args:
            component_name: 组件名称
            version: 版本号
            **kwargs: 其他属性
        """
        super().__init__(
            id=f"{component_name}@{version}",
            node_type=NodeType.VERSION,
            properties={
                'version': version,
                'release_date': kwargs.get('release_date'),
                'end_of_support': kwargs.get('end_of_support'),
                'is_vulnerable': kwargs.get('is_vulnerable', False)
            }
        )

@dataclass
class PatchNode(GraphNode):
    """补丁节点"""
    def __init__(self, patch_id: str, **kwargs):
        """
        初始化补丁节点
        
        Args:
            patch_id: 补丁ID
            **kwargs: 其他属性
        """
        super().__init__(
            id=patch_id,
            node_type=NodeType.PATCH,
            properties={
                'url': kwargs.get('url'),
                'description': kwargs.get('description'),
                'release_date': kwargs.get('release_date'),
                'type': kwargs.get('type')
            }
        )

@dataclass
class WeaknessNode(GraphNode):
    """弱点节点（CWE）"""
    def __init__(self, cwe_id: str, **kwargs):
        """
        初始化弱点节点
        
        Args:
            cwe_id: CWE ID
            **kwargs: 其他属性
        """
        super().__init__(
            id=cwe_id,
            node_type=NodeType.WEAKNESS,
            properties={
                'name': kwargs.get('name', f"Common Weakness: {cwe_id}"),
                'type': kwargs.get('type', 'CWE'),
                'description': kwargs.get('description', ''),
                'severity': kwargs.get('severity', 'medium')
            }
        )

@dataclass
class AffectsRelation(GraphRelation):
    """漏洞影响组件关系"""
    def __init__(self, vuln_id: str, component_id: str, **kwargs):
        """
        初始化影响关系
        
        Args:
            vuln_id: 漏洞ID
            component_id: 组件ID
            **kwargs: 其他属性
        """
        super().__init__(
            source_id=vuln_id,
            target_id=component_id,
            relation_type=RelationType.AFFECTS,
            properties={
                'affected_versions': kwargs.get('affected_versions', []),
                'fixed_versions': kwargs.get('fixed_versions', []),
                'severity': kwargs.get('severity')
            }
        )

@dataclass
class HasVersionRelation(GraphRelation):
    """组件包含版本关系"""
    def __init__(self, component_id: str, version_id: str, **kwargs):
        """
        初始化版本关系
        
        Args:
            component_id: 组件ID
            version_id: 版本ID
            **kwargs: 其他属性
        """
        super().__init__(
            source_id=component_id,
            target_id=version_id,
            relation_type=RelationType.HAS_VERSION,
            properties={
                'release_date': kwargs.get('release_date'),
                'is_latest': kwargs.get('is_latest', False)
            }
        )

@dataclass
class FixedByRelation(GraphRelation):
    """漏洞被补丁修复关系"""
    def __init__(self, vuln_id: str, patch_id: str, **kwargs):
        """
        初始化修复关系
        
        Args:
            vuln_id: 漏洞ID
            patch_id: 补丁ID
            **kwargs: 其他属性
        """
        super().__init__(
            source_id=vuln_id,
            target_id=patch_id,
            relation_type=RelationType.FIXED_BY,
            properties={
                'fix_date': kwargs.get('fix_date'),
                'verified': kwargs.get('verified', False)
            }
        )

@dataclass
class DependsOnRelation(GraphRelation):
    """组件依赖关系"""
    def __init__(self, source_id: str, target_id: str, **kwargs):
        """
        初始化组件依赖关系
        
        Args:
            source_id: 源组件ID
            target_id: 目标组件ID
            **kwargs: 其他属性
        """
        super().__init__(
            source_id=source_id,
            target_id=target_id,
            relation_type=RelationType.DEPENDS_ON,
            properties={
                'version_constraint': kwargs.get('version_constraint', ''),
                'is_dev_dependency': kwargs.get('is_dev_dependency', False)
            }
        )

@dataclass
class HasWeaknessRelation(GraphRelation):
    """漏洞包含弱点关系"""
    def __init__(self, vuln_id: str, cwe_id: str, **kwargs):
        """
        初始化漏洞包含弱点关系
        
        Args:
            vuln_id: 漏洞ID
            cwe_id: CWE ID
            **kwargs: 其他属性
        """
        super().__init__(
            source_id=vuln_id,
            target_id=cwe_id,
            relation_type=RelationType.HAS_WEAKNESS,
            properties=kwargs
        )

"""
使用示例:

1. 创建节点:
# 创建漏洞节点
vuln_node = VulnerabilityNode(
    vuln_id="CVE-2024-1234",
    source="NVD",
    title="SQL注入漏洞",
    severity="HIGH",
    cvss_v3_score=8.5
)

# 创建组件节点
component_node = ComponentNode(
    name="lodash",
    ecosystem="npm",
    latest_version="4.17.21"
)

# 创建版本节点
version_node = VersionNode(
    component_name="lodash",
    version="4.17.15",
    is_vulnerable=True
)

# 创建补丁节点
patch_node = PatchNode(
    patch_id="github-pr-123",
    url="https://github.com/lodash/lodash/pull/123",
    description="修复了CVE-2024-1234漏洞"
)

# 创建弱点节点
weakness_node = WeaknessNode(
    cwe_id="CWE-123",
    name="SQL Injection",
    type="CWE",
    description="A vulnerability that allows an attacker to inject SQL commands into a SQL database query.",
    severity="HIGH"
)

2. 创建关系:
# 创建漏洞影响组件关系
affects_relation = AffectsRelation(
    vuln_id="CVE-2024-1234",
    component_id="lodash",
    affected_versions=["4.17.15", "4.17.16", "4.17.17"],
    fixed_versions=["4.17.18"]
)

# 创建组件包含版本关系
has_version_relation = HasVersionRelation(
    component_id="lodash",
    version_id="lodash@4.17.15"
)

# 创建漏洞被补丁修复关系
fixed_by_relation = FixedByRelation(
    vuln_id="CVE-2024-1234",
    patch_id="github-pr-123",
    fix_date="2024-03-15"
)

# 创建组件依赖关系
depends_on_relation = DependsOnRelation(
    source_id="express",
    target_id="lodash",
    version_constraint="^4.17.0"
)
""" 