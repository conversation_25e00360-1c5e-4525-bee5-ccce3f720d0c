import json
import os
import sys
import time
from typing import Dict, Optional

import requests
from dotenv import load_dotenv

# 加载环境变量
# 假设.env文件在项目根目录
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')
load_dotenv(dotenv_path=dotenv_path)


# 检查必要的环境变量
def check_environment():
    required_vars = {
        "OPENAI_API_KEY": "API密钥",
        "OPENAI_API_URL": "API URL",
        "OPENAI_MODEL": "模型名称",
        "NVD_API_KEY": "NVD API密钥"
    }

    missing_vars = []
    for var, description in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        print("错误：以下必要的环境变量未设置：")
        for var in missing_vars:
            print(f"- {var}")
        print("\n请确保项目根目录下的.env文件中包含这些配置，或者直接在环境变量中设置它们。")
        sys.exit(1)


# 检查环境变量
check_environment()

# 配置API参数
API_KEY = os.getenv("OPENAI_API_KEY")
API_URL = os.getenv("OPENAI_API_URL", "https://api.siliconflow.cn/v1")
MODEL = os.getenv("OPENAI_MODEL", "Pro/deepseek-ai/DeepSeek-V3")
NVD_API_KEY = os.getenv("NVD_API_KEY")


class PocGenerator:
    def __init__(self):
        self.headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        self.session = requests.Session()
        self.nvd_headers = {
            "Accept": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"
        }
        self.proxies = {
            "http": os.getenv("HTTP_PROXY", ""),
            "https": os.getenv("HTTPS_PROXY", "")
        }

    def get_cve_details(self, cve_id: str) -> Optional[Dict]:
        """获取CVE详细信息"""
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                print(f"\n[尝试 {attempt + 1}/{max_retries}] 获取CVE信息...")
                # 使用会话发送请求
                url = f"https://services.nvd.nist.gov/rest/json/cves/2.0?cveId={cve_id}"
                response = self.session.get(
                    url,
                    headers=self.nvd_headers,
                    proxies=self.proxies,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()

                    # 打印完整的NVD API响应JSON
                    print("\n=================== NVD API 原始响应 ===================")
                    print(json.dumps(data, indent=2, ensure_ascii=False))
                    print("====================================================\n")

                    if data.get("vulnerabilities") and len(data["vulnerabilities"]) > 0:
                        vuln = data["vulnerabilities"][0]["cve"]
                        print("- CVE信息获取成功！")

                        # 获取CVSS信息
                        metrics = vuln.get("metrics", {})
                        severity = "UNKNOWN"
                        base_score = 0.0

                        # 按优先级尝试不同版本的CVSS
                        for metric_type in ["cvssMetricV31", "cvssMetricV30", "cvssMetricV2"]:
                            if metric_type in metrics:
                                cvss_data = metrics[metric_type][0].get("cvssData", {})
                                severity = cvss_data.get("baseSeverity", "UNKNOWN")
                                base_score = cvss_data.get("baseScore", 0.0)
                                break

                        # 获取描述（优先使用英文）
                        description = ""
                        if vuln.get("descriptions"):
                            for desc in vuln["descriptions"]:
                                if desc.get("lang") == "en":
                                    description = desc["value"]
                                    break
                            if not description:  # 如果没有找到英文描述，使用第一个可用的描述
                                description = vuln["descriptions"][0]["value"]

                        # 获取参考链接（去重）
                        refs = []
                        seen_urls = set()
                        if vuln.get("references"):
                            for ref in vuln["references"]:
                                url = ref.get("url")
                                if url and url not in seen_urls:
                                    refs.append(url)
                                    seen_urls.add(url)

                        # 获取漏洞类型
                        weaknesses = [w["description"][0]["value"] for w in vuln.get("weaknesses", []) if
                                      w.get("description")]

                        # 构建返回数据
                        cve_info = {
                            "id": vuln["id"],
                            "description": description,
                            "severity": severity,
                            "base_score": base_score,
                            "references": refs,
                            "published": vuln.get("published", ""),
                            "last_modified": vuln.get("lastModified", ""),
                            "weaknesses": weaknesses
                        }

                        # 打印完整的CVE信息到终端
                        print("\n=================== CVE详细信息 ===================")
                        print(f"CVE ID: {cve_info['id']}")
                        print(f"严重程度: {cve_info['severity']} (Base Score: {cve_info['base_score']})")
                        print(f"发布时间: {cve_info['published']}")
                        print(f"最后修改: {cve_info['last_modified']}")
                        print("\n描述:")
                        print(f"{cve_info['description']}")

                        if cve_info['weaknesses']:
                            print("\n漏洞类型:")
                            for weakness in cve_info['weaknesses']:
                                print(f"- {weakness}")

                        if cve_info['references']:
                            print("\n参考链接:")
                            for i, ref in enumerate(cve_info['references'], 1):
                                print(f"{i}. {ref}")

                        print("====================================================\n")
                        print("- CVE信息提取结束!")

                        return cve_info
                    else:
                        print("- 未找到CVE信息")
                else:
                    print(f"- 请求失败，状态码: {response.status_code}")
                    print(f"- 响应内容: {response.text}")

            except Exception as e:
                print(f"- 获取CVE信息时出错: {str(e)}")

            if attempt < max_retries - 1:
                print(f"- 等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                print("- 已达到最大重试次数，获取CVE信息失败")

        return None

    def generate_poc(self, cve_details: Dict) -> Optional[str]:
        """使用LLM生成PoC代码"""
        print("\n正在生成PoC代码...")
        print(f"- CVE ID: {cve_details['id']}")
        print(f"- 严重程度: {cve_details['severity']} (Base Score: {cve_details['base_score']})")
        print("- 正在构建提示...")

        prompt = f"""
请根据以下CVE漏洞信息生成一个完整的Python PoC（概念验证）代码：

<漏洞信息>
CVE ID: {cve_details['id']}
描述: {cve_details['description']}
严重程度: {cve_details['severity']} (Base Score: {cve_details['base_score']})
参考链接: {', '.join(cve_details['references'][:3])}
弱点类型: {', '.join(cve_details['weaknesses'][:2]) if cve_details.get('weaknesses') else '未指定'}
</漏洞信息>

请逐步分析这个漏洞，注意以下几点：
1. 仔细分析漏洞描述，确定漏洞类型、受影响的组件和攻击向量
2. 参考提供的链接获取更多技术细节，特别关注漏洞的触发条件和利用方法
3. 确定验证漏洞存在的最佳方法，优先选择非破坏性的验证手段
4. 设计一个清晰的命令行界面，允许用户配置目标和其他参数
5. 实现完整的错误处理和详细的日志输出

PoC代码需要满足以下要求：
1. 代码应包含详细的文档字符串，解释漏洞原理和利用方法
2. 实现完整的错误处理和日志记录机制
3. 包含命令行参数解析，支持灵活配置
4. 遵循Python安全编程最佳实践
5. 代码应该易于理解和修改
6. 如果涉及Web漏洞，优先使用requests库
7. 确保代码可以在Python 3.7+环境下运行

请提供完整可执行的Python脚本，包含以下部分：
1. 脚本描述和使用说明
2. 必要的导入语句
3. 漏洞检测和利用函数
4. 主函数和命令行参数处理
5. 示例用法和测试案例

输出格式为完整的Python脚本，不要使用引用句式如"以上是漏洞的PoC代码"。
"""

        try:
            print("- 正在调用LLM API...")
            payload = {
                "model": MODEL,
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的网络安全工程师，负责分析漏洞披露网站并总结漏洞信息。你拥有关于CVE、PoC(概念验证)和EXP(漏洞利用)的深入知识，熟悉各种常用编程语言，并能阅读中英文资料。你擅长编写高质量的漏洞验证和利用代码，这些代码应遵循安全编程最佳实践，并包含必要的注释和文档。请按照用户的要求逐步分析漏洞并生成相应的PoC代码。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "stream": True,  # 启用流式输出
                "max_tokens": 2000,
                "temperature": 0.7,
                "top_p": 0.7,
                "top_k": 50,
                "frequency_penalty": 0.5,
                "n": 1,
                "response_format": {"type": "text"}
            }

            print("- 发送请求...")

            # 使用流式响应
            response = requests.post(
                f"{API_URL}/chat/completions",
                json=payload,
                headers=self.headers,
                proxies=self.proxies,
                timeout=240,  # 增加超时时间到240秒
                stream=True  # 请求库的流式参数
            )

            # 检查响应状态
            if response.status_code != 200:
                print(f"- API请求失败: {response.status_code}")
                print(f"- 错误信息: {response.text}")
                return None

            # 处理流式响应
            full_content = ""
            for line in response.iter_lines():
                if line:
                    # 解析SSE数据行
                    line_text = line.decode('utf-8')
                    if line_text.startswith('data: '):
                        data_str = line_text[6:]  # 去掉'data: '前缀
                        if data_str == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            delta_content = data.get('choices', [{}])[0].get('delta', {}).get('content', '')
                            if delta_content:
                                print(delta_content, end='', flush=True)
                                full_content += delta_content
                        except json.JSONDecodeError:
                            pass

            print("\n- PoC代码生成完成！")
            return full_content

        except requests.Timeout:
            print("- 请求超时，可能是网络问题或API服务器响应较慢")
            return None
        except requests.ConnectionError:
            print("- 连接错误，请检查网络连接")
            return None
        except Exception as e:
            print(f"- 生成PoC代码时出错: {str(e)}")
            return None

    def save_poc(self, cve_id: str, poc_code: str, output_dir: str = "pocs"):
        """保存PoC代码到文件"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        filename = f"{output_dir}/{cve_id}_poc.py"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(poc_code)
        print(f"PoC代码已保存到: {filename}")

    def save_poc_to_markdown(self, cve_details: Dict, poc_code: str) -> None:
        """将PoC代码保存为markdown格式"""
        try:
            print("\n正在保存PoC代码...")
            # 创建pocs目录（如果不存在）
            os.makedirs("pocs", exist_ok=True)

            # 构建参考链接部分，确保每个链接单独一行
            references = "\n".join([f"- {ref}" for ref in cve_details['references']])

            # 构建markdown内容
            print("- 正在生成markdown文档...")
            markdown_content = f"""# {cve_details['id']} PoC

## 漏洞信息

- **漏洞ID**: {cve_details['id']}
- **严重程度**: {cve_details['severity']} (Base Score: {cve_details['base_score']})
- **发布时间**: {cve_details['published']}
- **最后修改**: {cve_details['last_modified']}

## 漏洞描述

{cve_details['description']}

## 相关链接

{references}

## 漏洞类型

{"\\n".join([f"- {w}" for w in cve_details['weaknesses']])}

## PoC 代码

```python
{poc_code}
```

## 免责声明

本PoC代码仅用于安全研究和测试目的。使用本代码进行测试时请确保已获得授权。对因滥用该代码所造成的任何损失，本项目概不负责。
"""

            # 保存文件
            filename = f"pocs/{cve_details['id']}.md"
            print(f"- 正在保存到文件: {filename}")
            with open(filename, "w", encoding="utf-8") as f:
                f.write(markdown_content)

            print(f"- PoC已成功保存到: {filename}")

        except Exception as e:
            print(f"- 保存PoC代码时出错: {str(e)}")

    def func(self, cve_id: str) -> None:
        """获取CVE信息，生成PoC代码并保存"""
        cve_details = self.get_cve_details(cve_id)
        if cve_details:
            poc_code = self.generate_poc(cve_details)
            if poc_code:
                self.save_poc_to_markdown(cve_details, poc_code)


def main():
    generator = PocGenerator()

    # 示例使用
    cve_id = input("请输入CVE ID (例如: CVE-2023-1234): ")
    generator.func(cve_id)


if __name__ == "__main__":
    main()
