#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Neo4j图数据库连接测试脚本
"""

import sys
import os
from neo4j import GraphDatabase
from neo4j.exceptions import Neo4jError

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_neo4j_connection():
    """测试Neo4j连接"""
    print("尝试连接Neo4j数据库...")
    
    # 连接参数
    uri = "bolt://localhost:7687"
    username = "neo4j"
    password = "password"  # 请替换为您实际设置的密码
    
    try:
        # 创建连接
        driver = GraphDatabase.driver(uri, auth=(username, password))
        
        # 验证连接
        driver.verify_connectivity()
        
        # 执行简单查询
        with driver.session() as session:
            result = session.run("RETURN 'Neo4j连接成功' AS message")
            message = result.single()["message"]
            print(f"查询结果: {message}")
            
            # 创建测试节点
            print("创建测试节点...")
            session.run("""
                CREATE (v:Vulnerability {id: 'TEST-001', name: '测试漏洞'})
                RETURN v
            """)
            
            # 查询节点
            print("查询测试节点...")
            result = session.run("MATCH (v:Vulnerability {id: 'TEST-001'}) RETURN v.name")
            node_name = result.single()["v.name"]
            print(f"节点名称: {node_name}")
            
            # 删除测试节点
            print("清理测试节点...")
            session.run("MATCH (v:Vulnerability {id: 'TEST-001'}) DELETE v")
        
        # 关闭连接
        driver.close()
        print("Neo4j连接测试成功!")
        return True
    except Neo4jError as e:
        print(f"Neo4j错误: {str(e)}")
        return False
    except Exception as e:
        print(f"连接错误: {str(e)}")
        return False

if __name__ == "__main__":
    test_neo4j_connection() 