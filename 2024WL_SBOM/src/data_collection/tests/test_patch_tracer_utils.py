#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试补丁跟踪器工具函数的可用性
"""

import sys
import json
import logging
import importlib.util
from typing import Optional, List, Tuple

# 动态导入模块
spec = importlib.util.spec_from_file_location(
    "patch_tracer_utils", 
    "2024WL_SBOM/src/data_collection/services/patch_tracer/utils.py"
)
patch_tracer_utils = importlib.util.module_from_spec(spec)
spec.loader.exec_module(patch_tracer_utils)

# 从模块中获取函数
extract_commit_hash_from_url = patch_tracer_utils.extract_commit_hash_from_url
find_github_repo_by_commit_hash = patch_tracer_utils.find_github_repo_by_commit_hash

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 从配置文件中读取GitHub API token
def get_github_token():
    """从sources.json配置文件中获取GitHub API token"""
    try:
        with open('2024WL_SBOM/src/data_collection/config/sources.json', 'r', encoding='utf-8') as f:
            sources_config = json.load(f)
            return sources_config.get('sources', {}).get('GitHub', {}).get('api_key', None)
    except Exception as e:
        logger.error(f"无法读取GitHub API token: {str(e)}")
        return None

def test_extract_commit_hash():
    """测试从URL中提取commit hash的功能"""
    print("测试 extract_commit_hash_from_url 函数...")
    
    # 测试用例：不同格式的URL
    test_cases = [
        # GitHub格式
        ("https://github.com/openssl/openssl/commit/6e5621b21dc37adbd5f3452bae7b39b5d57decbf", 
         "6e5621b21dc37adbd5f3452bae7b39b5d57decbf"),
        # GitLab格式
        ("https://gitlab.com/gitlab-org/gitlab/-/commit/a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2", 
         "a1b2c3d4e5f6a1b2c3d4e5f6a1b2c3d4e5f6a1b2"),
        # OpenSSL格式
        ("https://git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=2a34c5f67890a1b2c3d4e5f6a7b8c9d0e1f2a3b4", 
         "2a34c5f67890a1b2c3d4e5f6a7b8c9d0e1f2a3b4"),
        # Apache格式
        ("https://gitbox.apache.org/repos/asf?p=httpd.git;a=commit;h=3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c", 
         "3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c"),
        # Linux kernel格式
        ("https://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux.git/commit/?id=4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3", 
         "4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3"),
        # GNOME格式
        ("https://gitlab.gnome.org/GNOME/gnome-shell/-/commit/5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4", 
         "5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4"),
        # 异常情况
        ("https://example.com/not-a-commit-url", None)
    ]
    
    # 测试每个用例
    for url, expected_hash in test_cases:
        extracted_hash = extract_commit_hash_from_url(url)
        
        if extracted_hash == expected_hash:
            result = "✓ 通过"
        else:
            result = f"✗ 失败 (得到: {extracted_hash}, 期望: {expected_hash})"
            
        print(f"URL: {url}\n结果: {result}\n")

def test_find_github_repo(github_token: Optional[str] = None):
    """测试根据commit hash查找GitHub仓库的功能"""
    print("测试 find_github_repo_by_commit_hash 函数...")
    
    if not github_token:
        print("警告：没有GitHub token，API请求可能会受到限制")
    
    # 测试用例：已知存在的commit hash
    test_cases = [
        # OpenSSL (CVE-2023-0286 相关commit)
        "2c6c9d439b484e1ba9830d8454a34fa4f80fdfe9",
        # Linux Kernel 中的一个commit
        "b28e8c904382de2a1e633ef253aac71271cd3b49",
        # Linux Kernel 中的另一个commit (CVE-2022-0185)
        "dcd46d897adb70d63e025f175a00a89797d31a43",
        # Node.js 中的一个commit
        "dc14e0f8801c9dccdcd5e80fda793438b1c9461d" 
    ]
    
    successful_tests = 0
    total_tests = len(test_cases)
    
    for commit_hash in test_cases:
        print(f"\n查找commit hash: {commit_hash}")
        result = find_github_repo_by_commit_hash(commit_hash, github_token)
        
        if result:
            owner, repo, url = result
            print(f"✓ 找到仓库: {owner}/{repo}")
            print(f"URL: {url}")
            successful_tests += 1
        else:
            print(f"✗ 未找到仓库")
    
    # 打印测试总结
    print(f"\n测试结果总结: {successful_tests}/{total_tests} 成功")
    if successful_tests == 0:
        print("提示：如果所有测试都失败，可能是GitHub API限制或token无效。")

def main():
    """主函数"""
    # 从配置文件获取GitHub token
    github_token = get_github_token()
    if github_token:
        print(f"已从配置文件获取GitHub token: {github_token[:5]}...{github_token[-5:]}")
    else:
        print("警告：未能从配置文件获取GitHub token")
        # 尝试从命令行参数获取
        if len(sys.argv) > 1:
            github_token = sys.argv[1]
            print(f"使用命令行提供的token: {github_token[:5]}...{github_token[-5:]}")
    
    # 测试从URL中提取commit hash
    test_extract_commit_hash()
    
    # 测试根据commit hash查找GitHub仓库
    test_find_github_repo(github_token)

if __name__ == "__main__":
    main() 