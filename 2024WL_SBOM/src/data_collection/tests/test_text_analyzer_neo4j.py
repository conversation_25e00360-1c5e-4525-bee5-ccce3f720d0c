#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试文本分析器与Neo4j数据库的集成
"""
import os
import sys
from typing import List, Dict, Any

# 设置路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.text_analyzer import TextAnalyzer
from src.services.graph_store import GraphStore
from src.models.graph import NodeType, RelationType
from src.utils.logger import setup_logger, logger

def test_vulnerability_text_analysis():
    """测试漏洞文本分析"""
    # 设置日志
    setup_logger()
    
    # 初始化文本分析器和图数据库连接
    analyzer = TextAnalyzer()
    graph_store = GraphStore()
    
    # 获取部分漏洞节点
    limit = 5  # 限制数量，避免处理太多数据
    
    # 手动实现限制获取节点的功能
    vulnerabilities = graph_store.get_nodes(NodeType.VULNERABILITY)
    vulnerabilities = vulnerabilities[:limit]
    
    logger.info(f"获取到 {len(vulnerabilities)} 个漏洞节点进行测试")
    
    # 提取漏洞描述
    descriptions = []
    vuln_ids = []
    
    for vuln in vulnerabilities:
        props = vuln.get("properties", {})
        vuln_id = props.get("id")
        description = props.get("description")
        
        if vuln_id and description:
            descriptions.append(description)
            vuln_ids.append(vuln_id)
            logger.info(f"漏洞 {vuln_id}:")
            logger.info(f"  描述: {description[:150]}..." if len(description) > 150 else f"  描述: {description}")
    
    # 测试1: 文本相似度分析
    if len(descriptions) > 1:
        logger.info("\n===== 测试1: 文本相似度分析 =====")
        similarity_matrix = analyzer.compute_text_similarity(descriptions)
        
        # 找出相似的漏洞描述
        similar_pairs = analyzer.find_similar_texts(descriptions, threshold=0.3)
        
        logger.info(f"发现 {len(similar_pairs)} 对相似漏洞:")
        for i, j, score in similar_pairs:
            logger.info(f"  相似度 {score:.2f}: {vuln_ids[i]} <-> {vuln_ids[j]}")
    
    # 添加一些已知包含组件信息的漏洞描述样本
    sample_descriptions = [
        "Apache Log4j2 2.0-beta9 through 2.15.0 JNDI features used in configuration, log messages, and parameters do not protect against attacker controlled LDAP and other JNDI related endpoints.",
        "Spring Framework 5.3.0 to 5.3.17, 5.2.0 to 5.2.19 contains a remote code execution vulnerability in spring-beans module.",
        "A cross-site scripting (XSS) vulnerability in jQuery before 3.5.0 allows crafted HTML to bypass protection mechanisms."
    ]
    
    sample_ids = ["CVE-2021-44228", "CVE-2022-22965", "CVE-2019-11358"]
    
    # 测试2: 从样本描述中提取组件信息
    logger.info("\n===== 测试2: 从样本描述中提取组件信息 =====")
    
    for i, description in enumerate(sample_descriptions):
        vuln_id = sample_ids[i]
        logger.info(f"分析漏洞样本 {vuln_id}:")
        logger.info(f"  描述: {description}")
        
        # 提取组件信息
        components = analyzer.extract_components_from_text(description)
        
        if components:
            logger.info(f"  提取到 {len(components)} 个组件:")
            for comp in components:
                logger.info(f"    - 名称: {comp.get('name', 'N/A')}")
                logger.info(f"      生态系统: {comp.get('ecosystem', 'N/A')}")
                logger.info(f"      版本范围: {comp.get('version_range', 'N/A')}")
                
                # 标准化组件名称
                original_name = comp.get('name', '')
                normalized_name = analyzer.normalize_component_name(original_name)
                logger.info(f"      标准化名称: {normalized_name}")
                
                # 查询Neo4j中是否有匹配的组件
                neo4j_components = graph_store.get_nodes(NodeType.COMPONENT)
                
                # 检查是否有相同或相似的组件名称
                matching_components = []
                for neo4j_comp in neo4j_components:
                    neo4j_name = neo4j_comp.get("properties", {}).get("id", "")
                    if normalized_name.lower() in neo4j_name.lower() or neo4j_name.lower() in normalized_name.lower():
                        matching_components.append(neo4j_name)
                
                if matching_components:
                    logger.info(f"      Neo4j中匹配的组件: {matching_components}")
                else:
                    logger.info(f"      Neo4j中没有匹配的组件")
        else:
            logger.info("  未提取到任何组件信息")
    
    # 测试3: 组件名称分组
    logger.info("\n===== 测试3: 组件名称分组 =====")
    
    # 从Neo4j获取所有组件
    neo4j_components = graph_store.get_nodes(NodeType.COMPONENT)
    component_names = [comp.get("properties", {}).get("id", "") for comp in neo4j_components]
    
    # 限制组件数量，避免处理过多
    component_names = component_names[:20]
    
    logger.info(f"使用 {len(component_names)} 个组件名称进行分组测试")
    
    # 分组相似组件名称
    groups = analyzer.group_similar_components(component_names)
    
    logger.info(f"共分成 {len(groups)} 个组:")
    for i, group in enumerate(groups):
        if len(group) > 1:  # 只显示有多个组件的组
            logger.info(f"  组 {i+1}: {group}")
    
    logger.info("\n文本分析集成测试完成")

if __name__ == "__main__":
    test_vulnerability_text_analysis()
