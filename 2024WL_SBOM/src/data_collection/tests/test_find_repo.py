#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import time
import requests
import json
import re

# 添加项目根目录到Python路径
sys.path.append('/home')

# 动态导入，避免使用数字开头的模块名称
import importlib
utils_module = importlib.import_module('2024WL_SBOM.src.data_collection.services.patch_tracer.utils')
find_github_repo_by_commit_hash = utils_module.find_github_repo_by_commit_hash
extract_commit_hash_from_url = utils_module.extract_commit_hash_from_url

# 添加调试版本的查找函数
def debug_find_github_repo(commit_hash, github_token=None):
    """与find_github_repo_by_commit_hash相同，但添加了调试输出并修复了功能"""
    if not commit_hash or len(commit_hash) < 7:
        print(f"无效的commit hash: {commit_hash}")
        return None
        
    # 构造搜索URL
    search_url = f"https://api.github.com/search/commits?q=hash:{commit_hash}"
    print(f"API请求URL: {search_url}")
    
    # 设置请求头
    headers = {
        'Accept': 'application/vnd.github.cloak-preview',  # 必须的preview头
        'User-Agent': 'PatchTracer/1.0'
    }
    if github_token:
        headers['Authorization'] = f'token {github_token}'
        print("使用了GitHub令牌")
    else:
        print("未使用GitHub令牌，可能会受到API速率限制")
        
    try:
        response = requests.get(search_url, headers=headers, timeout=30)
        print(f"API响应状态码: {response.status_code}")
        
        # 检查GitHub API速率限制
        rate_limit = {
            'limit': response.headers.get('X-RateLimit-Limit', 'unknown'),
            'remaining': response.headers.get('X-RateLimit-Remaining', 'unknown'),
            'reset': response.headers.get('X-RateLimit-Reset', 'unknown'),
        }
        print(f"API速率限制: 限制={rate_limit['limit']}, 剩余={rate_limit['remaining']}, 重置={rate_limit['reset']}")
        
        if response.status_code == 403 and 'rate limit exceeded' in response.text.lower():
            print("超出GitHub API速率限制，请等待重置或使用令牌")
            return None
            
        if response.status_code != 200:
            print(f"GitHub API请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text[:500]}")  # 只打印前500个字符
            return None
            
        data = response.json()
        items_count = len(data.get('items', []))
        total_count = data.get('total_count', 0)
        print(f"找到 {total_count} 个匹配项，返回了 {items_count} 个结果")
        
        if items_count == 0:
            print("没有找到匹配的commit")
            return None
            
        repos_data = []
        # 遍历找到的结果（为了节省API调用，只处理前3个）
        for item in data.get('items', [])[:3]:
            repo_url = item.get('repository', {}).get('html_url', '')
            if not repo_url:
                continue
                
            owner_repo_match = re.search(r'github\.com/([^/]+)/([^/]+)/?$', repo_url)
            if not owner_repo_match:
                continue
                
            owner = owner_repo_match.group(1)
            repo = owner_repo_match.group(2)
            
            print(f"处理仓库: {owner}/{repo}")
            
            # 获取提交详情，包括时间戳
            commit_url = f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_hash}"
            
            try:
                # 避免API请求过于频繁
                time.sleep(1)
                
                commit_headers = headers.copy()
                commit_response = requests.get(commit_url, headers=commit_headers, timeout=30)
                
                # 检查API速率限制
                rate_limit_commit = {
                    'limit': commit_response.headers.get('X-RateLimit-Limit', 'unknown'),
                    'remaining': commit_response.headers.get('X-RateLimit-Remaining', 'unknown'),
                    'reset': commit_response.headers.get('X-RateLimit-Reset', 'unknown'),
                }
                print(f"  API速率限制: 限制={rate_limit_commit['limit']}, 剩余={rate_limit_commit['remaining']}")
                
                if commit_response.status_code == 403 and 'rate limit exceeded' in commit_response.text.lower():
                    print("  超出GitHub API速率限制，请等待重置或使用令牌")
                    return None
                
                if commit_response.status_code != 200:
                    print(f"  获取commit详情失败: HTTP {commit_response.status_code}")
                    continue
                    
                commit_data = commit_response.json()
                commit_date = commit_data.get('commit', {}).get('committer', {}).get('date')
                if not commit_date:
                    commit_date = commit_data.get('commit', {}).get('author', {}).get('date')
                    
                if commit_date:
                    # 标准化日期格式
                    if commit_date.endswith('Z'):
                        commit_date = commit_date[:-1] + '+00:00'
                        
                    print(f"  找到commit，提交日期: {commit_date}")
                    repos_data.append({
                        'owner': owner,
                        'repo': repo,
                        'url': f"https://github.com/{owner}/{repo}/commit/{commit_hash}",
                        'commit_date': commit_date
                    })
            except Exception as e:
                print(f"  处理仓库 {owner}/{repo} 时出错: {str(e)}")
        
        # 如果找到了仓库，按提交时间排序
        if repos_data:
            # 按时间戳升序排序（最早的在前）
            print(f"对找到的 {len(repos_data)} 个仓库按提交时间排序")
            sorted_repos = sorted(repos_data, key=lambda x: x.get('commit_date', ''))
            earliest_repo = sorted_repos[0]
            print(f"最早的提交来自: {earliest_repo['owner']}/{earliest_repo['repo']} 于 {earliest_repo['commit_date']}")
            return (earliest_repo['owner'], earliest_repo['repo'], earliest_repo['url'])
        else:
            print("未找到有效的仓库信息")
            return None
                
    except Exception as e:
        print(f"查找仓库时发生错误: {str(e)}")
        return None

# 从sources.json读取GitHub令牌
def load_github_token():
    try:
        sources_path = '/home/<USER>/src/data_collection/config/sources.json'
        with open(sources_path, 'r', encoding='utf-8') as f:
            sources = json.load(f)
        return sources.get('sources', {}).get('GitHub', {}).get('api_key', '')
    except Exception as e:
        print(f"读取GitHub令牌失败: {str(e)}")
        return None

# 手动设置GitHub令牌（从sources.json读取）
github_token = load_github_token()
print(f"GitHub令牌状态: {'已设置' if github_token else '未设置'}")
if github_token:
    print(f"令牌前10位: {github_token[:10]}...")

# 测试已知存在于GitHub的commit
print("\n测试已知存在于GitHub的commit:")
known_github_commits = [
    # Linux内核的一个commit
    "19f949f52599ba7c3f67a5897ac6be14bfcb1200"
]

for hash_value in known_github_commits:
    print(f"\nCommit hash: {hash_value}")
    print("正在查找GitHub仓库...")
    repo_info = debug_find_github_repo(hash_value, github_token)
    if repo_info:
        owner, repo, url = repo_info
        print(f"找到GitHub仓库:")
        print(f"  - 所有者: {owner}")
        print(f"  - 仓库名: {repo}")
        print(f"  - URL: {url}")
    else:
        print(f"未找到对应的GitHub仓库")
    # 避免GitHub API速率限制
    time.sleep(2)

# 最后测试OpenSSL提交
print("\n测试OpenSSL commit hash:")
openssl_url = 'https://git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=fd2af07dc083a350c959147097003a14a5e8ac4d'
commit_hash = extract_commit_hash_from_url(openssl_url)
print(f"从URL提取的commit hash: {commit_hash}")

print("正在查找GitHub仓库...")
repo_info = debug_find_github_repo(commit_hash, github_token)

if repo_info:
    owner, repo, url = repo_info
    print(f"找到GitHub仓库:")
    print(f"  - 所有者: {owner}")
    print(f"  - 仓库名: {repo}")
    print(f"  - URL: {url}")
else:
    print(f"未找到对应的GitHub仓库") 