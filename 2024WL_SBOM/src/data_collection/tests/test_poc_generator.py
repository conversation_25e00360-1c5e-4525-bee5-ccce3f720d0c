#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import argparse
import logging
from dotenv import load_dotenv

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入POC生成器
from src.data_collection.services.generetors.poc_llmgenerator import PocGenerator, check_environment

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

def main():
    """
    测试POC生成器功能
    """
    # 加载环境变量
    load_dotenv()
    
    # 检查环境变量
    check_environment()
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="测试POC生成器")
    parser.add_argument("--cve_id", type=str, help="CVE ID，例如: CVE-2023-1234")
    parser.add_argument("--output_dir", type=str, default="output", help="输出目录")
    args = parser.parse_args()
    
    if not args.cve_id:
        cve_id = input("请输入CVE ID (例如: CVE-2023-1234): ")
    else:
        cve_id = args.cve_id
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化POC生成器
    generator = PocGenerator()
    
    # 获取CVE详情并生成POC
    logger.info(f"开始处理 {cve_id}")
    output_file = os.path.join(args.output_dir, f"{cve_id.replace('-', '_')}_poc.md")
    
    try:
        # 生成POC
        result = generator.func(cve_id, output_file)
        
        if result:
            logger.info(f"成功生成POC，已保存到 {output_file}")
            
            # 显示生成的POC的前几行
            with open(output_file, "r") as f:
                content = f.read()
                preview = "\n".join(content.split("\n")[:10]) + "\n..."
                logger.info(f"生成的POC预览:\n{preview}")
        else:
            logger.error(f"生成POC失败")
    except Exception as e:
        logger.error(f"生成POC时发生错误: {str(e)}")

if __name__ == "__main__":
    main() 