# -*- coding: utf-8 -*-

"""
CNNVD爬虫调用示例

演示如何在其他程序中调用CNNVDCrawler类进行漏洞数据采集。
提供了多种调用方式，包括基本调用、多线程采集和自定义搜索等。
"""

import os
import sys
import sqlite3
import argparse

# 添加项目根目录到系统路径以解决导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.insert(0, parent_dir)

# 正确导入CNNVDCrawler
from src.data_collection.collectors.cnnvd import CNNVDCrawler

# ===============================================================
# 示例1: 基本调用 - 直接创建实例并启动爬虫
# ===============================================================
def example_basic_usage():
    """基本调用示例 - 使用命令行参数方式"""
    print("\n=== 示例1: 基本调用 ===")
    
    # 创建CNNVDCrawler实例
    crawler = CNNVDCrawler()
    
    # 设置命令行参数并启动爬虫
    # 相当于命令行: python cnnvd.py -A -s 1 -e 2
    sys.argv = ['cnnvd.py', '-A', '-s', '1', '-e', '2']
    
    # 启动爬虫
    crawler.start()
    
    print(f"爬取完成，成功获取 {crawler.Task.count} 条记录")

# ===============================================================
# 示例2: 直接调用特定方法 - 单线程模式爬取指定页面
# ===============================================================
def example_update_data():
    """直接调用update_data方法获取指定页面范围的数据"""
    print("\n=== 示例2: 单线程模式爬取 ===")
    
    # 创建CNNVDCrawler实例
    crawler = CNNVDCrawler()
    
    # 使用单线程模式爬取第1页到第3页的数据
    start_page = 1
    end_page = 3
    crawler.update_data(start_page, end_page)
    
    print(f"单线程爬取完成，成功获取 {crawler.Task.count} 条记录")

# ===============================================================
# 示例3: 使用多线程模式获取数据
# ===============================================================
def example_multithreaded_crawl():
    """使用多线程模式加速爬取过程"""
    print("\n=== 示例3: 多线程模式爬取 ===")
    
    # 创建CNNVDCrawler实例
    crawler = CNNVDCrawler()
    
    # 自定义线程数量（可选）
    crawler.Task.threads = 10  # 设置线程池大小
    
    # 使用多线程模式爬取第1页到第2页的数据
    crawler.get_all(1, 2)
    
    print(f"多线程爬取完成，成功获取 {crawler.Task.count} 条记录")

# ===============================================================
# 示例4: 使用自定义搜索条件
# ===============================================================
def example_custom_search():
    """使用自定义搜索条件筛选漏洞数据"""
    print("\n=== 示例4: 自定义搜索条件 ===")
    
    # 创建CNNVDCrawler实例
    crawler = CNNVDCrawler()
    
    # 创建自定义页面配置
    # 搜索关键词为"Windows"，危害等级为"高危"的漏洞
    page = crawler.Page(pageIndex=1, pageSize=50, keyword="Windows", hazardLevel="2")
    
    # 使用单线程模式处理一个页面
    continue_crawl = crawler.engine_start_one(page.info)
    
    print(f"自定义搜索完成，成功获取 {crawler.Task.count} 条记录")
    
    # 如果需要继续爬取下一页
    if continue_crawl:
        print("可以继续爬取下一页")

# ===============================================================
# 示例5: 查询已采集的数据
# ===============================================================
def example_query_database():
    """从数据库中查询已采集的漏洞数据"""
    print("\n=== 示例5: 查询数据库 ===")
    
    # 创建CNNVDCrawler实例
    crawler = CNNVDCrawler()
    
    # 连接数据库
    conn, cursor = crawler.connect_database()
    
    try:
        # 查询所有漏洞，不限制危害等级
        cursor.execute(f"SELECT cnnvd_id, vulName, hazardLevel FROM {crawler.tablename} LIMIT 10")
        vulns = cursor.fetchall()
        
        print(f"查询到 {len(vulns)} 条漏洞记录")
        
        # 显示查询结果
        for vuln in vulns:
            print(f"ID: {vuln[0]}, 名称: {vuln[1]}, 危害等级: {vuln[2]}")
        
    finally:
        # 关闭数据库连接
        crawler.close_database(conn, cursor)

# ===============================================================
# 示例6: 获取单个漏洞详细信息
# ===============================================================
def example_get_single_vulnerability():
    """获取单个漏洞的详细信息"""
    print("\n=== 示例6: 获取单个漏洞详细信息 ===")
    
    # 创建CNNVDCrawler实例
    crawler = CNNVDCrawler()
    
    # 创建一个页面请求参数
    page = crawler.Page(pageIndex=1, pageSize=1)
    
    # 获取第一页的第一条漏洞
    pocs = crawler.get_page_pocs(page.info)
    
    if pocs and len(pocs) > 0:
        # 获取第一个漏洞的详细信息
        poc = pocs[0]
        print(f"获取漏洞详情: {poc['vulName']}")
        
        vuln_details = crawler.get_poc(poc)
        
        # 显示漏洞详情
        print(f"漏洞ID: {vuln_details.get('cnnvdCode', 'N/A')}")
        print(f"漏洞名称: {vuln_details.get('vulName', 'N/A')}")
        print(f"CVE编号: {vuln_details.get('cveCode', 'N/A')}")
        print(f"危害等级: {vuln_details.get('hazardLevel', 'N/A')}")
        print(f"漏洞类型: {vuln_details.get('vulType', 'N/A')}")
        
        # 可以选择是否保存到数据库
        save_to_db = False
        if save_to_db:
            crawler.sqlite_insert_data(vuln_details)
            print("已保存到数据库")

# ===============================================================
# 示例7: 与其他系统集成
# ===============================================================
def example_integration():
    """展示如何将CNNVDCrawler集成到其他系统中"""
    print("\n=== 示例7: 系统集成 ===")
    
    # 假设这是您的主应用程序的一部分
    def update_vulnerability_database():
        """定期更新漏洞数据库的函数"""
        
        print("开始定期更新漏洞数据库...")
        
        # 创建CNNVDCrawler实例
        crawler = CNNVDCrawler()
        
        # 自定义数据库路径（可选）
        # crawler.database_path = "/path/to/custom/database.db"
        
        # 只爬取最新的3页数据
        print("正在获取最新漏洞数据...")
        crawler.get_all(1, 3)
        
        # 查询最新的数据
        conn, cursor = crawler.connect_database()
        try:
            # 获取最新10条记录
            cursor.execute(f"SELECT cnnvd_id, vulName, updateTime FROM {crawler.tablename} ORDER BY updateTime DESC LIMIT 10")
            latest_vulns = cursor.fetchall()
            
            print(f"获取到 {len(latest_vulns)} 条最新漏洞记录")
            
            # 在这里可以将数据传递给您的应用程序的其他部分
            # 例如: notify_security_team(latest_vulns)
            
            return latest_vulns
        finally:
            crawler.close_database(conn, cursor)
    
    # 调用更新函数
    latest_vulnerabilities = update_vulnerability_database()
    
    # 模拟将数据传递给其他系统组件
    print("将最新漏洞数据传递给安全团队...")
    for vuln in latest_vulnerabilities[:3]:  # 只处理前3个
        print(f"发送通知: 发现新漏洞 {vuln[0]} - {vuln[1]}")

# ===============================================================
# 主函数
# ===============================================================
if __name__ == "__main__":
    print("CNNVD爬虫调用示例")
    print("正在运行测试...")
    
    # 1. 单个漏洞获取测试
    print("\n1. 获取单个漏洞并保存")
    
    # 创建CNNVDCrawler实例
    crawler = CNNVDCrawler()
    
    # 获取第一页的第一条漏洞
    page = crawler.Page(pageIndex=1, pageSize=1)
    pocs = crawler.get_page_pocs(page.info)
    
    if pocs and len(pocs) > 0:
        # 获取第一个漏洞的详细信息并保存
        poc = pocs[0]
        print(f"获取漏洞详情: {poc['vulName']}")
        vuln_details = crawler.get_poc(poc)
        crawler.sqlite_insert_data(vuln_details)
        print(f"已保存漏洞到数据库: {vuln_details.get('cnnvdCode')}")
    
    # 2. 数据库查询测试
    print("\n2. 查询数据库")
    example_query_database()
    
    # 3. 多线程获取测试（仅获取第一页数据，限制线程数为3）
    print("\n3. 多线程获取测试")
    test_crawler = CNNVDCrawler()
    test_crawler.Task.threads = 3  # 设置线程数为3
    test_crawler.get_all(1, 1)  # 只获取第一页
    
    print("\n测试完成")