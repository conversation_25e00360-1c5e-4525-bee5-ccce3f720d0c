#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图数据库服务真实连接测试脚本

使用实际Neo4j连接测试GraphStore、PathAnalyzer和AnalyzerService
"""

import os
import sys
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from src.services.graph_store import GraphStore
from src.services.path_analyzer import PathAnalyzer
from src.services.analyzer import AnalyzerService
from src.models.graph import (
    VulnerabilityNode, ComponentNode, VersionNode, PatchNode,
    AffectsRelation, HasVersionRelation, FixedByRelation, DependsOnRelation,
    NodeType, RelationType
)
from src.utils.logger import setup_logger, logger

# Neo4j连接参数
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "12345678"  # 使用实际密码

def test_graph_store():
    """测试GraphStore服务（实际连接）"""
    logger.info("===== 测试GraphStore服务（实际连接） =====")
    
    try:
        # 初始化图数据库服务
        graph_store = GraphStore(
            uri=NEO4J_URI,
            username=NEO4J_USER,
            password=NEO4J_PASSWORD,
            test_mode=False  # 使用实际连接
        )
        logger.info("GraphStore初始化成功")
        
        # 检查连接状态
        if graph_store.is_connected():
            logger.info("GraphStore连接状态: 已连接")
        else:
            logger.error("GraphStore连接状态: 未连接")
            return False
        
        # 创建测试数据
        vuln = VulnerabilityNode(
            vuln_id="CVE-2023-TEST-REAL",
            source="test",
            title="Test vulnerability for real connection",
            description="This is a test vulnerability for real connection testing",
            severity="高",
            published_date=datetime.now().isoformat()
        )
        
        component = ComponentNode(
            name="test-component-real",
            ecosystem="npm",
            platform="javascript"
        )
        
        version = VersionNode(
            component_name="test-component-real",
            version="1.0.0"
        )
        
        patch = PatchNode(
            patch_id="PATCH-2023-TEST-REAL",
            url="https://example.com/patch",
            description="Test patch for real connection testing"
        )
        
        # 创建节点
        logger.info("创建节点...")
        result = graph_store.create_node(vuln)
        logger.info(f"创建漏洞节点结果: {result}")
        
        result = graph_store.create_node(component)
        logger.info(f"创建组件节点结果: {result}")
        
        result = graph_store.create_node(version)
        logger.info(f"创建版本节点结果: {result}")
        
        result = graph_store.create_node(patch)
        logger.info(f"创建补丁节点结果: {result}")
        
        # 创建关系
        logger.info("创建关系...")
        affects_relation = AffectsRelation(
            vuln_id="CVE-2023-TEST-REAL",
            component_id="test-component-real",
            affected_versions=["1.0.0"]
        )
        result = graph_store.create_relation(affects_relation)
        logger.info(f"创建影响关系结果: {result}")
        
        has_version_relation = HasVersionRelation(
            component_id="test-component-real",
            version_id="test-component-real@1.0.0"
        )
        result = graph_store.create_relation(has_version_relation)
        logger.info(f"创建版本关系结果: {result}")
        
        fixed_by_relation = FixedByRelation(
            vuln_id="CVE-2023-TEST-REAL",
            patch_id="PATCH-2023-TEST-REAL"
        )
        result = graph_store.create_relation(fixed_by_relation)
        logger.info(f"创建修复关系结果: {result}")
        
        # 查询节点
        logger.info("查询节点...")
        vuln_node = graph_store.get_node(NodeType.VULNERABILITY, "CVE-2023-TEST-REAL")
        logger.info(f"漏洞节点: {vuln_node}")
        
        # 查询关系
        logger.info("查询关系...")
        affects_rel = graph_store.get_relation(
            RelationType.AFFECTS,
            "CVE-2023-TEST-REAL",
            "test-component-real"
        )
        logger.info(f"影响关系: {affects_rel}")
        
        # 清理测试数据
        logger.info("清理测试数据...")
        graph_store.delete_relation(
            RelationType.FIXED_BY,
            "CVE-2023-TEST-REAL",
            "PATCH-2023-TEST-REAL"
        )
        graph_store.delete_relation(
            RelationType.HAS_VERSION,
            "test-component-real",
            "test-component-real@1.0.0"
        )
        graph_store.delete_relation(
            RelationType.AFFECTS,
            "CVE-2023-TEST-REAL",
            "test-component-real"
        )
        
        graph_store.delete_node(NodeType.PATCH, "PATCH-2023-TEST-REAL")
        graph_store.delete_node(NodeType.VERSION, "test-component-real@1.0.0")
        graph_store.delete_node(NodeType.COMPONENT, "test-component-real")
        graph_store.delete_node(NodeType.VULNERABILITY, "CVE-2023-TEST-REAL")
        
        # 关闭连接
        graph_store.close()
        
        logger.info("GraphStore测试完成")
        return True
    except Exception as e:
        logger.error(f"GraphStore测试失败: {str(e)}")
        return False

def test_path_analyzer():
    """测试PathAnalyzer服务（实际连接）"""
    logger.info("===== 测试PathAnalyzer服务（实际连接） =====")
    
    try:
        # 初始化图数据库服务
        graph_store = GraphStore(
            uri=NEO4J_URI,
            username=NEO4J_USER,
            password=NEO4J_PASSWORD,
            test_mode=False  # 使用实际连接
        )
        logger.info("GraphStore初始化成功")
        
        # 初始化路径分析服务
        path_analyzer = PathAnalyzer(graph_store)
        logger.info("PathAnalyzer初始化成功")
        
        # 创建测试数据
        vuln = VulnerabilityNode(
            vuln_id="CVE-2023-PATH-TEST-REAL",
            source="test",
            title="Test path vulnerability for real connection",
            description="This is a test vulnerability for path analysis with real connection",
            severity="高",
            published_date=datetime.now().isoformat()
        )
        
        component1 = ComponentNode(
            name="component-a-real",
            ecosystem="npm",
            platform="javascript"
        )
        
        component2 = ComponentNode(
            name="component-b-real",
            ecosystem="npm",
            platform="javascript"
        )
        
        component3 = ComponentNode(
            name="component-c-real",
            ecosystem="npm",
            platform="javascript"
        )
        
        # 创建节点
        logger.info("创建测试节点...")
        graph_store.create_node(vuln)
        graph_store.create_node(component1)
        graph_store.create_node(component2)
        graph_store.create_node(component3)
        
        # 创建关系
        logger.info("创建测试关系...")
        # 漏洞影响组件A
        affects_relation = AffectsRelation(
            vuln_id="CVE-2023-PATH-TEST-REAL",
            component_id="component-a-real"
        )
        graph_store.create_relation(affects_relation)
        
        # 组件B依赖组件A
        depends_relation1 = DependsOnRelation(
            source_component_id="component-b-real",
            target_component_id="component-a-real"
        )
        graph_store.create_relation(depends_relation1)
        
        # 组件C依赖组件B
        depends_relation2 = DependsOnRelation(
            source_component_id="component-c-real",
            target_component_id="component-b-real"
        )
        graph_store.create_relation(depends_relation2)
        
        # 测试漏洞影响分析
        logger.info("测试漏洞影响分析...")
        impact_result = path_analyzer.analyze_vulnerability_impact("CVE-2023-PATH-TEST-REAL")
        logger.info(f"漏洞影响分析结果: {impact_result}")
        
        # 测试组件依赖链分析
        logger.info("测试组件依赖链分析...")
        deps_result = path_analyzer.analyze_component_dependencies("component-a-real", "incoming")
        logger.info(f"组件依赖链分析结果: {deps_result}")
        
        # 测试漏洞传播路径分析
        logger.info("测试漏洞传播路径分析...")
        paths_result = path_analyzer.find_vulnerability_paths("CVE-2023-PATH-TEST-REAL", "component-c-real")
        logger.info(f"漏洞传播路径分析结果: {paths_result}")
        
        # 清理测试数据
        logger.info("清理测试数据...")
        graph_store.delete_relation(
            RelationType.DEPENDS_ON,
            "component-c-real",
            "component-b-real"
        )
        graph_store.delete_relation(
            RelationType.DEPENDS_ON,
            "component-b-real",
            "component-a-real"
        )
        graph_store.delete_relation(
            RelationType.AFFECTS,
            "CVE-2023-PATH-TEST-REAL",
            "component-a-real"
        )
        
        graph_store.delete_node(NodeType.COMPONENT, "component-c-real")
        graph_store.delete_node(NodeType.COMPONENT, "component-b-real")
        graph_store.delete_node(NodeType.COMPONENT, "component-a-real")
        graph_store.delete_node(NodeType.VULNERABILITY, "CVE-2023-PATH-TEST-REAL")
        
        # 关闭连接
        graph_store.close()
        
        logger.info("PathAnalyzer测试完成")
        return True
    except Exception as e:
        logger.error(f"PathAnalyzer测试失败: {str(e)}")
        return False

def test_analyzer_service():
    """测试AnalyzerService（实际连接）"""
    logger.info("===== 测试AnalyzerService（实际连接） =====")
    
    try:
        # 初始化分析服务
        analyzer = AnalyzerService(
            db_url="sqlite:///data/vulnerabilities.db",
            graph_url=NEO4J_URI,
            graph_user=NEO4J_USER,
            graph_password=NEO4J_PASSWORD,
            test_mode=False  # 使用实际连接
        )
        logger.info("AnalyzerService初始化完成")
        
        # 检查图数据库服务是否成功初始化
        if analyzer.graph_store is None:
            logger.error("图数据库服务初始化失败")
            return False
        
        if analyzer.path_analyzer is None:
            logger.error("路径分析服务初始化失败")
            return False
            
        logger.info("图数据库和路径分析服务初始化成功")
        
        # 创建测试数据
        vuln = VulnerabilityNode(
            vuln_id="CVE-2023-ANALYZER-TEST-REAL",
            source="test",
            title="Test analyzer vulnerability for real connection",
            description="This is a test vulnerability for analyzer service with real connection",
            severity="高",
            published_date=datetime.now().isoformat()
        )
        
        component = ComponentNode(
            name="analyzer-component-real",
            ecosystem="npm",
            platform="javascript"
        )
        
        # 创建节点
        logger.info("创建测试节点...")
        analyzer.graph_store.create_node(vuln)
        analyzer.graph_store.create_node(component)
        
        # 创建关系
        logger.info("创建测试关系...")
        affects_relation = AffectsRelation(
            vuln_id="CVE-2023-ANALYZER-TEST-REAL",
            component_id="analyzer-component-real"
        )
        analyzer.graph_store.create_relation(affects_relation)
        
        # 测试漏洞影响范围分析
        logger.info("测试漏洞影响范围分析...")
        impact_result = analyzer.analyze_vulnerability_impact("CVE-2023-ANALYZER-TEST-REAL")
        logger.info(f"漏洞影响范围分析结果: {impact_result}")
        
        # 测试组件风险评估
        logger.info("测试组件风险评估...")
        risk_result = analyzer.assess_risk("analyzer-component-real")
        logger.info(f"组件风险评估结果: {risk_result}")
        
        # 清理测试数据
        logger.info("清理测试数据...")
        analyzer.graph_store.delete_relation(
            RelationType.AFFECTS,
            "CVE-2023-ANALYZER-TEST-REAL",
            "analyzer-component-real"
        )
        
        analyzer.graph_store.delete_node(NodeType.COMPONENT, "analyzer-component-real")
        analyzer.graph_store.delete_node(NodeType.VULNERABILITY, "CVE-2023-ANALYZER-TEST-REAL")
        
        logger.info("AnalyzerService测试完成")
        return True
    except Exception as e:
        logger.error(f"AnalyzerService测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    # 设置日志
    setup_logger()
    
    logger.info("开始使用实际Neo4j连接测试图数据库服务")
    logger.info(f"Neo4j连接参数: URI={NEO4J_URI}, 用户={NEO4J_USER}")
    
    # 测试GraphStore服务
    graph_store_result = test_graph_store()
    
    # 测试PathAnalyzer服务
    path_analyzer_result = test_path_analyzer()
    
    # 测试AnalyzerService
    analyzer_service_result = test_analyzer_service()
    
    # 打印测试结果
    logger.info("===== 测试结果 =====")
    logger.info(f"GraphStore服务: {'成功' if graph_store_result else '失败'}")
    logger.info(f"PathAnalyzer服务: {'成功' if path_analyzer_result else '失败'}")
    logger.info(f"AnalyzerService: {'成功' if analyzer_service_result else '失败'}")
    
    if graph_store_result and path_analyzer_result and analyzer_service_result:
        logger.info("所有测试均成功，图数据库相关功能正常工作")
        return 0
    else:
        logger.error("部分测试失败，请检查日志了解详情")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 