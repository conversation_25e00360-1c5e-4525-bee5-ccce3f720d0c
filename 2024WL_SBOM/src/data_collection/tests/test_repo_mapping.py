#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
仓库映射功能测试脚本

测试将非GitHub URL映射到官方GitHub仓库的功能。
"""

import json
import os
import sys
import logging
from typing import List, Dict, Optional, Tuple

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("repo_mapping_test")

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src', 'data_collection', 'services', 'patch_tracer')
if src_dir not in sys.path:
    sys.path.append(src_dir)
    
# 导入必要的模块
from utils import extract_commit_hash_from_url, find_best_repo_for_commit
from repo_mapping import verify_commit_in_repo, identify_project_from_url

def load_trace_result(file_path: str) -> Dict:
    """
    加载追踪结果JSON文件
    
    Args:
        file_path: JSO<PERSON>文件路径
        
    Returns:
        Dict: 加载的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载文件时出错: {str(e)}")
        return {}

def test_patch_url_mapping(patch_url: str, github_token: Optional[str] = None) -> Tuple[bool, Optional[Tuple[str, str, str]]]:
    """
    测试补丁URL映射
    
    Args:
        patch_url: 补丁URL
        github_token: GitHub API令牌(可选)
        
    Returns:
        Tuple[bool, Optional[Tuple[str, str, str]]]: (成功标志, 仓库信息)
    """
    logger.info(f"测试URL: {patch_url}")
    
    # 1. 从URL提取commit hash
    commit_hash = extract_commit_hash_from_url(patch_url)
    if not commit_hash:
        logger.error(f"无法从URL提取commit hash: {patch_url}")
        return False, None
    
    logger.info(f"提取的commit hash: {commit_hash}")
    
    # 2. 识别项目
    project_info = identify_project_from_url(patch_url)
    if not project_info:
        logger.warning(f"无法从URL识别项目: {patch_url}")
    else:
        owner, repo = project_info
        logger.info(f"识别到的项目: {owner}/{repo}")
        
    # 3. 查找最佳仓库
    repo_info = find_best_repo_for_commit(commit_hash, patch_url, github_token)
    if not repo_info:
        logger.error(f"无法为commit {commit_hash} 找到合适的仓库")
        return False, None
    
    owner, repo, github_url = repo_info
    logger.info(f"找到仓库: {owner}/{repo}")
    logger.info(f"GitHub URL: {github_url}")
    
    # 4. 验证commit存在性
    exists = verify_commit_in_repo(owner, repo, commit_hash, github_token)
    logger.info(f"Commit存在于仓库中: {exists}")
    
    return exists, repo_info

def main():
    """
    主函数
    """
    # 设置GitHub令牌（如果有）
    github_token = os.environ.get("GITHUB_TOKEN")
    
    # 加载测试文件
    test_file = os.path.join(current_dir, 'data', 'patch', 'CVE_2023_0286', 'trace_result.json')
    trace_result = load_trace_result(test_file)
    
    if not trace_result:
        logger.error("无法加载测试文件")
        return
    
    # 提取所有补丁URL
    patch_urls = [patch["url"] for patch in trace_result.get("all_patches", [])]
    logger.info(f"找到 {len(patch_urls)} 个补丁URL")
    
    # 测试结果
    success_count = 0
    fail_count = 0
    
    # 测试每个URL
    for url in patch_urls:
        success, repo_info = test_patch_url_mapping(url, github_token)
        if success:
            success_count += 1
        else:
            fail_count += 1
        
        logger.info("-" * 50)
    
    # 测试统计
    logger.info(f"测试完成: 成功 {success_count}/{len(patch_urls)}, 失败 {fail_count}/{len(patch_urls)}")
    
    # 如果还有等价补丁，也可以测试
    for url, eq_patch_info in trace_result.get("equivalent_patches", {}).items():
        logger.info(f"\n测试等价补丁组: {url}")
        
        # 测试等价补丁
        eq_patches = eq_patch_info.get("equivalent_patches", [])
        for patch in eq_patches:
            if "url" in patch and "github.com" in patch["url"]:
                # 验证GitHub等价补丁
                github_url = patch["url"]
                commit_sha = patch["sha"]
                
                logger.info(f"验证GitHub等价补丁: {github_url}")
                
                # 从URL提取所有者和仓库
                parts = github_url.split("github.com/")
                if len(parts) > 1:
                    path_parts = parts[1].split("/")
                    if len(path_parts) >= 3:
                        owner = path_parts[0]
                        repo = path_parts[1]
                        
                        # 验证commit是否存在
                        exists = verify_commit_in_repo(owner, repo, commit_sha, github_token)
                        logger.info(f"等价补丁存在: {exists}")
                
                logger.info("-" * 50)

if __name__ == "__main__":
    main() 