#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文本分析与组件关联服务测试脚本
"""

import os
import sys
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from src.services.text_analyzer import TextAnalyzer
    TEXT_ANALYZER_IMPORTED = True
except ImportError:
    TEXT_ANALYZER_IMPORTED = False
    print("警告: 无法导入TextAnalyzer，测试将被跳过")

from src.utils.logger import setup_logger, logger

def test_text_similarity():
    """测试文本相似度分析"""
    if not TEXT_ANALYZER_IMPORTED:
        logger.warning("跳过文本相似度分析测试: TextAnalyzer未导入")
        return False
    
    logger.info("===== 测试文本相似度分析 =====")
    
    try:
        # 初始化文本分析器
        text_analyzer = TextAnalyzer()
        logger.info("TextAnalyzer初始化成功")
        
        # 测试文本
        texts = [
            "A buffer overflow vulnerability in the HTTP/2 implementation",
            "HTTP/2 protocol implementation contains a buffer overflow vulnerability",
            "Cross-site scripting vulnerability in the login form",
            "SQL injection vulnerability in the search function"
        ]
        
        # 计算相似度
        logger.info("计算文本相似度...")
        similarity_matrix = text_analyzer.compute_text_similarity(texts)
        
        # 验证结果
        if similarity_matrix is not None and similarity_matrix.shape == (len(texts), len(texts)):
            logger.info(f"相似度矩阵计算成功，形状: {similarity_matrix.shape}")
            
            # 检查相似文本
            similar_pairs = text_analyzer.find_similar_texts(texts, threshold=0.5)
            logger.info(f"发现 {len(similar_pairs)} 对相似文本")
            
            for i, j, score in similar_pairs:
                logger.info(f"相似文本对 ({i}, {j}): {score:.4f}")
                logger.info(f"文本1: {texts[i]}")
                logger.info(f"文本2: {texts[j]}")
            
            logger.info("文本相似度分析测试完成")
            return True
        else:
            logger.error("相似度矩阵计算失败")
            return False
    except Exception as e:
        logger.error(f"文本相似度分析测试失败: {str(e)}")
        return False

def test_component_name_normalization():
    """测试组件名称标准化"""
    if not TEXT_ANALYZER_IMPORTED:
        logger.warning("跳过组件名称标准化测试: TextAnalyzer未导入")
        return False
    
    logger.info("===== 测试组件名称标准化 =====")
    
    try:
        # 初始化文本分析器
        text_analyzer = TextAnalyzer()
        
        # 测试组件名称
        component_names = [
            "log4j",
            "log4j-core",
            "org.apache.logging.log4j:log4j-core",
            "apache-log4j",
            "Apache Log4j",
            "spring-core",
            "org.springframework:spring-core"
        ]
        
        # 标准化名称
        logger.info("标准化组件名称...")
        normalized_names = [text_analyzer.normalize_component_name(name) for name in component_names]
        
        # 打印结果
        for original, normalized in zip(component_names, normalized_names):
            logger.info(f"原始名称: {original} -> 标准化: {normalized}")
        
        # 分组相似组件
        logger.info("分组相似组件...")
        component_groups = text_analyzer.group_similar_components(component_names)
        
        for i, group in enumerate(component_groups):
            logger.info(f"组件组 {i+1}: {group}")
        
        logger.info("组件名称标准化测试完成")
        return True
    except Exception as e:
        logger.error(f"组件名称标准化测试失败: {str(e)}")
        return False

def test_version_matching():
    """测试版本匹配算法"""
    if not TEXT_ANALYZER_IMPORTED:
        logger.warning("跳过版本匹配算法测试: TextAnalyzer未导入")
        return False
    
    logger.info("===== 测试版本匹配算法 =====")
    
    try:
        # 初始化文本分析器
        text_analyzer = TextAnalyzer()
        
        # 测试版本范围
        version_ranges = [
            ">=2.0.0",
            "<1.2.3",
            ">=1.0.0,<2.0.0",
            "~1.2.3",
            "^1.2.3"
        ]
        
        test_versions = [
            "1.0.0",
            "1.2.3",
            "1.2.4",
            "2.0.0",
            "2.1.0"
        ]
        
        # 测试版本匹配
        logger.info("测试版本匹配...")
        for range_str in version_ranges:
            logger.info(f"版本范围: {range_str}")
            for version in test_versions:
                is_affected = text_analyzer.is_version_affected(version, range_str)
                logger.info(f"  版本 {version}: {'受影响' if is_affected else '不受影响'}")
        
        logger.info("版本匹配算法测试完成")
        return True
    except Exception as e:
        logger.error(f"版本匹配算法测试失败: {str(e)}")
        return False

def test_component_extraction():
    """测试从文本中提取组件信息"""
    if not TEXT_ANALYZER_IMPORTED:
        logger.warning("跳过组件信息提取测试: TextAnalyzer未导入")
        return False
    
    logger.info("===== 测试组件信息提取 =====")
    
    try:
        # 初始化文本分析器
        text_analyzer = TextAnalyzer()
        
        # 测试文本
        texts = [
            "Vulnerability in Apache Log4j versions 2.0 to 2.15.0",
            "A remote code execution vulnerability in spring-core versions prior to 5.3.18",
            "cross-site scripting vulnerability in jquery versions <=1.9.0"
        ]
        
        # 提取组件信息
        logger.info("从文本中提取组件信息...")
        for text in texts:
            components = text_analyzer.extract_components_from_text(text)
            logger.info(f"文本: {text}")
            logger.info(f"提取的组件: {components}")
        
        logger.info("组件信息提取测试完成")
        return True
    except Exception as e:
        logger.error(f"组件信息提取测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    # 设置日志
    setup_logger()
    
    # 如果TextAnalyzer未导入，打印模板结构
    if not TEXT_ANALYZER_IMPORTED:
        print("\n===== TextAnalyzer模板结构 =====")
        print("""
#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
文本分析与组件关联服务

该服务负责:
1. 文本相似度分析
2. 组件名称标准化
3. 版本匹配算法
4. 从文本中提取组件信息
\"\"\"

import re
from typing import List, Tuple, Dict, Any, Optional, Set
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from packaging import version
from packaging.specifiers import SpecifierSet

class TextAnalyzer:
    \"\"\"文本分析与组件关联服务\"\"\"
    
    def __init__(self, min_similarity: float = 0.7):
        \"\"\"
        初始化文本分析器
        
        Args:
            min_similarity: 最小相似度阈值
        \"\"\"
        self.min_similarity = min_similarity
        self.vectorizer = TfidfVectorizer(
            stop_words='english',
            max_features=5000,
            ngram_range=(1, 2)
        )
    
    def compute_text_similarity(self, texts: List[str]) -> np.ndarray:
        \"\"\"
        计算文本之间的相似度
        
        Args:
            texts: 文本列表
            
        Returns:
            相似度矩阵
        \"\"\"
        # 实现文本相似度计算...
        pass
    
    def find_similar_texts(self, 
                         texts: List[str], 
                         threshold: Optional[float] = None) -> List[Tuple[int, int, float]]:
        \"\"\"
        查找相似的文本对
        
        Args:
            texts: 文本列表
            threshold: 相似度阈值，默认使用self.min_similarity
            
        Returns:
            相似文本对列表，每个元素为(i, j, score)
        \"\"\"
        # 实现相似文本查找...
        pass
    
    def normalize_component_name(self, name: str) -> str:
        \"\"\"
        标准化组件名称
        
        Args:
            name: 组件名称
            
        Returns:
            标准化后的名称
        \"\"\"
        # 实现组件名称标准化...
        pass
    
    def group_similar_components(self, components: List[str]) -> List[List[str]]:
        \"\"\"
        将相似的组件名称分组
        
        Args:
            components: 组件名称列表
            
        Returns:
            组件组列表
        \"\"\"
        # 实现组件分组...
        pass
    
    def is_version_affected(self, version_str: str, version_range: str) -> bool:
        \"\"\"
        检查版本是否受影响
        
        Args:
            version_str: 版本字符串
            version_range: 版本范围字符串
            
        Returns:
            是否受影响
        \"\"\"
        # 实现版本匹配...
        pass
    
    def extract_components_from_text(self, text: str) -> List[Dict[str, Any]]:
        \"\"\"
        从文本中提取组件信息
        
        Args:
            text: 输入文本
            
        Returns:
            组件信息列表，每个元素为包含name, version, ecosystem等信息的字典
        \"\"\"
        # 实现组件信息提取...
        pass
""")
    
    # 测试文本相似度分析
    text_similarity_result = test_text_similarity()
    
    # 测试组件名称标准化
    component_normalization_result = test_component_name_normalization()
    
    # 测试版本匹配算法
    version_matching_result = test_version_matching()
    
    # 测试组件信息提取
    component_extraction_result = test_component_extraction()
    
    # 打印测试结果
    logger.info("===== 测试结果 =====")
    
    if TEXT_ANALYZER_IMPORTED:
        logger.info(f"文本相似度分析: {'成功' if text_similarity_result else '失败'}")
        logger.info(f"组件名称标准化: {'成功' if component_normalization_result else '失败'}")
        logger.info(f"版本匹配算法: {'成功' if version_matching_result else '失败'}")
        logger.info(f"组件信息提取: {'成功' if component_extraction_result else '失败'}")
        
        if (text_similarity_result and component_normalization_result and
            version_matching_result and component_extraction_result):
            logger.info("所有测试均成功，TextAnalyzer功能完整")
            return 0
        else:
            logger.error("部分测试失败，请检查日志了解详情")
            return 1
    else:
        logger.info("TextAnalyzer未实现，请参考上面的模板结构创建该服务")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 