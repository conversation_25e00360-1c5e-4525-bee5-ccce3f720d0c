#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD采集器测试脚本

用于测试从NVD获取特定CVE数据的功能
"""

import json
import os
import sys
import requests
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from src.collectors.nvd import NVDCollector
from src.utils.logger import setup_logger

# 设置日志
setup_logger()
logger = logging.getLogger(__name__)

def test_nvd_specific_cve():
    """测试NVD采集器获取特定CVE的功能"""
    # 读取配置文件
    try:
        with open('config/sources.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
            nvd_config = config.get('sources', {}).get('NVD', {})
    except Exception as e:
        logger.error(f"读取配置文件失败: {str(e)}")
        return False

    # 直接通过NVD API查询特定CVE
    target_cve = "CVE-2021-34527"
    logger.info(f"开始测试获取 {target_cve} 的数据")
    
    # NVD API URL
    url = nvd_config.get('url', 'https://services.nvd.nist.gov/rest/json/cves/2.0')
    
    # 构建请求头和参数
    headers = {}
    api_key = nvd_config.get('api_key')
    if api_key and api_key != "YOUR-ACTUAL-API-KEY":
        headers['apiKey'] = api_key
    
    params = {
        'cveId': target_cve
    }
    
    # 发送请求
    try:
        response = requests.get(url, headers=headers, params=params, timeout=30)
        response.raise_for_status()  # 检查请求是否成功
        
        # 解析响应数据
        data = response.json()
        
        # 检查是否找到目标CVE
        vulnerabilities = data.get('vulnerabilities', [])
        if not vulnerabilities:
            logger.error(f"未找到 {target_cve} 的数据")
            return False
        
        # 获取第一个漏洞数据
        vuln_data = vulnerabilities[0].get('cve', {})
        
        # 检查返回的数据结构是否包含所有必要字段
        required_fields = [
            'id',                # CVE ID
            'descriptions',      # 描述
            'metrics',           # CVSS评分
            'references',        # 引用链接
            'configurations'     # CPE配置
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in vuln_data:
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"数据缺少必要字段: {', '.join(missing_fields)}")
            return False
        
        # 输出获取到的数据基本信息
        logger.info(f"成功获取 {target_cve} 的数据")
        
        # 保存原始数据以便检查
        with open(f"{target_cve}_data.json", 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"原始数据已保存到 {target_cve}_data.json")
        
        # 获取描述
        descriptions = vuln_data.get('descriptions', [])
        if descriptions:
            for desc in descriptions:
                if desc.get('lang') == 'en':
                    logger.info(f"描述: {desc.get('value')}")
                    break
        
        # 获取CVSS评分
        metrics = vuln_data.get('metrics', {})
        cvss_v3 = metrics.get('cvssMetricV31', []) or metrics.get('cvssMetricV30', [])
        if cvss_v3:
            cvss_data = cvss_v3[0].get('cvssData', {})
            logger.info(f"CVSS v3评分: {cvss_data.get('baseScore')}")
            logger.info(f"严重程度: {cvss_data.get('baseSeverity')}")
        
        # 获取引用链接数量
        references = vuln_data.get('references', [])
        logger.info(f"引用链接数量: {len(references)}")
        
        # 获取CPE配置数量
        configurations = data.get('vulnerabilities', [{}])[0].get('configurations', [])
        logger.info(f"CPE配置数量: {len(configurations)}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        logger.error(f"请求失败: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"处理数据失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_nvd_specific_cve()
    if success:
        print("\n测试结果: 通过 ✓")
        print("NVD结构化漏洞信息获取功能正常工作，能够获取指定CVE的完整数据")
    else:
        print("\n测试结果: 失败 ✗")
        print("NVD结构化漏洞信息获取功能存在问题，请检查日志了解详情") 