#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化版Debian漏洞采集器测试脚本
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入收集器
try:
    from src.collectors.debian import DebianCollector
    logger.info("成功导入DebianCollector")
except Exception as e:
    logger.error(f"导入DebianCollector失败: {e}")
    sys.exit(1)

def main():
    try:
        # 创建简单配置
        debian_config = {
            "url": "https://security-tracker.debian.org",
            "delay_between_requests": 3,
            "max_retries": 3,
            "timeout": 30,
            "proxy": None
        }
        
        # 初始化Debian采集器
        logger.info("初始化Debian漏洞采集器")
        collector = DebianCollector(debian_config)
        
        # 设置日期范围（最近7天的数据）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        
        # 获取数据
        logger.info(f"开始获取Debian漏洞数据 (时间范围: {start_date} 至 {end_date})")
        
        # 注意：这里使用硬编码的start_date和end_date，避免任何潜在问题
        fixed_start = datetime(2023, 1, 1)
        fixed_end = datetime(2023, 1, 31)
        logger.info(f"使用固定时间范围: {fixed_start} 至 {fixed_end}")
        
        vulnerabilities = collector.fetch_data(fixed_start, fixed_end)
        
        # 检查是否成功获取数据
        if vulnerabilities:
            logger.info(f"成功获取 {len(vulnerabilities)} 条Debian漏洞数据")
            
            # 输出前2条数据的基本信息
            for i, vuln in enumerate(vulnerabilities[:2]):
                logger.info(f"漏洞 #{i+1}: {vuln.get('id', 'N/A')}")
                
            return True
        else:
            logger.error("未能获取Debian漏洞数据")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n测试结果: 通过 ✓")
        print("Debian漏洞信息获取功能正常工作")
    else:
        print("\n测试结果: 失败 ✗")
        print("Debian漏洞信息获取功能存在问题，请检查日志") 