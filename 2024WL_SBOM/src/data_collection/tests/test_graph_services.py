#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图数据库服务测试脚本 (测试模式)

在测试模式下验证GraphStore、PathAnalyzer和AnalyzerService
"""

import os
import sys
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from src.services.graph_store import GraphStore
from src.services.path_analyzer import PathAnalyzer
from src.services.analyzer import AnalyzerService
from src.models.graph import (
    VulnerabilityNode, ComponentNode, VersionNode, PatchNode,
    AffectsRelation, HasVersionRelation, FixedByRelation, DependsOnRelation,
    NodeType, RelationType
)
from src.utils.logger import setup_logger, logger

def test_graph_store():
    """测试GraphStore服务 (测试模式)"""
    logger.info("===== 测试GraphStore服务 (测试模式) =====")
    
    try:
        # 初始化图数据库服务 (测试模式)
        graph_store = GraphStore(
            uri="bolt://localhost:7687",
            username="neo4j",
            password="password",
            test_mode=True  # 启用测试模式
        )
        logger.info("GraphStore初始化成功 (测试模式)")
        
        # 检查连接状态
        if graph_store.is_connected():
            logger.info("GraphStore连接状态: 已连接 (测试模式)")
        else:
            logger.warning("GraphStore连接状态: 未连接")
        
        # 创建测试数据
        vuln = VulnerabilityNode(
            vuln_id="CVE-2023-TEST",
            source="test",
            title="Test vulnerability",
            description="This is a test vulnerability",
            severity="高",
            published_date=datetime.now().isoformat()
        )
        
        component = ComponentNode(
            name="test-component",
            ecosystem="npm",
            platform="javascript"
        )
        
        # 创建节点 (测试模式)
        logger.info("创建节点... (测试模式)")
        result = graph_store.create_node(vuln)
        logger.info(f"创建漏洞节点结果: {result}")
        
        result = graph_store.create_node(component)
        logger.info(f"创建组件节点结果: {result}")
        
        # 创建关系 (测试模式)
        logger.info("创建关系... (测试模式)")
        affects_relation = AffectsRelation(
            vuln_id="CVE-2023-TEST",
            component_id="test-component",
            affected_versions=["1.0.0"]
        )
        result = graph_store.create_relation(affects_relation)
        logger.info(f"创建影响关系结果: {result}")
        
        # 关闭连接
        graph_store.close()
        
        logger.info("GraphStore测试完成 (测试模式)")
        return True
    except Exception as e:
        logger.error(f"GraphStore测试失败: {str(e)}")
        return False

def test_path_analyzer():
    """测试PathAnalyzer服务 (测试模式)"""
    logger.info("===== 测试PathAnalyzer服务 (测试模式) =====")
    
    try:
        # 初始化图数据库服务 (测试模式)
        graph_store = GraphStore(
            uri="bolt://localhost:7687",
            username="neo4j",
            password="password",
            test_mode=True  # 启用测试模式
        )
        logger.info("Graph Store初始化成功 (测试模式)")
        
        # 初始化路径分析服务
        path_analyzer = PathAnalyzer(graph_store)
        logger.info("Path Analyzer初始化成功 (依赖于测试模式的GraphStore)")
        
        # 注意: 在测试模式下，无法获取实际的分析结果
        # 但我们可以验证服务是否能够正确初始化
        logger.info("路径分析器已成功初始化，无法在测试模式下执行实际的路径分析操作")
        
        graph_store.close()
        logger.info("PathAnalyzer测试完成 (测试模式)")
        return True
    except Exception as e:
        logger.error(f"PathAnalyzer测试失败: {str(e)}")
        return False

def test_analyzer_service():
    """测试AnalyzerService (测试模式)"""
    logger.info("===== 测试AnalyzerService (测试模式) =====")
    
    try:
        # 初始化分析服务
        analyzer = AnalyzerService(
            db_url="sqlite:///data/vulnerabilities.db",
            graph_url="bolt://localhost:7687",
            graph_user="neo4j",
            graph_password="password",
            test_mode=True  # 启用测试模式
        )
        logger.info("AnalyzerService初始化完成 (测试模式)")
        
        # 检查graph_store是否为None
        if analyzer.graph_store is None:
            logger.error("图数据库服务初始化失败 (应该成功)")
            return False
        else:
            logger.info("图数据库服务初始化成功")
        
        # 检查path_analyzer是否为None
        if analyzer.path_analyzer is None:
            logger.error("路径分析服务初始化失败 (应该成功)")
            return False
        else:
            logger.info("路径分析服务初始化成功")
        
        # 尝试调用一些分析功能
        logger.info("尝试调用分析功能...")
        
        # 尝试调用漏洞影响分析
        vuln_id = "CVE-2023-TEST"
        impact_result = analyzer.analyze_vulnerability_impact(vuln_id)
        logger.info(f"漏洞影响分析调用结果: {impact_result is not None}")
        
        # 尝试调用组件风险评估
        component_name = "test-component"
        risk_result = analyzer.assess_risk(component_name)
        logger.info(f"组件风险评估调用结果: {risk_result is not None}")
        
        logger.info("AnalyzerService测试完成 (测试模式)")
        return True
    except Exception as e:
        logger.error(f"AnalyzerService测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    # 设置日志
    setup_logger()
    
    # 测试GraphStore服务 (测试模式)
    graph_store_result = test_graph_store()
    
    # 测试PathAnalyzer服务 (测试模式)
    path_analyzer_result = test_path_analyzer()
    
    # 测试AnalyzerService (测试模式)
    analyzer_service_result = test_analyzer_service()
    
    # 打印测试结果
    logger.info("===== 测试结果 (测试模式) =====")
    logger.info(f"GraphStore服务: {'成功' if graph_store_result else '失败'}")
    logger.info(f"PathAnalyzer服务: {'成功' if path_analyzer_result else '失败'}")
    logger.info(f"AnalyzerService: {'成功' if analyzer_service_result else '失败'}")
    
    if graph_store_result and path_analyzer_result and analyzer_service_result:
        logger.info("所有测试均成功，服务可以正确初始化 (测试模式)")
        logger.info("注意: 这仅验证了服务能否正确初始化，没有测试实际的数据库操作")
        return 0
    else:
        logger.error("部分测试失败，请检查日志了解详情")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 