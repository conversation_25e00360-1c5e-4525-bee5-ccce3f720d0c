#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到Python路径
sys.path.append('/home')

# 动态导入，避免使用数字开头的模块名称
import importlib
utils_module = importlib.import_module('2024WL_SBOM.src.data_collection.services.patch_tracer.utils')
extract_commit_hash_from_url = utils_module.extract_commit_hash_from_url

# 测试所有支持的URL格式
test_urls = [
    # OpenSSL格式
    'https://git.openssl.org/gitweb/?p=openssl.git;a=commitdiff;h=fd2af07dc083a350c959147097003a14a5e8ac4d',
    
    # GitHub格式
    'https://github.com/openssl/openssl/commit/fd2af07dc083a350c959147097003a14a5e8ac4d',
    
    # Linux kernel格式
    'https://git.kernel.org/pub/scm/linux/kernel/git/torvalds/linux.git/commit/?id=abcdef1234567890',
    
    # Apache格式
    'https://gitbox.apache.org/repos/asf?p=httpd.git;a=commit;h=9c0774b88219f93b7157ed434d73ba2a8069571f',
    
    # GNOME格式
    'https://gitlab.gnome.org/GNOME/gtk/-/commit/a71c9d51aba50b9a63b49a7b86cadbd99a7e0a06',
    
    # 常规格式
    'https://example.org/repo/project/commit/1234567890abcdef'
]

# 测试并打印结果
for url in test_urls:
    hash_value = extract_commit_hash_from_url(url)
    print(f"\nURL: {url}")
    print(f"提取的hash: {hash_value}")

# 测试无法提取hash的URL
invalid_urls = [
    'https://github.com/openssl/openssl',
    'https://example.org/page.html',
    'https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2023-0286'
]

print("\n无法提取hash的URL:")
for url in invalid_urls:
    hash_value = extract_commit_hash_from_url(url)
    print(f"URL: {url} -> {hash_value}") 