# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.project
.pydevproject
.settings/

# 测试
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
htmlcov/

# 文档
docs/_build/
docs/api/

# 日志
*.log
logs/
log/

# 数据
data/
*.db
*.sqlite3
*.json
!config/*.json.example

# 系统
.DS_Store
Thumbs.db
desktop.ini

# 临时文件
*.tmp
*.bak
*.swp
*.pid
*.seed
*.pid.lock

# 配置文件
config/*.json
!config/*.json.example
.env
.env.*

# 编译文件
*.pyc
*.pyo
*.pyd
*.so
*.dll
*.dylib

# 打包文件
*.tar.gz
*.zip
*.rar
*.7z

# 性能分析
*.prof
*.lprof
*.stats

# 调试文件
.ipynb_checkpoints/
*.ipynb

# 其他
*.bak
*.swp
*.swo
*~
.DS_Store
Thumbs.db
.data/