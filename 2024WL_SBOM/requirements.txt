# 基础HTTP/网络请求
requests>=2.25.0
urllib3>=1.26.0
aiohttp>=3.9.1
asyncio>=3.4.3

# Web抓取和浏览器自动化
beautifulsoup4>=4.9.3
lxml>=4.9.3
playwright>=1.41.0
playwright-stealth>=1.0.6
selenium>=4.16.0
webdriver-manager>=4.0.1

# 数据处理和分析
pandas>=2.1.4
numpy>=1.26.3
python-dateutil>=2.8.1
networkx>=2.5
scikit-learn>=1.4.0

# 数据库
sqlalchemy>=2.0.25
neo4j>=5.18.0
sqlite3  # 通常作为Python标准库的一部分

# 图形可视化
matplotlib>=3.3.0

# 自然语言处理
nltk>=3.8.1
spacy>=3.7.2

# 配置和数据验证
pyyaml>=6.0.1
pydantic>=2.5.3
certifi>=2021.5.30

# 日志管理
loguru>=0.7.2

# 数据导出/格式化
openpyxl>=3.1.2
markdown2>=2.4.10
jinja2>=3.1.2

# API集成
pygithub>=1.55  # GitHub API
openai>=1.13.3  # OpenAI API (用于LLM生成POC)

# 测试
pytest>=7.4.4
pytest-cov>=4.1.0
pytest-mock>=3.12.0

# 代码质量
flake8>=7.0.0
black>=23.12.1
isort>=5.13.2
mypy>=1.8.0

# 文档
sphinx>=7.2.6
sphinx-rtd-theme>=2.0.0

# 开发工具
ipython>=8.18.1
jupyter>=1.0.0

# 系统工具
psutil>=5.9.7  # 系统资源监控
tqdm>=4.66.1   # 进度条支持

# 多线程/并发
concurrent-log-handler>=0.9.24  # 线程安全日志处理 