#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD补丁对收集主程序

收集2011-2025年的NVD补丁对数据，应用mystique筛选标准
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from pathlib import Path

# 添加项目根目录到sys.path
sys.path.insert(0, "/home/<USER>")

from services.nvd_patch_collector import NVDPatchPairCollector

def setup_logging(log_level: str = "INFO", log_file: str = None):
    """设置日志配置"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 配置根日志记录器
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 如果指定了日志文件，添加文件处理器
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="NVD补丁对收集器 - 收集2011-2025年的补丁对数据",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 收集所有年份的数据
  python main.py --start-year 2011 --end-year 2025 --output-dir ./output
  
  # 收集特定年份的数据，限制每年最大CVE数量
  python main.py --start-year 2020 --end-year 2023 --max-cves-per-year 100
  
  # 使用自定义配置文件
  python main.py --config-path /path/to/sources.json --output-dir ./results
        """
    )
    
    # 基本参数
    parser.add_argument(
        '--start-year', 
        type=int, 
        default=2011,
        help='开始年份 (默认: 2011)'
    )
    parser.add_argument(
        '--end-year', 
        type=int, 
        default=2025,
        help='结束年份 (默认: 2025)'
    )
    parser.add_argument(
        '--output-dir', 
        type=str, 
        default='./output',
        help='输出目录 (默认: ./output)'
    )
    parser.add_argument(
        '--config-path', 
        type=str, 
        default='/home/<USER>/src/data_collection/config/sources.json',
        help='配置文件路径'
    )
    
    # 限制参数
    parser.add_argument(
        '--max-cves-per-year', 
        type=int, 
        default=None,
        help='每年最大CVE数量限制 (默认: 无限制)'
    )
    parser.add_argument(
        '--similarity-threshold', 
        type=float, 
        default=0.5,
        help='mystique相似度阈值 (默认: 0.5)'
    )
    
    # 日志参数
    parser.add_argument(
        '--log-level', 
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    parser.add_argument(
        '--log-file', 
        type=str, 
        default=None,
        help='日志文件路径 (默认: 仅控制台输出)'
    )
    
    # 模式参数
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='试运行模式，不实际收集数据'
    )
    parser.add_argument(
        '--resume', 
        action='store_true',
        help='恢复模式，跳过已收集的CVE'
    )
    
    args = parser.parse_args()
    
    # 验证参数
    if args.start_year > args.end_year:
        print("错误: 开始年份不能大于结束年份")
        return 1
    
    if not os.path.exists(args.config_path):
        print(f"错误: 配置文件不存在: {args.config_path}")
        return 1
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    log_file = args.log_file
    if not log_file:
        log_file = output_dir / f"nvd_patch_collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    setup_logging(args.log_level, str(log_file))
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 60)
    logger.info("NVD补丁对收集器启动")
    logger.info("=" * 60)
    logger.info(f"收集年份范围: {args.start_year} - {args.end_year}")
    logger.info(f"输出目录: {output_dir.absolute()}")
    logger.info(f"配置文件: {args.config_path}")
    logger.info(f"相似度阈值: {args.similarity_threshold}")
    if args.max_cves_per_year:
        logger.info(f"每年最大CVE数量: {args.max_cves_per_year}")
    logger.info(f"试运行模式: {'是' if args.dry_run else '否'}")
    logger.info("=" * 60)
    
    if args.dry_run:
        logger.info("试运行模式，将模拟收集过程但不保存数据")
        return 0
    
    try:
        # 初始化收集器
        logger.info("初始化NVD补丁对收集器...")
        collector = NVDPatchPairCollector(
            config_path=args.config_path,
            output_dir=str(output_dir)
        )
        
        # 设置相似度阈值
        collector.mystique_filter.similarity_threshold = args.similarity_threshold
        
        # 检查GitHub token状态
        valid_tokens = collector.token_manager.get_valid_tokens_count()
        logger.info(f"可用的GitHub tokens: {valid_tokens}")
        
        if valid_tokens == 0:
            logger.warning("没有可用的GitHub tokens，可能会遇到API限制")
        
        # 开始收集
        logger.info("开始收集补丁对数据...")
        start_time = datetime.now()
        
        yearly_results = collector.collect_patch_pairs_by_year_range(
            start_year=args.start_year,
            end_year=args.end_year,
            max_cves_per_year=args.max_cves_per_year
        )
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # 生成报告
        logger.info("生成收集报告...")
        report = collector.generate_collection_report(yearly_results)
        
        # 输出总结
        logger.info("=" * 60)
        logger.info("收集完成!")
        logger.info("=" * 60)
        logger.info(f"总耗时: {duration}")
        logger.info(f"总补丁对数量: {report['collection_summary']['total_patch_pairs']}")
        logger.info(f"覆盖年份: {len(report['yearly_breakdown'])} 年")
        logger.info(f"补丁类型分布: {report['patch_type_distribution']}")
        logger.info(f"补丁对类别分布: {report['category_distribution']}")
        logger.info("=" * 60)
        
        # 显示年度统计
        logger.info("年度统计:")
        for year, count in report['yearly_breakdown'].items():
            logger.info(f"  {year}: {count} 个补丁对")
        
        logger.info(f"详细报告已保存到输出目录: {output_dir.absolute()}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("收集过程被用户中断")
        return 1
    except Exception as e:
        logger.error(f"收集过程中发生错误: {str(e)}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())
