#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD补丁对收集器

基于现有的代码基础设施，收集2011-2025年的NVD补丁对数据
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Set

# 添加项目根目录到sys.path
sys.path.insert(0, "/home/<USER>")

from src.data_collection.collectors.nvd import NVDCollector
from src.data_collection.services.patch_tracer import PatchTracer
from src.data_collection.utils.token_manager import GitHubTokenManager
from src.data_collection.utils.logger import logger

# 导入我们新创建的模块
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from models.patch_pair import PatchPair, ProjectInfo, VersionInfo, ProjectVersions, PatchPairMetadata
from services.mystique_filter import MystiqueFilter

class NVDPatchPairCollector:
    """NVD补丁对收集器"""
    
    def __init__(self, config_path: str, output_dir: str):
        """
        初始化收集器
        
        Args:
            config_path: 配置文件路径
            output_dir: 输出目录
        """
        self.config_path = config_path
        self.output_dir = output_dir
        self.token_manager = GitHubTokenManager(config_path)
        self.mystique_filter = MystiqueFilter()
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化NVD收集器
        nvd_config = self.config.get('sources', {}).get('NVD', {})
        self.nvd_collector = NVDCollector(nvd_config)
        
        # 初始化补丁跟踪器
        self.patch_tracer = PatchTracer(
            output_dir=output_dir,
            github_token=self.token_manager.get_next_token(),
            confidence_threshold=0.7
        )
        
        # 存储已收集的CVE ID，避免重复
        self.collected_cve_ids: Set[str] = set()
        self.load_existing_cve_ids()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            return {}
    
    def load_existing_cve_ids(self):
        """加载已存在的CVE ID，避免重复收集"""
        # 从merged_dataset加载已有的CVE ID
        merged_dataset_paths = [
            "/home/<USER>/data/merged_dataset.json",
            "/home/<USER>/data/patch/merged_dataset.json",
            # 可以添加更多路径
        ]
        
        for path in merged_dataset_paths:
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            for item in data:
                                cve_id = item.get('cve_id')
                                if cve_id:
                                    self.collected_cve_ids.add(cve_id)
                        elif isinstance(data, dict):
                            cve_id = data.get('cve_id')
                            if cve_id:
                                self.collected_cve_ids.add(cve_id)
                    logger.info(f"从 {path} 加载了 {len(self.collected_cve_ids)} 个已存在的CVE ID")
                except Exception as e:
                    logger.warning(f"加载 {path} 失败: {str(e)}")
    
    def collect_cves_by_timerange(self, 
                                 start_date: datetime, 
                                 end_date: datetime,
                                 max_cves: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        按时间范围收集CVE数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_cves: 最大CVE数量限制
            
        Returns:
            CVE数据列表
        """
        logger.info(f"开始收集CVE数据: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        try:
            # 使用现有的NVD收集器
            cve_data = self.nvd_collector.fetch_data(start_date, end_date)
            
            if not cve_data:
                logger.warning("未获取到CVE数据")
                return []
            
            logger.info(f"获取到 {len(cve_data)} 条CVE数据")
            
            # 过滤已收集的CVE
            filtered_cves = []
            for cve in cve_data:
                cve_id = cve.get('id')
                if cve_id and cve_id not in self.collected_cve_ids:
                    filtered_cves.append(cve)
                    if max_cves and len(filtered_cves) >= max_cves:
                        break
            
            logger.info(f"过滤后剩余 {len(filtered_cves)} 条新CVE数据")
            return filtered_cves
            
        except Exception as e:
            logger.error(f"收集CVE数据失败: {str(e)}")
            return []
    
    def extract_cwe_categories(self, cve_data: Dict[str, Any]) -> List[str]:
        """
        提取CVE的CWE类别
        
        Args:
            cve_data: CVE数据
            
        Returns:
            CWE类别列表
        """
        cwe_categories = []
        
        # 从不同字段提取CWE信息
        weaknesses = cve_data.get('weaknesses', [])
        for weakness in weaknesses:
            for desc in weakness.get('description', []):
                value = desc.get('value', '')
                if value.startswith('CWE-'):
                    cwe_categories.append(value)
        
        # 也可以从描述中提取
        descriptions = cve_data.get('descriptions', [])
        for desc in descriptions:
            text = desc.get('value', '')
            import re
            cwe_matches = re.findall(r'CWE-\d+', text)
            cwe_categories.extend(cwe_matches)
        
        return list(set(cwe_categories))  # 去重
    
    def extract_patch_references_from_cve(self, cve_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从CVE数据中直接提取补丁引用

        Args:
            cve_data: CVE数据

        Returns:
            补丁引用列表
        """
        cve_id = cve_data.get('id', 'unknown')
        logger.info(f"从 {cve_id} 中提取补丁引用")

        patch_references = []

        # 从references字段提取补丁链接
        references = cve_data.get('references', [])

        for ref in references:
            url = ref.get('url', '')
            tags = ref.get('tags', [])

            # 检查是否是补丁相关的链接
            if self._is_patch_reference(url, tags):
                patch_ref = {
                    'url': url,
                    'tags': tags,
                    'source': ref.get('source', 'unknown'),
                    'cve_id': cve_id
                }
                patch_references.append(patch_ref)

        logger.info(f"为 {cve_id} 找到 {len(patch_references)} 个补丁引用")
        return patch_references

    def _is_patch_reference(self, url: str, tags: List[str]) -> bool:
        """
        判断是否是补丁相关的引用（只允许真正的commit链接）

        Args:
            url: 引用URL
            tags: 标签列表

        Returns:
            是否是补丁引用
        """
        # 只允许真正的commit URL
        return self._is_commit_url(url)

    def _is_commit_url(self, url: str) -> bool:
        """
        严格验证是否是commit URL

        Args:
            url: URL

        Returns:
            是否是commit URL
        """
        import re

        # GitHub commit URL模式
        github_commit_pattern = r'github\.com/[^/]+/[^/]+/commit/[a-f0-9]{7,40}'
        if re.search(github_commit_pattern, url.lower()):
            return True

        # GitLab commit URL模式
        gitlab_commit_pattern = r'gitlab\.com/[^/]+/[^/]+/-/commit/[a-f0-9]{7,40}'
        if re.search(gitlab_commit_pattern, url.lower()):
            return True

        # 其他git托管平台的commit模式
        generic_commit_pattern = r'/commit/[a-f0-9]{7,40}'
        if re.search(generic_commit_pattern, url.lower()):
            return True

        return False

    def is_cpp_related_cve(self, cve_data: Dict[str, Any]) -> bool:
        """
        判断CVE是否与C/C++相关

        Args:
            cve_data: CVE数据

        Returns:
            是否与C/C++相关
        """
        # 检查描述中是否包含C/C++相关关键词
        descriptions = cve_data.get('descriptions', [])
        cpp_keywords = [
            'c++', 'cpp', 'c programming', 'gcc', 'clang',
            'buffer overflow', 'memory corruption', 'segmentation fault',
            'use after free', 'double free', 'null pointer dereference',
            'stack overflow', 'heap overflow', 'linux kernel', 'glibc',
            'malloc', 'free()', 'strcpy', 'memcpy', 'sprintf'
        ]

        for desc in descriptions:
            text = desc.get('value', '').lower()
            if any(keyword in text for keyword in cpp_keywords):
                return True

        # 检查配置信息中的产品名称
        configurations = cve_data.get('configurations', [])

        # configurations可能是列表或字典
        if isinstance(configurations, dict):
            nodes = configurations.get('nodes', [])
        elif isinstance(configurations, list):
            nodes = configurations
        else:
            nodes = []

        for node in nodes:
            cpe_matches = node.get('cpeMatch', [])
            for cpe_match in cpe_matches:
                cpe23_uri = cpe_match.get('criteria', '').lower()
                # 检查是否包含C/C++相关的产品
                cpp_products = ['gcc', 'clang', 'glibc', 'musl', 'linux', 'kernel']
                if any(keyword in cpe23_uri for keyword in cpp_products):
                    return True

        return False
    
    def process_single_cve(self, cve_data: Dict[str, Any]) -> List[PatchPair]:
        """
        处理单个CVE，根据补丁数量和C/C++相关性决定是否保留

        Args:
            cve_data: CVE数据

        Returns:
            补丁对列表
        """
        cve_id = cve_data.get('id')
        if not cve_id:
            return []

        logger.info(f"处理CVE: {cve_id}")

        # 直接从CVE数据中提取补丁引用
        patch_references = self.extract_patch_references_from_cve(cve_data)

        if not patch_references:
            logger.info(f"{cve_id} 没有找到补丁引用")
            return []

        # 去重相同的commit URL
        unique_patch_refs = []
        seen_urls = set()
        for ref in patch_references:
            url = ref.get('url', '')
            if url not in seen_urls:
                unique_patch_refs.append(ref)
                seen_urls.add(url)

        patch_count = len(unique_patch_refs)
        logger.info(f"{cve_id} 找到 {patch_count} 个补丁引用（去重后）")

        # 更新patch_references为去重后的列表
        patch_references = unique_patch_refs

        # 检查是否与C/C++相关
        is_cpp_related = self.is_cpp_related_cve(cve_data)

        if patch_count == 1:
            # 只有1个补丁，必须是C/C++相关且是有效commit才保留作为备用
            patch_ref = patch_references[0]
            if self._is_commit_url(patch_ref.get('url', '')):
                if is_cpp_related:
                    logger.info(f"{cve_id} 有1个有效commit且与C/C++相关，保留作为备用")
                    return self._create_single_patch_backup(cve_data, patch_ref)
                else:
                    logger.info(f"{cve_id} 有1个有效commit但与C/C++无关，跳过")
                    return []
            else:
                logger.info(f"{cve_id} 有1个补丁但不是有效commit，跳过")
                return []

        elif patch_count >= 2:
            # 有2个或更多补丁，应用mystique筛选标准
            if is_cpp_related:
                logger.info(f"{cve_id} 有 {patch_count} 个补丁且与C/C++相关，应用mystique筛选")
                return self._apply_mystique_filtering(cve_data, patch_references)
            else:
                logger.info(f"{cve_id} 有 {patch_count} 个补丁但与C/C++无关，跳过")
                return []

        else:
            logger.info(f"{cve_id} 没有补丁引用")
            return []

    def _create_single_patch_backup(self, cve_data: Dict[str, Any], patch_ref: Dict[str, Any]) -> List[PatchPair]:
        """
        为单个补丁创建等价补丁备用记录（按照JSON格式要求）

        Args:
            cve_data: CVE数据
            patch_ref: 补丁引用

        Returns:
            补丁对列表（单个等价补丁记录）
        """
        cve_id = cve_data.get('id')
        patch_url = patch_ref.get('url', '')

        # 提取仓库信息
        repo_name = self._extract_repo_from_url(patch_url)
        project_name = repo_name.split('/')[-1] if repo_name else 'unknown'
        commit_hash = self._extract_commit_from_url(patch_url)

        # 创建等价补丁记录（source和target都指向同一个补丁，表示这是单个补丁的备用记录）
        projects = {
            'source': ProjectInfo(
                name=project_name,
                repo=patch_url,
                language="C"
            ),
            'target': ProjectInfo(
                name=project_name + "_equivalent",  # 标记为等价补丁
                repo=patch_url,
                language="C"
            )
        }

        versions = {
            'source': ProjectVersions(
                vulnerable=VersionInfo(version="unknown", commit="unknown"),
                patched=VersionInfo(version="unknown", commit=commit_hash)
            ),
            'target': ProjectVersions(
                vulnerable=VersionInfo(version="unknown", commit="unknown"),
                patched=VersionInfo(version="unknown", commit=commit_hash)  # 相同的commit，表示等价
            )
        }

        metadata = PatchPairMetadata(
            cwe_categories=self.extract_cwe_categories(cve_data),
            published_date=cve_data.get('published_date'),
            patch_pair_category="equivalent_patch_backup",
            confidence_score=0.8,  # 等价补丁的置信度较高
            mystique_criteria={
                'single_patch_backup': True,
                'equivalent_patch': True
            }
        )

        patch_pair = PatchPair(
            unified_id="",
            cve_id=cve_id,
            patch_type="equivalent_patch",  # 标记为等价补丁
            projects=projects,
            versions=versions,
            metadata=metadata
        )

        return [patch_pair]

    def _apply_mystique_filtering(self, cve_data: Dict[str, Any], patch_references: List[Dict[str, Any]]) -> List[PatchPair]:
        """
        对多个补丁应用mystique筛选标准

        Args:
            cve_data: CVE数据
            patch_references: 补丁引用列表

        Returns:
            筛选后的补丁对列表
        """
        cve_id = cve_data.get('id')

        # 简化的mystique筛选：检查补丁引用之间的关系
        filtered_pairs = []

        # 对于多个补丁引用，创建补丁对
        for i in range(len(patch_references)):
            for j in range(i + 1, len(patch_references)):
                source_ref = patch_references[i]
                target_ref = patch_references[j]

                # 检查是否满足基本的mystique条件
                if self._check_mystique_criteria(source_ref, target_ref, cve_id):
                    patch_pair = self._create_patch_pair_from_references(
                        cve_data, source_ref, target_ref
                    )
                    if patch_pair:
                        filtered_pairs.append(patch_pair)

        logger.info(f"{cve_id} 通过mystique筛选生成了 {len(filtered_pairs)} 个补丁对")
        return filtered_pairs

    def _check_mystique_criteria(self, ref1: Dict[str, Any], ref2: Dict[str, Any], cve_id: str = None) -> bool:
        """
        检查两个补丁引用是否满足mystique标准（简化条件，删除代码相似度要求）

        Args:
            ref1: 第一个补丁引用
            ref2: 第二个补丁引用
            cve_id: CVE ID（未使用，保持接口兼容性）

        Returns:
            是否满足标准
        """
        url1 = ref1.get('url', '')
        url2 = ref2.get('url', '')

        # 两个URL都必须是真正的commit链接
        if not (self._is_commit_url(url1) and self._is_commit_url(url2)):
            return False

        # 条件1: 来自不同的仓库（cross-repo）
        repo1 = self._extract_repo_from_url(url1)
        repo2 = self._extract_repo_from_url(url2)

        if repo1 and repo2 and repo1 != repo2:
            return True

        # 条件2: 同一仓库但不同commit
        if repo1 and repo2 and repo1 == repo2:
            commit1 = self._extract_commit_from_url(url1)
            commit2 = self._extract_commit_from_url(url2)
            if commit1 != commit2 and commit1 != "unknown" and commit2 != "unknown":
                return True

        return False

    def _extract_repo_from_url(self, url: str) -> str:
        """
        从GitHub URL中提取仓库名称

        Args:
            url: GitHub URL

        Returns:
            仓库名称
        """
        import re
        match = re.search(r'github\.com/([^/]+/[^/]+)', url)
        return match.group(1) if match else ""

    def _create_patch_pair_from_references(self,
                                         cve_data: Dict[str, Any],
                                         source_ref: Dict[str, Any],
                                         target_ref: Dict[str, Any]) -> Optional[PatchPair]:
        """
        从两个补丁引用创建补丁对

        Args:
            cve_data: CVE数据
            source_ref: 源补丁引用
            target_ref: 目标补丁引用

        Returns:
            补丁对对象
        """
        try:
            cve_id = cve_data.get('id')

            # 确定补丁类型
            source_repo = self._extract_repo_from_url(source_ref.get('url', ''))
            target_repo = self._extract_repo_from_url(target_ref.get('url', ''))
            patch_type = "cross_repo" if source_repo != target_repo else "same_repo"

            # 创建项目信息
            projects = {
                'source': ProjectInfo(
                    name=source_repo.split('/')[-1] if source_repo else 'unknown',
                    repo=source_ref.get('url', ''),
                    language="C"
                ),
                'target': ProjectInfo(
                    name=target_repo.split('/')[-1] if target_repo else 'unknown',
                    repo=target_ref.get('url', ''),
                    language="C"
                )
            }

            # 创建版本信息
            versions = {
                'source': ProjectVersions(
                    vulnerable=VersionInfo(version="unknown", commit="unknown"),
                    patched=VersionInfo(version="unknown", commit=self._extract_commit_from_url(source_ref.get('url', '')))
                ),
                'target': ProjectVersions(
                    vulnerable=VersionInfo(version="unknown", commit="unknown"),
                    patched=VersionInfo(version="unknown", commit=self._extract_commit_from_url(target_ref.get('url', '')))
                )
            }

            # 创建元数据
            metadata = PatchPairMetadata(
                cwe_categories=self.extract_cwe_categories(cve_data),
                published_date=cve_data.get('published_date'),
                confidence_score=0.7,  # 基于引用的置信度
                mystique_criteria={
                    'reference_based': True,
                    'cross_repo': patch_type == "cross_repo",
                    'same_repo': patch_type == "same_repo"
                }
            )

            # 创建补丁对
            patch_pair = PatchPair(
                unified_id="",
                cve_id=cve_id,
                patch_type=patch_type,
                projects=projects,
                versions=versions,
                metadata=metadata
            )

            # 确定补丁对类别
            patch_pair.determine_patch_pair_category()

            return patch_pair

        except Exception as e:
            logger.error(f"从引用创建补丁对失败: {str(e)}")
            return None

    def _extract_commit_from_url(self, url: str) -> str:
        """
        从URL中提取commit hash（严格验证）

        Args:
            url: 包含commit的URL

        Returns:
            commit hash
        """
        import re

        # 严格匹配commit URL中的hash
        commit_patterns = [
            r'/commit/([a-f0-9]{7,40})(?:[/?#]|$)',  # GitHub/GitLab commit URL
            r'/-/commit/([a-f0-9]{7,40})(?:[/?#]|$)',  # GitLab commit URL
        ]

        for pattern in commit_patterns:
            match = re.search(pattern, url.lower())
            if match:
                commit_hash = match.group(1)
                # 验证commit hash长度（至少7位，最多40位）
                if 7 <= len(commit_hash) <= 40:
                    return commit_hash

        return "unknown"
    
    def _create_patch_pair(self, 
                          cve_data: Dict[str, Any], 
                          target_patch: Dict[str, Any], 
                          source_patch: Dict[str, Any]) -> Optional[PatchPair]:
        """
        创建补丁对对象
        
        Args:
            cve_data: CVE数据
            target_patch: 目标补丁
            source_patch: 源补丁
            
        Returns:
            补丁对对象
        """
        try:
            cve_id = cve_data.get('id')
            
            # 确定补丁类型
            source_repo = source_patch.get('repo_url', '')
            target_repo = target_patch.get('repo_url', '')
            patch_type = "cross_repo" if source_repo != target_repo else "same_repo"
            
            # 创建项目信息
            projects = {
                'source': ProjectInfo(
                    name=source_patch.get('project_name', 'unknown'),
                    repo=source_repo,
                    language=source_patch.get('language', 'C')
                ),
                'target': ProjectInfo(
                    name=target_patch.get('project_name', 'unknown'),
                    repo=target_repo,
                    language=target_patch.get('language', 'C')
                )
            }
            
            # 创建版本信息
            versions = {
                'source': ProjectVersions(
                    vulnerable=VersionInfo(
                        version=source_patch.get('vulnerable_version', 'unknown'),
                        commit=source_patch.get('vulnerable_commit', 'unknown')
                    ),
                    patched=VersionInfo(
                        version=source_patch.get('patched_version', 'unknown'),
                        commit=source_patch.get('commit_sha', 'unknown')
                    )
                ),
                'target': ProjectVersions(
                    vulnerable=VersionInfo(
                        version=target_patch.get('vulnerable_version', 'unknown'),
                        commit=target_patch.get('vulnerable_commit', 'unknown')
                    ),
                    patched=VersionInfo(
                        version=target_patch.get('patched_version', 'unknown'),
                        commit=target_patch.get('commit_sha', 'unknown')
                    )
                )
            }
            
            # 创建元数据
            metadata = PatchPairMetadata(
                cwe_categories=self.extract_cwe_categories(cve_data),
                published_date=cve_data.get('published'),
                confidence_score=min(
                    source_patch.get('confidence', 0.0),
                    target_patch.get('confidence', 0.0)
                ),
                similarity_score=source_patch.get('similarity_score', 0.0),
                edit_distance_score=source_patch.get('edit_distance_score', 0.0),
                mystique_criteria=source_patch.get('mystique_criteria', {})
            )
            
            # 创建补丁对
            patch_pair = PatchPair(
                unified_id="",  # 将在__post_init__中生成
                cve_id=cve_id,
                patch_type=patch_type,
                projects=projects,
                versions=versions,
                metadata=metadata
            )
            
            # 确定补丁对类别
            patch_pair.determine_patch_pair_category()
            
            return patch_pair
            
        except Exception as e:
            logger.error(f"创建补丁对失败: {str(e)}")
            return None

    def save_patch_pairs_by_time(self, patch_pairs: List[PatchPair]):
        """
        按时间分类保存补丁对

        Args:
            patch_pairs: 补丁对列表
        """
        # 按需求说明的时间点分类
        time_categories = {
            "2023-08-23": datetime(2023, 8, 23),
            "2022-07-01": datetime(2022, 7, 1),
            "all": None  # 所有数据
        }

        categorized_data = {category: [] for category in time_categories.keys()}

        for patch_pair in patch_pairs:
            published_date_str = patch_pair.metadata.published_date
            if not published_date_str:
                continue

            try:
                # 解析发布日期
                if 'T' in published_date_str:
                    published_date = datetime.fromisoformat(published_date_str.replace('Z', '+00:00'))
                else:
                    published_date = datetime.strptime(published_date_str, '%Y-%m-%d')

                # 分类到对应的时间段
                for category, cutoff_date in time_categories.items():
                    if category == "all":
                        categorized_data[category].append(patch_pair)
                    elif cutoff_date and published_date <= cutoff_date:
                        categorized_data[category].append(patch_pair)

            except Exception as e:
                logger.warning(f"解析日期失败 {published_date_str}: {str(e)}")
                # 如果日期解析失败，只添加到all分类
                categorized_data["all"].append(patch_pair)

        # 保存各个分类的数据
        for category, pairs in categorized_data.items():
            if pairs:
                self._save_patch_pairs_to_file(pairs, category)

    def _save_patch_pairs_to_file(self, patch_pairs: List[PatchPair], category: str):
        """
        保存补丁对到文件

        Args:
            patch_pairs: 补丁对列表
            category: 分类名称
        """
        filename = f"nvd_patch_pairs_{category}_{datetime.now().strftime('%Y%m%d')}.json"
        filepath = os.path.join(self.output_dir, filename)

        try:
            # 转换为字典格式
            data = [pair.to_dict() for pair in patch_pairs]

            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            logger.info(f"保存了 {len(patch_pairs)} 个补丁对到 {filepath}")

        except Exception as e:
            logger.error(f"保存补丁对到 {filepath} 失败: {str(e)}")

    def collect_patch_pairs_by_year_range(self,
                                        start_year: int = 2011,
                                        end_year: int = 2025,
                                        max_cves_per_year: Optional[int] = None) -> Dict[str, List[PatchPair]]:
        """
        按年份范围收集补丁对

        Args:
            start_year: 开始年份
            end_year: 结束年份
            max_cves_per_year: 每年最大CVE数量

        Returns:
            按年份分组的补丁对字典
        """
        all_patch_pairs = []
        yearly_results = {}

        for year in range(start_year, end_year + 1):
            logger.info(f"开始收集 {year} 年的数据")

            year_patch_pairs = []
            year_cve_count = 0

            # 按月收集，避免时间范围过大
            for month in range(1, 13):
                if max_cves_per_year and year_cve_count >= max_cves_per_year:
                    break

                # 设置月份范围
                start_date = datetime(year, month, 1)
                if month == 12:
                    end_date = datetime(year, 12, 31)
                else:
                    end_date = datetime(year, month + 1, 1) - timedelta(days=1)

                logger.info(f"收集 {year}-{month:02d} 月的数据")

                # 计算本月最大CVE数量
                month_max_cves = None
                if max_cves_per_year:
                    remaining_cves = max_cves_per_year - year_cve_count
                    month_max_cves = min(remaining_cves, max_cves_per_year // 12 + 10)

                # 收集该月的CVE数据
                cve_data_list = self.collect_cves_by_timerange(
                    start_date, end_date, month_max_cves
                )

                year_cve_count += len(cve_data_list)

                # 处理该月的每个CVE
                for cve_data in cve_data_list:
                    try:
                        patch_pairs = self.process_single_cve(cve_data)
                        year_patch_pairs.extend(patch_pairs)

                        # 更新token（如果需要）
                        current_token = self.token_manager.get_next_token()
                        if current_token:
                            self.patch_tracer.github_token = current_token

                    except Exception as e:
                        cve_id = cve_data.get('id', 'unknown')
                        logger.error(f"处理CVE {cve_id} 失败: {str(e)}")
                        continue

            yearly_results[str(year)] = year_patch_pairs
            all_patch_pairs.extend(year_patch_pairs)

            logger.info(f"{year} 年收集到 {len(year_patch_pairs)} 个补丁对")

            # 每年立即保存结果，避免内存积累
            if year_patch_pairs:
                logger.info(f"保存 {year} 年的 {len(year_patch_pairs)} 个补丁对到文件")
                self._save_patch_pairs_to_file(year_patch_pairs, f"nvd_patch_pairs_{year}_")

            # 清理该年数据，释放内存
            del year_patch_pairs
            logger.info(f"{year} 年数据已保存并清理内存")

        # 保存所有补丁对的汇总（按时间分类）
        if all_patch_pairs:
            logger.info(f"保存所有年份汇总数据，共 {len(all_patch_pairs)} 个补丁对")
            self.save_patch_pairs_by_time(all_patch_pairs)

        return yearly_results

    def generate_collection_report(self, yearly_results: Dict[str, List[PatchPair]]) -> Dict[str, Any]:
        """
        生成收集报告

        Args:
            yearly_results: 按年份分组的结果

        Returns:
            收集报告
        """
        total_pairs = sum(len(pairs) for pairs in yearly_results.values())

        # 统计各种类型
        patch_type_stats = {"cross_repo": 0, "same_repo": 0, "equivalent_patch": 0}
        category_stats = {"cross_version": 0, "cross_branch": 0, "cross_repo": 0, "equivalent_patch_backup": 0}
        cwe_stats = {}

        for pairs in yearly_results.values():
            for pair in pairs:
                # 补丁类型统计
                patch_type_stats[pair.patch_type] += 1

                # 补丁对类别统计
                category = pair.metadata.patch_pair_category
                if category in category_stats:
                    category_stats[category] += 1

                # CWE统计
                for cwe in pair.metadata.cwe_categories:
                    cwe_stats[cwe] = cwe_stats.get(cwe, 0) + 1

        report = {
            "collection_summary": {
                "total_patch_pairs": total_pairs,
                "years_covered": list(yearly_results.keys()),
                "collection_date": datetime.now().isoformat()
            },
            "yearly_breakdown": {
                year: len(pairs) for year, pairs in yearly_results.items()
            },
            "patch_type_distribution": patch_type_stats,
            "category_distribution": category_stats,
            "top_cwe_categories": dict(sorted(cwe_stats.items(), key=lambda x: x[1], reverse=True)[:10]),
            "token_status": self.token_manager.get_token_status()
        }

        # 保存报告
        report_path = os.path.join(self.output_dir, f"collection_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            logger.info(f"收集报告已保存到: {report_path}")
        except Exception as e:
            logger.error(f"保存报告失败: {str(e)}")

        return report
