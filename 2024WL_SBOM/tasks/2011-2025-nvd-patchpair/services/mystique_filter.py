#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Mystique筛选标准实现

实现mystique的6个筛选条件：
1. 补丁代码与收集到的commit之一完全相同
2. 编辑距离相似度得分超过0.5，且提交消息与收集到的提交之一相同
3. 提交消息引用了收集到的提交的SHA值
4. 提交消息引用了收集到的CVE ID
5. 选择CVE补丁对（最早和最新提交）
6. 结果存储（按时间分类）
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from difflib import SequenceMatcher
from datetime import datetime

logger = logging.getLogger(__name__)

class MystiqueFilter:
    """Mystique筛选器"""
    
    def __init__(self, similarity_threshold: float = 0.5):
        """
        初始化筛选器
        
        Args:
            similarity_threshold: 相似度阈值，默认0.5
        """
        self.similarity_threshold = similarity_threshold
    
    def calculate_edit_distance_similarity(self, text1: str, text2: str) -> float:
        """
        计算编辑距离相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            
        Returns:
            相似度得分 (0-1)
        """
        if not text1 or not text2:
            return 0.0
        
        # 使用SequenceMatcher计算相似度
        matcher = SequenceMatcher(None, text1, text2)
        return matcher.ratio()
    
    def is_code_identical(self, patch1: str, patch2: str) -> bool:
        """
        检查补丁代码是否完全相同
        
        Args:
            patch1: 第一个补丁代码
            patch2: 第二个补丁代码
            
        Returns:
            是否完全相同
        """
        if not patch1 or not patch2:
            return False
        
        # 标准化代码（去除空白字符差异）
        normalized_patch1 = self._normalize_code(patch1)
        normalized_patch2 = self._normalize_code(patch2)
        
        return normalized_patch1 == normalized_patch2
    
    def _normalize_code(self, code: str) -> str:
        """
        标准化代码格式
        
        Args:
            code: 原始代码
            
        Returns:
            标准化后的代码
        """
        if not code:
            return ""
        
        # 去除多余的空白字符，但保留代码结构
        lines = []
        for line in code.split('\n'):
            # 去除行首行尾空白，但保留缩进结构
            stripped = line.rstrip()
            if stripped:
                lines.append(stripped)
        
        return '\n'.join(lines)
    
    def check_commit_message_similarity(self, message1: str, message2: str) -> bool:
        """
        检查提交消息是否相同
        
        Args:
            message1: 第一个提交消息
            message2: 第二个提交消息
            
        Returns:
            是否相同
        """
        if not message1 or not message2:
            return False
        
        # 标准化消息（去除空白字符，转小写）
        normalized_msg1 = self._normalize_message(message1)
        normalized_msg2 = self._normalize_message(message2)
        
        return normalized_msg1 == normalized_msg2
    
    def _normalize_message(self, message: str) -> str:
        """
        标准化提交消息
        
        Args:
            message: 原始消息
            
        Returns:
            标准化后的消息
        """
        if not message:
            return ""
        
        # 转小写，去除多余空白
        normalized = re.sub(r'\s+', ' ', message.lower().strip())
        return normalized
    
    def check_sha_reference(self, commit_message: str, sha_list: List[str]) -> bool:
        """
        检查提交消息是否引用了SHA值
        
        Args:
            commit_message: 提交消息
            sha_list: SHA值列表
            
        Returns:
            是否引用了SHA值
        """
        if not commit_message or not sha_list:
            return False
        
        message_lower = commit_message.lower()
        
        for sha in sha_list:
            if not sha:
                continue
            
            # 检查完整SHA或前7位
            sha_lower = sha.lower()
            sha_short = sha_lower[:7] if len(sha_lower) >= 7 else sha_lower
            
            if sha_lower in message_lower or sha_short in message_lower:
                return True
        
        return False
    
    def check_cve_reference(self, commit_message: str, cve_id: str) -> bool:
        """
        检查提交消息是否引用了CVE ID
        
        Args:
            commit_message: 提交消息
            cve_id: CVE ID
            
        Returns:
            是否引用了CVE ID
        """
        if not commit_message or not cve_id:
            return False
        
        # CVE ID模式匹配
        cve_pattern = r'CVE-\d{4}-\d{4,}'
        message_cves = re.findall(cve_pattern, commit_message.upper())
        
        return cve_id.upper() in [cve.upper() for cve in message_cves]
    
    def filter_patches_by_mystique_criteria(self, 
                                          patches: List[Dict[str, Any]], 
                                          cve_id: str) -> List[Dict[str, Any]]:
        """
        根据mystique标准筛选补丁
        
        Args:
            patches: 补丁列表
            cve_id: CVE ID
            
        Returns:
            筛选后的补丁列表
        """
        filtered_patches = []
        
        for i, patch in enumerate(patches):
            criteria_met = {
                'code_identical': False,
                'edit_distance_and_message': False,
                'sha_reference': False,
                'cve_reference': False
            }
            
            patch_code = patch.get('code', '')
            commit_message = patch.get('commit_message', '')
            commit_sha = patch.get('commit_sha', '')
            
            # 检查与其他补丁的关系
            for j, other_patch in enumerate(patches):
                if i == j:
                    continue
                
                other_code = other_patch.get('code', '')
                other_message = other_patch.get('commit_message', '')
                other_sha = other_patch.get('commit_sha', '')
                
                # 条件1: 代码完全相同
                if self.is_code_identical(patch_code, other_code):
                    criteria_met['code_identical'] = True
                
                # 条件2: 编辑距离相似度 + 消息相同
                similarity = self.calculate_edit_distance_similarity(patch_code, other_code)
                if (similarity > self.similarity_threshold and 
                    self.check_commit_message_similarity(commit_message, other_message)):
                    criteria_met['edit_distance_and_message'] = True
            
            # 条件3: SHA引用
            all_shas = [p.get('commit_sha', '') for p in patches if p.get('commit_sha')]
            if self.check_sha_reference(commit_message, all_shas):
                criteria_met['sha_reference'] = True
            
            # 条件4: CVE引用
            if self.check_cve_reference(commit_message, cve_id):
                criteria_met['cve_reference'] = True
            
            # 如果满足任一条件，保留该补丁
            if any(criteria_met.values()):
                patch['mystique_criteria'] = criteria_met
                patch['criteria_score'] = sum(criteria_met.values())
                filtered_patches.append(patch)
        
        return filtered_patches
    
    def select_cve_patch_pairs(self, patches: List[Dict[str, Any]]) -> List[Tuple[Dict[str, Any], Dict[str, Any]]]:
        """
        选择CVE补丁对（最早和最新提交）
        
        Args:
            patches: 筛选后的补丁列表
            
        Returns:
            补丁对列表 [(target_patch, source_patch), ...]
        """
        if len(patches) < 2:
            logger.warning(f"补丁数量不足，无法形成补丁对: {len(patches)}")
            return []
        
        # 按时间戳排序
        sorted_patches = sorted(patches, key=lambda x: x.get('timestamp', 0))
        
        patch_pairs = []
        
        # 按CVE分组（如果有多个CVE）
        cve_groups = {}
        for patch in sorted_patches:
            cve_id = patch.get('cve_id', 'unknown')
            if cve_id not in cve_groups:
                cve_groups[cve_id] = []
            cve_groups[cve_id].append(patch)
        
        # 为每个CVE选择最早和最新的提交
        for cve_id, cve_patches in cve_groups.items():
            if len(cve_patches) >= 2:
                # 最早的作为target patch，最新的作为source patch
                target_patch = cve_patches[0]  # 最早
                source_patch = cve_patches[-1]  # 最新
                
                patch_pairs.append((target_patch, source_patch))
                logger.info(f"为 {cve_id} 创建补丁对: {target_patch.get('commit_sha', '')[:8]} -> {source_patch.get('commit_sha', '')[:8]}")
        
        return patch_pairs
    
    def is_c_cpp_related(self, patch: Dict[str, Any]) -> bool:
        """
        检查补丁是否与C/C++相关
        
        Args:
            patch: 补丁信息
            
        Returns:
            是否与C/C++相关
        """
        # 检查文件扩展名
        files = patch.get('files', [])
        c_cpp_extensions = ['.c', '.cpp', '.cc', '.cxx', '.h', '.hpp', '.hxx']
        
        for file_path in files:
            if any(file_path.lower().endswith(ext) for ext in c_cpp_extensions):
                return True
        
        # 检查项目语言
        language = patch.get('language', '').lower()
        if language in ['c', 'c++', 'cpp']:
            return True
        
        return False
    
    def filter_non_c_cpp_patches(self, patches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤非C/C++相关的补丁
        
        Args:
            patches: 补丁列表
            
        Returns:
            C/C++相关的补丁列表
        """
        return [patch for patch in patches if self.is_c_cpp_related(patch)]
