#!/usr/bin/env python3
"""
增强现有的补丁对结果，添加：
1. 补丁对分类：cross_version, cross_branch, cross_repo
2. CWE类别信息
3. 生成统计报告
"""

import json
import os
import re
from typing import Dict, List, Any, <PERSON><PERSON>
from collections import defaultdict, Counter
import sys

# 添加项目路径
sys.path.insert(0, '/home/<USER>')
sys.path.insert(0, '.')

def extract_repo_info(url: str) -> Tuple[str, str, str]:
    """
    从URL中提取仓库信息
    
    Returns:
        (repo_owner, repo_name, commit_hash)
    """
    # GitHub URL pattern
    github_pattern = r'https://github\.com/([^/]+)/([^/]+)/commit/([a-f0-9]+)'
    # GitLab URL pattern  
    gitlab_pattern = r'https://gitlab\.com/([^/]+)/([^/]+)/-/commit/([a-f0-9]+)'
    
    github_match = re.match(github_pattern, url)
    if github_match:
        return github_match.group(1), github_match.group(2), github_match.group(3)
    
    gitlab_match = re.match(gitlab_pattern, url)
    if gitlab_match:
        return gitlab_match.group(1), gitlab_match.group(2), gitlab_match.group(3)
    
    return "unknown", "unknown", "unknown"

def classify_patch_pair(source_repo: str, target_repo: str, source_commit: str, target_commit: str) -> str:
    """
    分类补丁对类型
    
    Returns:
        cross_repo, cross_branch, cross_version, equivalent_patch
    """
    # 如果是等价补丁（单个补丁的备用记录）
    if source_repo == target_repo and source_commit == target_commit:
        return "equivalent_patch"
    
    # 提取仓库信息
    source_owner, source_name, source_hash = extract_repo_info(source_repo)
    target_owner, target_name, target_hash = extract_repo_info(target_repo)
    
    # Cross repo: 不同的仓库
    if f"{source_owner}/{source_name}" != f"{target_owner}/{target_name}":
        return "cross_repo"
    
    # 同一仓库，不同commit
    if source_hash != target_hash and source_hash != "unknown" and target_hash != "unknown":
        # 这里简化处理，实际需要Git API来判断是否同branch
        # 暂时都归类为cross_version（同repo同branch的不同版本）
        return "cross_version"
    
    return "cross_branch"  # 默认归类

def get_cwe_from_cve_id(cve_id: str, original_cve_data: Dict = None) -> List[str]:
    """
    从CVE数据中提取CWE信息
    这里需要重新查询NVD数据库或使用原始CVE数据
    """
    # 如果有原始CVE数据，从中提取CWE
    if original_cve_data and 'weaknesses' in original_cve_data:
        cwe_list = []
        for weakness in original_cve_data['weaknesses']:
            if 'description' in weakness:
                for desc in weakness['description']:
                    if desc.get('lang') == 'en' and desc.get('value', '').startswith('CWE-'):
                        cwe_list.append(desc['value'])
        return cwe_list
    
    # 如果没有原始数据，返回空列表（后续可以通过API查询）
    return []

def enhance_patch_pair(patch_pair: Dict[str, Any]) -> Dict[str, Any]:
    """
    增强单个补丁对的信息
    """
    enhanced_pair = patch_pair.copy()
    
    # 获取仓库信息
    source_repo = patch_pair['projects']['source']['repo']
    target_repo = patch_pair['projects']['target']['repo']
    source_commit = patch_pair['versions']['source']['patched']['commit']
    target_commit = patch_pair['versions']['target']['patched']['commit']
    
    # 分类补丁对
    patch_classification = classify_patch_pair(source_repo, target_repo, source_commit, target_commit)
    
    # 更新metadata
    if 'metadata' not in enhanced_pair:
        enhanced_pair['metadata'] = {}
    
    enhanced_pair['metadata']['patch_classification'] = patch_classification
    
    # 如果CWE信息为空，尝试从CVE ID推断（这里简化处理）
    if not enhanced_pair['metadata'].get('cwe_categories'):
        cve_id = patch_pair['cve_id']
        # 这里可以添加CWE查询逻辑
        enhanced_pair['metadata']['cwe_categories'] = []
    
    return enhanced_pair

def process_json_file(input_file: str, output_file: str) -> Dict[str, Any]:
    """
    处理单个JSON文件
    """
    print(f"处理文件: {input_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        patch_pairs = json.load(f)
    
    enhanced_pairs = []
    stats = {
        'total': len(patch_pairs),
        'cross_repo': 0,
        'cross_branch': 0, 
        'cross_version': 0,
        'equivalent_patch': 0,
        'repos': set(),
        'cwe_categories': Counter()
    }
    
    for pair in patch_pairs:
        enhanced_pair = enhance_patch_pair(pair)
        enhanced_pairs.append(enhanced_pair)
        
        # 统计信息
        classification = enhanced_pair['metadata']['patch_classification']
        stats[classification] += 1
        
        # 收集仓库信息
        source_owner, source_name, _ = extract_repo_info(enhanced_pair['projects']['source']['repo'])
        target_owner, target_name, _ = extract_repo_info(enhanced_pair['projects']['target']['repo'])
        
        if source_owner != "unknown" and source_name != "unknown":
            stats['repos'].add(f"{source_owner}/{source_name}")
        if target_owner != "unknown" and target_name != "unknown":
            stats['repos'].add(f"{target_owner}/{target_name}")
        
        # 收集CWE信息
        for cwe in enhanced_pair['metadata'].get('cwe_categories', []):
            stats['cwe_categories'][cwe] += 1
    
    # 保存增强后的结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(enhanced_pairs, f, indent=2, ensure_ascii=False)
    
    # 转换set为list以便JSON序列化
    stats['repos'] = list(stats['repos'])
    stats['cwe_categories'] = dict(stats['cwe_categories'])
    
    print(f"完成处理: {input_file} -> {output_file}")
    print(f"  总数: {stats['total']}")
    print(f"  Cross Repo: {stats['cross_repo']}")
    print(f"  Cross Branch: {stats['cross_branch']}")
    print(f"  Cross Version: {stats['cross_version']}")
    print(f"  Equivalent Patch: {stats['equivalent_patch']}")
    print(f"  涉及仓库数: {len(stats['repos'])}")
    
    return stats

def main():
    input_dir = "output_2011_2025_optimized"
    output_dir = "output_2011_2025_enhanced"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有JSON文件
    json_files = [f for f in os.listdir(input_dir) if f.endswith('.json') and 'nvd_patch_pairs' in f]
    
    print(f"找到 {len(json_files)} 个JSON文件需要处理")
    
    overall_stats = {
        'total_files': len(json_files),
        'total_pairs': 0,
        'cross_repo': 0,
        'cross_branch': 0,
        'cross_version': 0,
        'equivalent_patch': 0,
        'all_repos': set(),
        'all_cwe_categories': Counter(),
        'file_stats': {}
    }
    
    # 处理每个文件
    for json_file in sorted(json_files):
        input_path = os.path.join(input_dir, json_file)
        output_path = os.path.join(output_dir, json_file)
        
        try:
            file_stats = process_json_file(input_path, output_path)
            
            # 累计统计
            overall_stats['total_pairs'] += file_stats['total']
            overall_stats['cross_repo'] += file_stats['cross_repo']
            overall_stats['cross_branch'] += file_stats['cross_branch']
            overall_stats['cross_version'] += file_stats['cross_version']
            overall_stats['equivalent_patch'] += file_stats['equivalent_patch']
            overall_stats['all_repos'].update(file_stats['repos'])
            overall_stats['all_cwe_categories'].update(file_stats['cwe_categories'])
            overall_stats['file_stats'][json_file] = file_stats
            
        except Exception as e:
            print(f"处理文件 {json_file} 时出错: {str(e)}")
            continue
    
    # 转换为可序列化格式
    overall_stats['all_repos'] = list(overall_stats['all_repos'])
    overall_stats['all_cwe_categories'] = dict(overall_stats['all_cwe_categories'])
    
    # 保存总体统计报告
    report_path = os.path.join(output_dir, "enhancement_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(overall_stats, f, indent=2, ensure_ascii=False)
    
    # 打印总体统计
    print("\n" + "="*60)
    print("总体统计报告")
    print("="*60)
    print(f"处理文件数: {overall_stats['total_files']}")
    print(f"总补丁对数: {overall_stats['total_pairs']}")
    print(f"Cross Repo: {overall_stats['cross_repo']}")
    print(f"Cross Branch: {overall_stats['cross_branch']}")
    print(f"Cross Version: {overall_stats['cross_version']}")
    print(f"Equivalent Patch: {overall_stats['equivalent_patch']}")
    print(f"涉及仓库总数: {len(overall_stats['all_repos'])}")
    print(f"CWE类别总数: {len(overall_stats['all_cwe_categories'])}")
    
    print(f"\n详细报告已保存到: {report_path}")
    print(f"增强后的文件已保存到: {output_dir}/")

if __name__ == "__main__":
    main()
