#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD补丁对数据模型

定义补丁对的数据结构，符合mystique筛选标准
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

class PatchType(Enum):
    """补丁类型枚举"""
    CROSS_REPO = "cross_repo"
    SAME_REPO = "same_repo"

class PatchPairCategory(Enum):
    """补丁对类别枚举"""
    CROSS_VERSION = "cross_version"  # 同repo，同branch，不同version
    CROSS_BRANCH = "cross_branch"    # 同repo，不同branch
    CROSS_REPO = "cross_repo"        # 不同repo

@dataclass
class ProjectInfo:
    """项目信息"""
    name: str
    repo: str
    language: str

@dataclass
class VersionInfo:
    """版本信息"""
    version: str = "unknown"
    commit: str = "unknown"

@dataclass
class ProjectVersions:
    """项目版本信息"""
    vulnerable: VersionInfo = field(default_factory=VersionInfo)
    patched: VersionInfo = field(default_factory=VersionInfo)

@dataclass
class PatchPairMetadata:
    """补丁对元数据"""
    cwe_categories: List[str] = field(default_factory=list)
    published_date: Optional[str] = None
    patch_pair_category: Optional[str] = None
    confidence_score: float = 0.0
    similarity_score: float = 0.0
    edit_distance_score: float = 0.0
    commit_message_match: bool = False
    sha_reference_match: bool = False
    cve_reference_match: bool = False
    source_dataset: str = "NVD-PatchPair"
    collection_date: str = field(default_factory=lambda: datetime.now().isoformat())
    
    # mystique筛选标准记录
    mystique_criteria: Dict[str, bool] = field(default_factory=dict)

@dataclass
class PatchPair:
    """NVD补丁对数据模型"""
    unified_id: str
    cve_id: str
    patch_type: str  # cross_repo | same_repo
    
    # 项目信息
    projects: Dict[str, ProjectInfo]
    
    # 版本信息
    versions: Dict[str, ProjectVersions]
    
    # 数据源
    source_dataset: str = "NVD-PatchPair"
    
    # 元数据
    metadata: PatchPairMetadata = field(default_factory=PatchPairMetadata)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.unified_id:
            self.generate_unified_id()
    
    def generate_unified_id(self):
        """生成统一ID"""
        source_project = self.projects.get('source')
        target_project = self.projects.get('target')
        target_versions = self.versions.get('target')

        source_name = source_project.name if source_project else 'unknown'
        target_name = target_project.name if target_project else 'unknown'
        target_version_hash = target_versions.patched.commit if target_versions else 'unknown'

        if target_version_hash == "unknown" and target_versions:
            target_version_hash = target_versions.patched.version

        self.unified_id = f"{self.cve_id}_{source_name}_{target_name}_{target_version_hash}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "unified_id": self.unified_id,
            "cve_id": self.cve_id,
            "patch_type": self.patch_type,
            "projects": {
                "source": {
                    "name": self.projects['source'].name,
                    "repo": self.projects['source'].repo,
                    "language": self.projects['source'].language
                },
                "target": {
                    "name": self.projects['target'].name,
                    "repo": self.projects['target'].repo,
                    "language": self.projects['target'].language
                }
            },
            "versions": {
                "source": {
                    "vulnerable": {
                        "version": self.versions['source'].vulnerable.version,
                        "commit": self.versions['source'].vulnerable.commit
                    },
                    "patched": {
                        "version": self.versions['source'].patched.version,
                        "commit": self.versions['source'].patched.commit
                    }
                },
                "target": {
                    "vulnerable": {
                        "version": self.versions['target'].vulnerable.version,
                        "commit": self.versions['target'].vulnerable.commit
                    },
                    "patched": {
                        "version": self.versions['target'].patched.version,
                        "commit": self.versions['target'].patched.commit
                    }
                }
            },
            "source_dataset": self.source_dataset,
            "metadata": {
                "cwe_categories": self.metadata.cwe_categories,
                "published_date": self.metadata.published_date,
                "patch_pair_category": self.metadata.patch_pair_category,
                "confidence_score": self.metadata.confidence_score,
                "similarity_score": self.metadata.similarity_score,
                "edit_distance_score": self.metadata.edit_distance_score,
                "commit_message_match": self.metadata.commit_message_match,
                "sha_reference_match": self.metadata.sha_reference_match,
                "cve_reference_match": self.metadata.cve_reference_match,
                "source_dataset": self.metadata.source_dataset,
                "collection_date": self.metadata.collection_date,
                "mystique_criteria": self.metadata.mystique_criteria
            }
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PatchPair':
        """从字典创建实例"""
        projects = {
            'source': ProjectInfo(**data['projects']['source']),
            'target': ProjectInfo(**data['projects']['target'])
        }
        
        versions = {
            'source': ProjectVersions(
                vulnerable=VersionInfo(**data['versions']['source']['vulnerable']),
                patched=VersionInfo(**data['versions']['source']['patched'])
            ),
            'target': ProjectVersions(
                vulnerable=VersionInfo(**data['versions']['target']['vulnerable']),
                patched=VersionInfo(**data['versions']['target']['patched'])
            )
        }
        
        metadata = PatchPairMetadata(**data.get('metadata', {}))
        
        return cls(
            unified_id=data['unified_id'],
            cve_id=data['cve_id'],
            patch_type=data['patch_type'],
            projects=projects,
            versions=versions,
            source_dataset=data.get('source_dataset', 'NVD-PatchPair'),
            metadata=metadata
        )
    
    def meets_mystique_criteria(self) -> bool:
        """检查是否满足mystique筛选标准"""
        criteria = self.metadata.mystique_criteria
        
        # 至少满足一个条件
        return any([
            criteria.get('code_identical', False),  # 条件1: 代码完全相同
            criteria.get('edit_distance_and_message', False),  # 条件2: 编辑距离+消息相同
            criteria.get('sha_reference', False),  # 条件3: SHA引用
            criteria.get('cve_reference', False),  # 条件4: CVE引用
        ])
    
    def is_c_cpp_project(self) -> bool:
        """检查是否为C/C++项目"""
        source_lang = self.projects['source'].language.lower()
        target_lang = self.projects['target'].language.lower()
        
        valid_languages = ['c', 'c++', 'cpp']
        return (source_lang in valid_languages and 
                target_lang in valid_languages)
    
    def determine_patch_pair_category(self):
        """确定补丁对类别"""
        source_repo = self.projects['source'].repo
        target_repo = self.projects['target'].repo
        
        if source_repo != target_repo:
            self.metadata.patch_pair_category = PatchPairCategory.CROSS_REPO.value
        else:
            # 同repo的情况下，需要进一步分析branch和version
            # 这里简化处理，可以根据实际需求扩展
            self.metadata.patch_pair_category = PatchPairCategory.CROSS_VERSION.value
