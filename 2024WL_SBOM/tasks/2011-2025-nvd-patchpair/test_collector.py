#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD补丁对收集器测试脚本

测试收集器的各个功能模块
"""

import os
import sys
import json
import logging
from datetime import datetime

# 添加项目根目录到sys.path
sys.path.insert(0, "/home/<USER>")

# 添加当前目录到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from services.nvd_patch_collector import NVDPatchPairCollector
from services.mystique_filter import MystiqueFilter
from models.patch_pair import PatchPair, ProjectInfo, VersionInfo, ProjectVersions, PatchPairMetadata

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_mystique_filter():
    """测试mystique筛选器"""
    print("\n" + "="*50)
    print("测试 Mystique 筛选器")
    print("="*50)
    
    filter_obj = MystiqueFilter()
    
    # 测试代码相似度
    code1 = """
    if (ptr == NULL) {
        return -1;
    }
    """
    
    code2 = """
    if (ptr == NULL) {
        return -1;
    }
    """
    
    similarity = filter_obj.calculate_edit_distance_similarity(code1, code2)
    print(f"代码相似度测试: {similarity}")
    
    # 测试代码完全相同
    is_identical = filter_obj.is_code_identical(code1, code2)
    print(f"代码完全相同测试: {is_identical}")
    
    # 测试提交消息相似性
    msg1 = "Fix CVE-2021-1234 buffer overflow"
    msg2 = "Fix CVE-2021-1234 buffer overflow"
    msg_similar = filter_obj.check_commit_message_similarity(msg1, msg2)
    print(f"提交消息相似性测试: {msg_similar}")
    
    # 测试SHA引用
    commit_msg = "Backport fix from commit abc123def456"
    sha_list = ["abc123def456", "other_sha"]
    has_sha_ref = filter_obj.check_sha_reference(commit_msg, sha_list)
    print(f"SHA引用测试: {has_sha_ref}")
    
    # 测试CVE引用
    cve_msg = "Fix for CVE-2021-1234 vulnerability"
    cve_id = "CVE-2021-1234"
    has_cve_ref = filter_obj.check_cve_reference(cve_msg, cve_id)
    print(f"CVE引用测试: {has_cve_ref}")

def test_patch_pair_model():
    """测试补丁对数据模型"""
    print("\n" + "="*50)
    print("测试补丁对数据模型")
    print("="*50)
    
    # 创建测试数据
    projects = {
        'source': ProjectInfo(
            name="Linux Kernel",
            repo="https://github.com/torvalds/linux",
            language="C"
        ),
        'target': ProjectInfo(
            name="Linux Kernel",
            repo="https://github.com/torvalds/linux",
            language="C"
        )
    }
    
    versions = {
        'source': ProjectVersions(
            vulnerable=VersionInfo(version="4.17", commit="abc123"),
            patched=VersionInfo(version="4.18", commit="def456")
        ),
        'target': ProjectVersions(
            vulnerable=VersionInfo(version="4.9", commit="ghi789"),
            patched=VersionInfo(version="4.10", commit="jkl012")
        )
    }
    
    metadata = PatchPairMetadata(
        cwe_categories=["CWE-119", "CWE-787"],
        published_date="2021-01-01",
        confidence_score=0.8,
        mystique_criteria={
            'code_identical': True,
            'edit_distance_and_message': False,
            'sha_reference': False,
            'cve_reference': True
        }
    )
    
    # 创建补丁对
    patch_pair = PatchPair(
        unified_id="",
        cve_id="CVE-2021-1234",
        patch_type="same_repo",
        projects=projects,
        versions=versions,
        metadata=metadata
    )
    
    print(f"生成的统一ID: {patch_pair.unified_id}")
    print(f"是否满足mystique标准: {patch_pair.meets_mystique_criteria()}")
    print(f"是否为C/C++项目: {patch_pair.is_c_cpp_project()}")
    
    # 转换为字典
    patch_dict = patch_pair.to_dict()
    print(f"字典格式预览: {json.dumps(patch_dict, indent=2)[:500]}...")

def test_token_manager():
    """测试GitHub token管理器"""
    print("\n" + "="*50)
    print("测试 GitHub Token 管理器")
    print("="*50)
    
    config_path = "/home/<USER>/src/data_collection/config/sources.json"
    
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        return
    
    from src.data_collection.utils.token_manager import GitHubTokenManager
    
    token_manager = GitHubTokenManager(config_path)
    
    print(f"加载的token数量: {len(token_manager.tokens)}")
    
    # 获取下一个token
    token = token_manager.get_next_token()
    if token:
        print(f"获取到token: {token[:10]}...")
        
        # 检查token有效性
        is_valid = token_manager.check_token_validity(token)
        print(f"Token有效性: {is_valid}")
    else:
        print("没有可用的token")
    
    # 显示token状态
    status = token_manager.get_token_status()
    print(f"Token状态: {json.dumps(status, indent=2)}")

def test_nvd_collector_init():
    """测试NVD收集器初始化"""
    print("\n" + "="*50)
    print("测试 NVD 收集器初始化")
    print("="*50)
    
    config_path = "/home/<USER>/src/data_collection/config/sources.json"
    output_dir = "./test_output"
    
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        return
    
    try:
        collector = NVDPatchPairCollector(config_path, output_dir)
        print("✓ NVD收集器初始化成功")
        
        # 测试配置加载
        print(f"✓ 配置加载成功，包含 {len(collector.config.get('sources', {}))} 个数据源")
        
        # 测试token管理器
        valid_tokens = collector.token_manager.get_valid_tokens_count()
        print(f"✓ 可用GitHub tokens: {valid_tokens}")
        
        # 测试已收集CVE ID加载
        print(f"✓ 已加载 {len(collector.collected_cve_ids)} 个已收集的CVE ID")
        
    except Exception as e:
        print(f"✗ 初始化失败: {str(e)}")

def test_cve_collection():
    """测试CVE数据收集（小规模测试）"""
    print("\n" + "="*50)
    print("测试 CVE 数据收集")
    print("="*50)
    
    config_path = "/home/<USER>/src/data_collection/config/sources.json"
    output_dir = "./test_output"
    
    if not os.path.exists(config_path):
        print(f"配置文件不存在: {config_path}")
        return
    
    try:
        collector = NVDPatchPairCollector(config_path, output_dir)
        
        # 测试收集少量CVE数据
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 7)  # 只测试一周的数据
        
        print(f"测试收集 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的CVE数据...")
        
        cve_data = collector.collect_cves_by_timerange(start_date, end_date, max_cves=5)
        
        print(f"✓ 收集到 {len(cve_data)} 条CVE数据")
        
        if cve_data:
            sample_cve = cve_data[0]
            print(f"✓ 示例CVE: {sample_cve.get('id', 'unknown')}")
            
            # 测试CWE提取
            cwe_categories = collector.extract_cwe_categories(sample_cve)
            print(f"✓ 提取到CWE类别: {cwe_categories}")
        
    except Exception as e:
        print(f"✗ CVE收集测试失败: {str(e)}")

def main():
    """主测试函数"""
    setup_test_logging()
    
    print("NVD补丁对收集器功能测试")
    print("="*60)
    
    # 运行各项测试
    test_mystique_filter()
    test_patch_pair_model()
    test_token_manager()
    test_nvd_collector_init()
    test_cve_collection()
    
    print("\n" + "="*60)
    print("测试完成!")
    print("="*60)

if __name__ == "__main__":
    main()
