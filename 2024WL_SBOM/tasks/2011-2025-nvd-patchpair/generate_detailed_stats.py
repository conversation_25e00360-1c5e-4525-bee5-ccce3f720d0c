#!/usr/bin/env python3
"""
生成详细的统计分析报告
"""

import json
import os
from collections import defaultdict, Counter
from typing import Dict, List, Any

def analyze_enhanced_results():
    """分析增强后的结果"""
    
    # 读取总体报告
    with open('output_2011_2025_enhanced/enhancement_report.json', 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    print("="*80)
    print("2011-2025年 NVD 补丁对收集结果 - 详细统计分析")
    print("="*80)
    
    # 总体统计
    print(f"\n📊 总体统计:")
    print(f"  处理文件数: {report['total_files']}")
    print(f"  总补丁对数: {report['total_pairs']:,}")
    print(f"  涉及仓库数: {len(report['all_repos'])}")
    print(f"  CWE类别数: {len(report['all_cwe_categories'])}")
    
    # 补丁对分类统计
    print(f"\n🔍 补丁对分类统计:")
    print(f"  Cross Repo (跨仓库): {report['cross_repo']:,} ({report['cross_repo']/report['total_pairs']*100:.1f}%)")
    print(f"  Cross Version (同仓库不同版本): {report['cross_version']:,} ({report['cross_version']/report['total_pairs']*100:.1f}%)")
    print(f"  Cross Branch (同仓库不同分支): {report['cross_branch']:,} ({report['cross_branch']/report['total_pairs']*100:.1f}%)")
    print(f"  Equivalent Patch (等价补丁): {report['equivalent_patch']:,} ({report['equivalent_patch']/report['total_pairs']*100:.1f}%)")
    
    # CWE统计
    print(f"\n🛡️ CWE类别统计:")
    if report['all_cwe_categories']:
        for cwe, count in sorted(report['all_cwe_categories'].items()):
            print(f"  {cwe}: {count} 个补丁对")
    else:
        print("  暂无CWE分类数据")
    
    # 年度趋势分析
    print(f"\n📈 年度趋势分析:")
    yearly_stats = {}
    for filename, stats in report['file_stats'].items():
        if 'nvd_patch_pairs_' in filename and '__' in filename:
            # 提取年份
            year_part = filename.split('nvd_patch_pairs_')[1].split('__')[0]
            if year_part.isdigit():
                year = int(year_part)
                yearly_stats[year] = stats
    
    for year in sorted(yearly_stats.keys()):
        stats = yearly_stats[year]
        print(f"  {year}年: {stats['total']:,} 个补丁对")
        print(f"    - Cross Repo: {stats['cross_repo']}")
        print(f"    - Cross Version: {stats['cross_version']}")
        print(f"    - Equivalent Patch: {stats['equivalent_patch']}")
        print(f"    - 涉及仓库: {len(stats['repos'])}")
    
    # 热门仓库统计
    print(f"\n🔥 热门仓库 TOP 20:")
    repo_counts = Counter()
    
    # 统计每个仓库的补丁对数量
    for filename, stats in report['file_stats'].items():
        if 'repos' in stats:
            for repo in stats['repos']:
                repo_counts[repo] += stats['total']
    
    for i, (repo, count) in enumerate(repo_counts.most_common(20), 1):
        print(f"  {i:2d}. {repo}: {count} 个补丁对")
    
    # 分类与仓库的关系分析
    print(f"\n🔗 分类与仓库关系分析:")
    
    # 读取具体的补丁对数据进行更详细分析
    classification_repo_stats = defaultdict(lambda: defaultdict(int))
    
    # 分析主要文件
    main_files = ['nvd_patch_pairs_all_20250804.json']
    
    for filename in main_files:
        filepath = f'output_2011_2025_enhanced/{filename}'
        if os.path.exists(filepath):
            print(f"\n  分析文件: {filename}")
            with open(filepath, 'r', encoding='utf-8') as f:
                patch_pairs = json.load(f)
            
            for pair in patch_pairs:
                classification = pair['metadata']['patch_classification']
                source_repo = pair['projects']['source']['repo']
                
                # 提取仓库名
                if 'github.com' in source_repo:
                    repo_parts = source_repo.split('github.com/')[1].split('/commit/')[0]
                    classification_repo_stats[classification][repo_parts] += 1
    
    for classification in ['cross_repo', 'cross_version', 'equivalent_patch']:
        if classification in classification_repo_stats:
            print(f"\n  {classification.upper()} 类型的热门仓库:")
            repo_stats = classification_repo_stats[classification]
            for i, (repo, count) in enumerate(sorted(repo_stats.items(), key=lambda x: x[1], reverse=True)[:10], 1):
                print(f"    {i:2d}. {repo}: {count} 个补丁对")
    
    # 保存详细统计到文件
    detailed_stats = {
        'summary': {
            'total_files': report['total_files'],
            'total_pairs': report['total_pairs'],
            'total_repos': len(report['all_repos']),
            'total_cwe_categories': len(report['all_cwe_categories'])
        },
        'classification_distribution': {
            'cross_repo': {
                'count': report['cross_repo'],
                'percentage': round(report['cross_repo']/report['total_pairs']*100, 2)
            },
            'cross_version': {
                'count': report['cross_version'],
                'percentage': round(report['cross_version']/report['total_pairs']*100, 2)
            },
            'cross_branch': {
                'count': report['cross_branch'],
                'percentage': round(report['cross_branch']/report['total_pairs']*100, 2)
            },
            'equivalent_patch': {
                'count': report['equivalent_patch'],
                'percentage': round(report['equivalent_patch']/report['total_pairs']*100, 2)
            }
        },
        'cwe_distribution': report['all_cwe_categories'],
        'yearly_trends': yearly_stats,
        'top_repositories': dict(repo_counts.most_common(50)),
        'classification_repo_analysis': dict(classification_repo_stats)
    }
    
    with open('output_2011_2025_enhanced/detailed_statistics.json', 'w', encoding='utf-8') as f:
        json.dump(detailed_stats, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 详细统计数据已保存到: output_2011_2025_enhanced/detailed_statistics.json")
    print("="*80)

if __name__ == "__main__":
    analyze_enhanced_results()
