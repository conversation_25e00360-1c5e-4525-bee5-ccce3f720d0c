#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD Patch Pair收集器主程序

收集2011-2025年的NVD patch pair数据，符合mystique筛选标准
"""

import sys
import os
import logging
import time
from datetime import datetime
from typing import List, Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from collectors.nvd_collector import NVDCollector
from collectors.patch_extractor import PatchExtractor
from filters.mystique_filter import MystiqueFilter
from generators.patch_pair_generator import PatchPairGenerator
from storage.data_storage import DataStorage
from models.patch_pair import PatchPair, CVEInfo


def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL),
        format=Config.LOG_FORMAT,
        handlers=[
            logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 设置第三方库的日志级别
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


class NVDPatchPairCollector:
    """NVD Patch Pair收集器主类"""
    
    def __init__(self):
        """初始化收集器"""
        self.nvd_collector = NVDCollector(Config.NVD_API_KEY)
        self.patch_extractor = PatchExtractor(Config.GITHUB_TOKEN)
        self.mystique_filter = MystiqueFilter()
        self.patch_pair_generator = PatchPairGenerator()
        self.data_storage = DataStorage(Config.OUTPUT_DIR)
        
        self.logger = logging.getLogger(__name__)
        
        # 加载已存在的CVE
        self.existing_cves = self.data_storage.load_merged_dataset(Config.MERGED_DATASET_PATH)
        self.logger.info(f"加载了 {len(self.existing_cves)} 个已存在的CVE ID")
    
    def collect_all_data(self) -> Dict[str, str]:
        """
        收集所有年份的数据
        
        Returns:
            保存的文件路径字典
        """
        self.logger.info(f"开始收集{Config.START_YEAR}-{Config.END_YEAR}年的NVD patch pair数据")
        
        all_patch_pairs = []
        
        # 按年份收集数据
        for year in range(Config.START_YEAR, Config.END_YEAR + 1):
            if year == Config.END_YEAR:
                # 2025年只收集到8月4日
                end_date = Config.END_DATE
            else:
                end_date = datetime(year, 12, 31, 23, 59, 59)
            
            self.logger.info(f"开始收集{year}年的数据")
            year_patch_pairs = self.collect_year_data(year, end_date)
            all_patch_pairs.extend(year_patch_pairs)
            
            self.logger.info(f"{year}年收集完成，获得 {len(year_patch_pairs)} 个补丁对")
            
            # 每年收集完后稍作休息
            time.sleep(2)
        
        self.logger.info(f"所有年份收集完成，共获得 {len(all_patch_pairs)} 个补丁对")
        
        # 保存数据
        collection_date = datetime.now().strftime("%Y%m%d")
        saved_files = self.data_storage.save_patch_pairs(all_patch_pairs, collection_date)
        
        return saved_files
    
    def collect_year_data(self, year: int, end_date: datetime = None) -> List[PatchPair]:
        """
        收集指定年份的数据
        
        Args:
            year: 年份
            end_date: 结束日期，如果为None则使用年末
            
        Returns:
            补丁对列表
        """
        if end_date is None:
            end_date = datetime(year, 12, 31, 23, 59, 59)
        
        # 收集CVE数据
        self.logger.info(f"收集{year}年的CVE数据")
        cve_list = self.nvd_collector.fetch_cves_by_year(year)
        
        if not cve_list:
            self.logger.warning(f"{year}年没有找到CVE数据")
            return []
        
        self.logger.info(f"{year}年共找到 {len(cve_list)} 个CVE")
        
        # 过滤已存在的CVE
        new_cves = [cve for cve in cve_list if cve.cve_id not in self.existing_cves]
        self.logger.info(f"过滤已存在CVE后剩余 {len(new_cves)} 个")
        
        if not new_cves:
            self.logger.info(f"{year}年没有新的CVE数据")
            return []
        
        # 处理CVE数据
        year_patch_pairs = []
        processed_count = 0
        
        for cve_info in new_cves:
            try:
                patch_pairs = self.process_single_cve(cve_info)
                year_patch_pairs.extend(patch_pairs)
                
                processed_count += 1
                if processed_count % 10 == 0:
                    self.logger.info(f"已处理 {processed_count}/{len(new_cves)} 个CVE")
                
                # 控制处理速度
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"处理CVE {cve_info.cve_id} 失败: {e}")
                continue
        
        self.logger.info(f"{year}年处理完成，生成 {len(year_patch_pairs)} 个补丁对")
        return year_patch_pairs
    
    def process_single_cve(self, cve_info: CVEInfo) -> List[PatchPair]:
        """
        处理单个CVE
        
        Args:
            cve_info: CVE信息
            
        Returns:
            补丁对列表
        """
        # 从references中提取补丁
        commits = self.patch_extractor.extract_patches_from_references(
            cve_info.references, cve_info.cve_id
        )
        
        if not commits:
            self.logger.debug(f"CVE {cve_info.cve_id} 没有找到补丁提交")
            return []
        
        # 应用Mystique过滤器
        if Config.ENABLE_MYSTIQUE_FILTER:
            # 先过滤C/C++项目
            if Config.REQUIRE_C_CPP_PROJECT:
                commits = self.mystique_filter.filter_c_cpp_projects(commits)
            
            # 应用Mystique标准
            commits = self.mystique_filter.apply_mystique_criteria(commits, cve_info)
        
        if len(commits) < Config.MIN_COMMITS_FOR_PAIR:
            self.logger.debug(f"CVE {cve_info.cve_id} 筛选后提交数不足: {len(commits)}")
            return []
        
        # 生成补丁对
        patch_pairs = self.patch_pair_generator.generate_patch_pairs(cve_info, commits)
        
        # 去重
        patch_pairs = self.patch_pair_generator.filter_duplicate_pairs(patch_pairs)
        
        return patch_pairs
    
    def run(self):
        """运行收集器"""
        start_time = time.time()
        
        try:
            self.logger.info("NVD Patch Pair收集器启动")
            self.logger.info(f"配置信息:")
            self.logger.info(f"  - 时间范围: {Config.START_YEAR}-{Config.END_YEAR}")
            self.logger.info(f"  - 输出目录: {Config.OUTPUT_DIR}")
            self.logger.info(f"  - 启用Mystique过滤: {Config.ENABLE_MYSTIQUE_FILTER}")
            self.logger.info(f"  - 要求C/C++项目: {Config.REQUIRE_C_CPP_PROJECT}")
            
            # 收集数据
            saved_files = self.collect_all_data()
            
            # 输出结果
            self.logger.info("收集完成！保存的文件:")
            for category, filepath in saved_files.items():
                self.logger.info(f"  - {category}: {filepath}")
            
            elapsed_time = time.time() - start_time
            self.logger.info(f"总耗时: {elapsed_time:.2f} 秒")
            
        except KeyboardInterrupt:
            self.logger.info("收集被用户中断")
        except Exception as e:
            self.logger.error(f"收集过程中发生错误: {e}", exc_info=True)
        finally:
            self.logger.info("NVD Patch Pair收集器结束")


def main():
    """主函数"""
    setup_logging()
    
    collector = NVDPatchPairCollector()
    collector.run()


if __name__ == "__main__":
    main()
