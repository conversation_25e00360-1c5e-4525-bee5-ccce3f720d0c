#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD Patch Pair数据模型

定义补丁对的数据结构，符合需求文档要求
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
import json


@dataclass
class ProjectInfo:
    """项目信息"""
    name: str
    repo: str
    language: str


@dataclass
class VersionInfo:
    """版本信息"""
    version: str = "unknown"
    commit: str = "unknown"


@dataclass
class ProjectVersions:
    """项目版本信息"""
    vulnerable: VersionInfo = field(default_factory=VersionInfo)
    patched: VersionInfo = field(default_factory=VersionInfo)


@dataclass
class PatchPair:
    """补丁对数据模型 - 符合需求文档格式"""
    unified_id: str
    cve_id: str
    patch_type: str  # cross_repo | same_repo
    projects: Dict[str, ProjectInfo]  # source, target
    versions: Dict[str, ProjectVersions]  # source, target
    source_dataset: str = "NVD-PatchPair"
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.unified_id:
            self.generate_unified_id()
    
    def generate_unified_id(self):
        """生成统一ID: {cve_id}_{source_project}_{target_project}_{target_version_hash}"""
        source_project = self.projects.get('source')
        target_project = self.projects.get('target')
        target_versions = self.versions.get('target')

        source_name = source_project.name.replace(' ', '_') if source_project else 'unknown'
        target_name = target_project.name.replace(' ', '_') if target_project else 'unknown'
        
        # target_version_hash如果无，可以用所在版本号代替
        target_version_hash = 'unknown'
        if target_versions:
            if target_versions.patched.commit != "unknown":
                target_version_hash = target_versions.patched.commit[:8]  # 取前8位
            elif target_versions.patched.version != "unknown":
                target_version_hash = target_versions.patched.version

        self.unified_id = f"{self.cve_id}_{source_name}_{target_name}_{target_version_hash}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式 - 符合需求文档格式"""
        return {
            "unified_id": self.unified_id,
            "cve_id": self.cve_id,
            "patch_type": self.patch_type,
            "projects": {
                "source": {
                    "name": self.projects['source'].name,
                    "repo": self.projects['source'].repo,
                    "language": self.projects['source'].language
                },
                "target": {
                    "name": self.projects['target'].name,
                    "repo": self.projects['target'].repo,
                    "language": self.projects['target'].language
                }
            },
            "versions": {
                "source": {
                    "vulnerable": {
                        "version": self.versions['source'].vulnerable.version,
                        "commit": self.versions['source'].vulnerable.commit
                    },
                    "patched": {
                        "version": self.versions['source'].patched.version,
                        "commit": self.versions['source'].patched.commit
                    }
                },
                "target": {
                    "vulnerable": {
                        "version": self.versions['target'].vulnerable.version,
                        "commit": self.versions['target'].vulnerable.commit
                    },
                    "patched": {
                        "version": self.versions['target'].patched.version,
                        "commit": self.versions['target'].patched.commit
                    }
                }
            },
            "source_dataset": self.source_dataset,
            "metadata": self.metadata
        }


@dataclass
class CommitInfo:
    """提交信息"""
    sha: str
    message: str
    author: str
    date: datetime
    repo_owner: str
    repo_name: str
    repo_url: str
    files_changed: List[str] = field(default_factory=list)
    additions: int = 0
    deletions: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "sha": self.sha,
            "message": self.message,
            "author": self.author,
            "date": self.date.isoformat() if self.date else None,
            "repo_owner": self.repo_owner,
            "repo_name": self.repo_name,
            "repo_url": self.repo_url,
            "files_changed": self.files_changed,
            "additions": self.additions,
            "deletions": self.deletions
        }


@dataclass
class CVEInfo:
    """CVE信息"""
    cve_id: str
    published_date: datetime
    last_modified_date: datetime
    description: str
    cvss_score: Optional[float] = None
    cvss_severity: Optional[str] = None
    cwe_ids: List[str] = field(default_factory=list)
    references: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "cve_id": self.cve_id,
            "published_date": self.published_date.isoformat() if self.published_date else None,
            "last_modified_date": self.last_modified_date.isoformat() if self.last_modified_date else None,
            "description": self.description,
            "cvss_score": self.cvss_score,
            "cvss_severity": self.cvss_severity,
            "cwe_ids": self.cwe_ids,
            "references": self.references
        }


def determine_patch_type(source_repo: str, target_repo: str) -> str:
    """确定补丁类型"""
    if source_repo == target_repo:
        return "same_repo"
    else:
        return "cross_repo"


def determine_patch_pair_category(source_repo: str, target_repo: str, 
                                source_version: str, target_version: str) -> str:
    """确定补丁对类别"""
    if source_repo != target_repo:
        return "cross_repo"
    elif source_version != target_version:
        return "cross_version"
    else:
        return "cross_branch"


def extract_project_name_from_repo(repo_url: str) -> str:
    """从仓库URL提取项目名称"""
    if not repo_url:
        return "unknown"
    
    # 处理GitHub URL
    if "github.com" in repo_url:
        parts = repo_url.rstrip('/').split('/')
        if len(parts) >= 2:
            return parts[-1]  # 仓库名
    
    # 处理其他URL
    parts = repo_url.rstrip('/').split('/')
    if parts:
        return parts[-1]
    
    return "unknown"


def is_c_cpp_project(repo_url: str, files_changed: List[str] = None) -> bool:
    """判断是否为C/C++项目"""
    if files_changed:
        c_cpp_extensions = {'.c', '.cpp', '.cc', '.cxx', '.h', '.hpp', '.hxx'}
        for file_path in files_changed:
            for ext in c_cpp_extensions:
                if file_path.endswith(ext):
                    return True
    
    # 基于仓库名称的简单判断
    repo_lower = repo_url.lower()
    c_cpp_indicators = ['linux', 'kernel', 'gcc', 'glibc', 'openssl', 'nginx', 'apache']
    for indicator in c_cpp_indicators:
        if indicator in repo_lower:
            return True
    
    return False
