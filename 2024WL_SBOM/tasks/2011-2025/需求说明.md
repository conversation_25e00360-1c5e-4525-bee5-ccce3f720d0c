- 现在我需要收集2011-2025年的nvd-patchpair数据，并进行分析。要求如下：
    - 补丁筛选需要满足mystique的筛选标准。
        1. 补丁代码与收集到的commit之一完全相同。
        2. 编辑距离相似度得分超过0.5，且提交消息与收集到的提交之一相同。
        3. 提交消息引用了收集到的提交的SHA值。
        4. 提交消息引用了收集到的CVE ID。
        5. 选择CVE补丁对。遵循PPatHF [35]，过滤了与漏洞无关的提交，并进一步提炼了涉及与C语言无关的文件或函数外部修改的提交。过滤后，按时间戳对所有提交进行排序，并选择每个漏洞的最早和最新提交以形成CVE补丁对。最早的提交被视为target patch commit，最新的提交被视为source patch commit。如果漏洞仅包含一个补丁，我们也会将其排除。
        6. 结果存储。将收集的patch pair根据两个时间保存（2023年8月23日和2022年07月01日以及原始收集到的全部结果）
    - 每一条记录的格式要求：
        ```
                {
        "unified_id": "{cve_id}_{source_project}_{target_project}_{target_version_hash}", // target_version_hash如果无，可以用所在版本号代替
        "cve_id": "CVE-2018-1118",
        "patch_type": "cross_repo|same_repo", // 明确标识补丁类型
        "projects": {
            "source": {
            "name": "Linux Kernel",
            "repo": "https://github.com/torvalds/linux",
            "language": "C"
            },
            "target": {
            "name": "Linux Kernel",
            "repo": "https://github.com/torvalds/linux",
            "language": "C"
            }
        },
        "versions": {
            "source": { // 有的项目vulnerable和patched只给出其中一个信息。若为空 使用unknown表示即可
            "vulnerable": {"version": "4.17", "commit": "55e49dc43a835b19567e62142cb1c87dc7db7b3c"}, // 有的项目version和commit只存在其一，如果为空用unknown标识
            "patched": {"version": "unknown", "commit": "unknown"} // 有的项目version和commit只存在其一，如果为空用unknown标识
            },
            "target": {
            "vulnerable": {"version": "unknown", "commit": "unknown"}, // 有的项目version和commit只存在其一，如果为空用unknown标识
            "patched": {"version": "4.9", "commit": "a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99"}
            }
        },
        "source_dataset": "FixMorph",
        "metadata": {
            // 该字段根据项目的特点走，不必统一
        }
        }
        ```
    - 其他要求：
        1. 收集到的数据不能和merged_dataset中CVE-ID重合
        2. 需要将收集到的CVE的CWE类别保存到metadata中
        3. 需要将收集到的CVE的发布日期保存到metadata中，方便后续根据时间去筛选。
        4. 需要将patch pair的类别保存到metadata中。
            - cross version:同repo，同branch，不同version
            - cross branch:同repo，不同branch
            - cross repo:不同repo
        5. 项目要求：必须是C/C++项目。