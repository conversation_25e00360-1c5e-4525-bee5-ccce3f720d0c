#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
完整收集脚本

收集2011-2025年8月4日的完整NVD patch pair数据
"""

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from main import NVDPatchPairCollector


def setup_logging():
    """设置日志系统"""
    log_filename = f"full_collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return log_filename


def main():
    """运行完整收集"""
    log_filename = setup_logging()
    
    print("🚀 开始完整NVD Patch Pair收集")
    print(f"收集范围: {Config.START_YEAR}-{Config.END_YEAR}年8月4日")
    print(f"日志文件: {log_filename}")
    print("=" * 60)
    
    start_time = datetime.now()
    
    try:
        collector = NVDPatchPairCollector()
        
        print(f"配置信息:")
        print(f"  - 时间范围: {Config.START_YEAR}-{Config.END_YEAR}")
        print(f"  - 输出目录: {Config.OUTPUT_DIR}")
        print(f"  - 启用Mystique过滤: {Config.ENABLE_MYSTIQUE_FILTER}")
        print(f"  - 要求C/C++项目: {Config.REQUIRE_C_CPP_PROJECT}")
        print(f"  - 已存在CVE数量: {len(collector.existing_cves)}")
        print()
        
        # 运行完整收集
        saved_files = collector.collect_all_data()
        
        # 输出结果
        end_time = datetime.now()
        elapsed_time = end_time - start_time
        
        print("\n" + "=" * 60)
        print("🎉 收集完成！")
        print(f"总耗时: {elapsed_time}")
        print(f"开始时间: {start_time}")
        print(f"结束时间: {end_time}")
        print()
        print("保存的文件:")
        for category, filepath in saved_files.items():
            print(f"  - {category}: {filepath}")
        print()
        print(f"详细日志: {log_filename}")
        
    except KeyboardInterrupt:
        print("\n收集被用户中断")
    except Exception as e:
        print(f"\n❌ 收集过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        end_time = datetime.now()
        elapsed_time = end_time - start_time
        print(f"\n程序结束，总运行时间: {elapsed_time}")


if __name__ == "__main__":
    main()
