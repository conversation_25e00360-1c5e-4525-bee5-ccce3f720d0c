#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据存储模块

实现按时间分类存储功能，生成不同时间版本的数据
"""

import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
from models.patch_pair import PatchPair

logger = logging.getLogger(__name__)


class DataStorage:
    """数据存储器"""
    
    def __init__(self, output_dir: str = "output"):
        """
        初始化数据存储器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 时间分割点
        self.time_cutoffs = {
            "2022-07-01": datetime(2022, 7, 1),
            "2023-08-23": datetime(2023, 8, 23)
        }
    
    def save_patch_pairs(self, patch_pairs: List[PatchPair], 
                        collection_date: Optional[str] = None) -> Dict[str, str]:
        """
        保存补丁对数据，按时间分类
        
        Args:
            patch_pairs: 补丁对列表
            collection_date: 收集日期，用于文件命名
            
        Returns:
            保存的文件路径字典
        """
        if not collection_date:
            collection_date = datetime.now().strftime("%Y%m%d")
        
        logger.info(f"保存 {len(patch_pairs)} 个补丁对到文件")
        
        # 按时间分类补丁对
        categorized_pairs = self._categorize_by_time(patch_pairs)
        
        # 保存文件
        saved_files = {}
        
        for category, pairs in categorized_pairs.items():
            if not pairs:
                continue
            
            filename = f"nvd_patch_pairs_{category}_{collection_date}.json"
            filepath = self.output_dir / filename
            
            # 转换为字典格式
            pairs_data = [pair.to_dict() for pair in pairs]
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(pairs_data, f, indent=2, ensure_ascii=False)
            
            saved_files[category] = str(filepath)
            logger.info(f"保存 {category} 版本: {len(pairs)} 个补丁对 -> {filepath}")
        
        # 生成收集报告
        report_file = self._generate_collection_report(patch_pairs, saved_files, collection_date)
        saved_files["report"] = report_file
        
        return saved_files
    
    def _categorize_by_time(self, patch_pairs: List[PatchPair]) -> Dict[str, List[PatchPair]]:
        """
        按时间分类补丁对
        
        Args:
            patch_pairs: 补丁对列表
            
        Returns:
            按时间分类的补丁对字典
        """
        categorized = {
            "2022-07-01": [],
            "2023-08-23": [],
            "all": []
        }
        
        for pair in patch_pairs:
            # 获取CVE发布日期
            published_date_str = pair.metadata.get("published_date")
            if not published_date_str:
                # 如果没有发布日期，放入all类别
                categorized["all"].append(pair)
                continue
            
            try:
                published_date = datetime.fromisoformat(published_date_str.replace('Z', '+00:00'))
                
                # 分类到不同时间段
                if published_date <= self.time_cutoffs["2022-07-01"]:
                    categorized["2022-07-01"].append(pair)
                elif published_date <= self.time_cutoffs["2023-08-23"]:
                    categorized["2023-08-23"].append(pair)
                
                # 所有数据都放入all类别
                categorized["all"].append(pair)
                
            except Exception as e:
                logger.warning(f"解析发布日期失败 {published_date_str}: {e}")
                categorized["all"].append(pair)
        
        logger.info(f"时间分类结果: 2022-07-01: {len(categorized['2022-07-01'])}, "
                   f"2023-08-23: {len(categorized['2023-08-23'])}, "
                   f"all: {len(categorized['all'])}")
        
        return categorized
    
    def _generate_collection_report(self, patch_pairs: List[PatchPair], 
                                  saved_files: Dict[str, str], 
                                  collection_date: str) -> str:
        """
        生成收集报告
        
        Args:
            patch_pairs: 补丁对列表
            saved_files: 保存的文件路径
            collection_date: 收集日期
            
        Returns:
            报告文件路径
        """
        # 统计信息
        stats = self._calculate_statistics(patch_pairs)
        
        report = {
            "collection_info": {
                "collection_date": collection_date,
                "total_patch_pairs": len(patch_pairs),
                "collection_time": datetime.now().isoformat()
            },
            "statistics": stats,
            "output_files": saved_files,
            "time_distribution": self._get_time_distribution(patch_pairs)
        }
        
        # 保存报告
        report_filename = f"collection_report_{collection_date}.json"
        report_filepath = self.output_dir / report_filename
        
        with open(report_filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"生成收集报告: {report_filepath}")
        return str(report_filepath)
    
    def _calculate_statistics(self, patch_pairs: List[PatchPair]) -> Dict[str, Any]:
        """
        计算统计信息
        
        Args:
            patch_pairs: 补丁对列表
            
        Returns:
            统计信息字典
        """
        if not patch_pairs:
            return {}
        
        # 补丁类型统计
        patch_types = {}
        for pair in patch_pairs:
            patch_type = pair.patch_type
            patch_types[patch_type] = patch_types.get(patch_type, 0) + 1
        
        # CVE统计
        cves = set(pair.cve_id for pair in patch_pairs)
        
        # 仓库统计
        repos = set()
        for pair in patch_pairs:
            repos.add(pair.projects['source'].repo)
            repos.add(pair.projects['target'].repo)
        
        # CWE统计
        cwe_counts = {}
        for pair in patch_pairs:
            cwe_categories = pair.metadata.get("cwe_categories", [])
            for cwe in cwe_categories:
                cwe_counts[cwe] = cwe_counts.get(cwe, 0) + 1
        
        return {
            "total_patch_pairs": len(patch_pairs),
            "unique_cves": len(cves),
            "unique_repositories": len(repos),
            "patch_type_distribution": patch_types,
            "top_cwe_categories": dict(sorted(cwe_counts.items(), 
                                            key=lambda x: x[1], reverse=True)[:10]),
            "repositories": list(repos)[:20]  # 只显示前20个仓库
        }
    
    def _get_time_distribution(self, patch_pairs: List[PatchPair]) -> Dict[str, int]:
        """
        获取时间分布统计
        
        Args:
            patch_pairs: 补丁对列表
            
        Returns:
            时间分布字典
        """
        time_dist = {}
        
        for pair in patch_pairs:
            published_date_str = pair.metadata.get("published_date")
            if not published_date_str:
                continue
            
            try:
                published_date = datetime.fromisoformat(published_date_str.replace('Z', '+00:00'))
                year = published_date.year
                time_dist[str(year)] = time_dist.get(str(year), 0) + 1
            except:
                continue
        
        return dict(sorted(time_dist.items()))
    
    def load_merged_dataset(self, merged_dataset_path: str) -> set:
        """
        加载已合并的数据集，获取已存在的CVE ID
        
        Args:
            merged_dataset_path: 合并数据集文件路径
            
        Returns:
            已存在的CVE ID集合
        """
        existing_cves = set()
        
        if not os.path.exists(merged_dataset_path):
            logger.warning(f"合并数据集文件不存在: {merged_dataset_path}")
            return existing_cves
        
        try:
            with open(merged_dataset_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for item in data:
                cve_id = item.get('cve_id')
                if cve_id:
                    existing_cves.add(cve_id)
            
            logger.info(f"从合并数据集中加载了 {len(existing_cves)} 个已存在的CVE ID")
            
        except Exception as e:
            logger.error(f"加载合并数据集失败: {e}")
        
        return existing_cves
    
    def filter_existing_cves(self, patch_pairs: List[PatchPair], 
                           existing_cves: set) -> List[PatchPair]:
        """
        过滤已存在的CVE
        
        Args:
            patch_pairs: 补丁对列表
            existing_cves: 已存在的CVE ID集合
            
        Returns:
            过滤后的补丁对列表
        """
        filtered_pairs = []
        
        for pair in patch_pairs:
            if pair.cve_id not in existing_cves:
                filtered_pairs.append(pair)
        
        logger.info(f"CVE去重: {len(patch_pairs)} -> {len(filtered_pairs)} 个补丁对")
        return filtered_pairs
