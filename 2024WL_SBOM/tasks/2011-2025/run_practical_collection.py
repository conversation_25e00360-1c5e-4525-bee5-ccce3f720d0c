#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实用收集脚本

使用较小的时间窗口收集NVD patch pair数据，避免API限制
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from main import NVDPatchPairCollector


def setup_logging():
    """设置日志系统"""
    log_filename = f"practical_collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_filename, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return log_filename


def main():
    """运行实用收集"""
    log_filename = setup_logging()
    
    print("🚀 开始实用NVD Patch Pair收集")
    print("使用较小时间窗口，避免API限制")
    print(f"日志文件: {log_filename}")
    print("=" * 60)
    
    start_time = datetime.now()
    
    try:
        collector = NVDPatchPairCollector()
        
        print(f"配置信息:")
        print(f"  - 启用Mystique过滤: {Config.ENABLE_MYSTIQUE_FILTER}")
        print(f"  - 要求C/C++项目: {Config.REQUIRE_C_CPP_PROJECT}")
        print(f"  - 已存在CVE数量: {len(collector.existing_cves)}")
        print()
        
        # 使用较小的时间窗口收集数据
        all_patch_pairs = []
        
        # 收集2019年1-6月的数据作为示例
        time_ranges = [
            (datetime(2019, 1, 1), datetime(2019, 3, 31, 23, 59, 59)),
            (datetime(2019, 4, 1), datetime(2019, 6, 30, 23, 59, 59)),
        ]
        
        for i, (start_date, end_date) in enumerate(time_ranges, 1):
            print(f"\n=== 收集时间段 {i}/{len(time_ranges)}: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} ===")
            
            # 获取CVE数据
            cve_list = collector.nvd_collector.fetch_cves_by_date_range(start_date, end_date)
            print(f"找到 {len(cve_list)} 个CVE")
            
            if not cve_list:
                print("没有找到CVE数据，跳过此时间段")
                continue
            
            # 限制处理数量
            max_cves = 100
            if len(cve_list) > max_cves:
                cve_list = cve_list[:max_cves]
                print(f"限制处理数量为 {max_cves} 个CVE")
            
            # 过滤已存在的CVE
            new_cves = [cve for cve in cve_list if cve.cve_id not in collector.existing_cves]
            print(f"过滤已存在CVE后剩余 {len(new_cves)} 个")
            
            if not new_cves:
                print("没有新的CVE数据，跳过此时间段")
                continue
            
            # 处理CVE数据
            period_patch_pairs = []
            processed_count = 0
            
            for cve_info in new_cves:
                try:
                    patch_pairs = collector.process_single_cve(cve_info)
                    
                    if patch_pairs:
                        period_patch_pairs.extend(patch_pairs)
                        print(f"  CVE {cve_info.cve_id}: 生成 {len(patch_pairs)} 个补丁对")
                    
                    processed_count += 1
                    
                    # 每处理20个CVE显示进度
                    if processed_count % 20 == 0:
                        print(f"  进度: {processed_count}/{len(new_cves)}, 已生成 {len(period_patch_pairs)} 个补丁对")
                    
                except Exception as e:
                    print(f"  CVE {cve_info.cve_id}: 处理失败 - {e}")
                    continue
            
            all_patch_pairs.extend(period_patch_pairs)
            print(f"时间段 {i} 完成: 处理了 {processed_count} 个CVE，生成了 {len(period_patch_pairs)} 个补丁对")
            print(f"累计补丁对数量: {len(all_patch_pairs)}")
        
        print(f"\n" + "=" * 60)
        print("🎉 收集完成！")
        
        if all_patch_pairs:
            # 保存结果
            collection_date = datetime.now().strftime("%Y%m%d_%H%M%S")
            saved_files = collector.data_storage.save_patch_pairs(all_patch_pairs, f"practical_{collection_date}")
            
            print(f"总共生成了 {len(all_patch_pairs)} 个补丁对")
            print("保存的文件:")
            for category, filepath in saved_files.items():
                print(f"  - {category}: {filepath}")
        else:
            print("没有生成补丁对数据")
        
        # 输出统计信息
        end_time = datetime.now()
        elapsed_time = end_time - start_time
        
        print(f"\n总耗时: {elapsed_time}")
        print(f"详细日志: {log_filename}")
        
    except KeyboardInterrupt:
        print("\n收集被用户中断")
    except Exception as e:
        print(f"\n❌ 收集过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        end_time = datetime.now()
        elapsed_time = end_time - start_time
        print(f"\n程序结束，总运行时间: {elapsed_time}")


if __name__ == "__main__":
    main()
