#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Mystique过滤器

实现符合Mystique标准的补丁筛选逻辑
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from models.patch_pair import CommitInfo, CVEInfo

logger = logging.getLogger(__name__)


class MystiqueFilter:
    """Mystique过滤器 - 实现mystique筛选标准"""
    
    def __init__(self):
        """初始化过滤器"""
        # C/C++文件扩展名
        self.c_cpp_extensions = {
            '.c', '.cpp', '.cc', '.cxx', '.c++',
            '.h', '.hpp', '.hh', '.hxx', '.h++',
            '.inc', '.inl'
        }
        
        # 与漏洞无关的文件模式
        self.irrelevant_patterns = [
            r'\.md$',           # 文档文件
            r'\.txt$',          # 文本文件
            r'\.rst$',          # reStructuredText
            r'\.py$',           # Python文件
            r'\.js$',           # JavaScript文件
            r'\.java$',         # Java文件
            r'\.go$',           # Go文件
            r'\.rs$',           # Rust文件
            r'/doc/',           # 文档目录
            r'/docs/',          # 文档目录
            r'/test/',          # 测试目录
            r'/tests/',         # 测试目录
            r'/example/',       # 示例目录
            r'/examples/',      # 示例目录
            r'Makefile',        # 构建文件
            r'CMakeLists\.txt', # CMake文件
            r'\.cmake$',        # CMake文件
            r'\.yml$',          # YAML文件
            r'\.yaml$',         # YAML文件
            r'\.json$',         # JSON文件
            r'\.xml$',          # XML文件
        ]
    
    def apply_mystique_criteria(self, commits: List[CommitInfo], cve_info: CVEInfo) -> List[CommitInfo]:
        """
        应用Mystique筛选标准
        
        Args:
            commits: 提交列表
            cve_info: CVE信息
            
        Returns:
            筛选后的提交列表
        """
        logger.info(f"对CVE {cve_info.cve_id} 的 {len(commits)} 个提交应用Mystique筛选标准")
        
        filtered_commits = []
        
        for commit in commits:
            # 标准1: 提交消息引用了收集到的提交的SHA值
            sha_reference_match = self._check_sha_reference(commit, commits)
            
            # 标准2: 提交消息引用了收集到的CVE ID
            cve_reference_match = self._check_cve_reference(commit, cve_info.cve_id)
            
            # 标准3: 过滤与漏洞无关的提交和非C语言文件的修改
            is_relevant_commit = self._check_commit_relevance(commit)
            
            # 记录筛选结果
            mystique_criteria = {
                'sha_reference_match': sha_reference_match,
                'cve_reference_match': cve_reference_match,
                'is_relevant_commit': is_relevant_commit
            }
            
            # 至少满足一个引用标准，且是相关提交
            if (sha_reference_match or cve_reference_match) and is_relevant_commit:
                # 将筛选信息添加到提交对象中（如果需要的话）
                filtered_commits.append(commit)
                logger.debug(f"提交 {commit.sha[:8]} 通过Mystique筛选: {mystique_criteria}")
            else:
                logger.debug(f"提交 {commit.sha[:8]} 未通过Mystique筛选: {mystique_criteria}")
        
        logger.info(f"Mystique筛选后剩余 {len(filtered_commits)} 个提交")
        return filtered_commits
    
    def _check_sha_reference(self, commit: CommitInfo, all_commits: List[CommitInfo]) -> bool:
        """
        检查提交消息是否引用了其他提交的SHA值
        
        Args:
            commit: 当前提交
            all_commits: 所有提交列表
            
        Returns:
            是否引用了SHA值
        """
        message = commit.message.lower()
        
        # 检查是否引用了其他提交的SHA
        for other_commit in all_commits:
            if other_commit.sha == commit.sha:
                continue
            
            # 检查完整SHA和短SHA
            full_sha = other_commit.sha.lower()
            short_sha = full_sha[:7]
            
            if full_sha in message or short_sha in message:
                return True
        
        # 检查通用SHA模式
        sha_patterns = [
            r'\b[a-f0-9]{7,40}\b',  # SHA哈希
            r'commit\s+[a-f0-9]{7,40}',  # commit <sha>
            r'cherry.pick.*[a-f0-9]{7,40}',  # cherry-pick
        ]
        
        for pattern in sha_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        
        return False
    
    def _check_cve_reference(self, commit: CommitInfo, cve_id: str) -> bool:
        """
        检查提交消息是否引用了CVE ID
        
        Args:
            commit: 提交信息
            cve_id: CVE标识符
            
        Returns:
            是否引用了CVE ID
        """
        message = commit.message.lower()
        cve_lower = cve_id.lower()
        
        # 直接匹配CVE ID
        if cve_lower in message:
            return True
        
        # 匹配CVE ID的变体
        cve_patterns = [
            cve_id.replace('-', ''),  # CVE20181118
            cve_id.replace('-', ' '),  # CVE 2018 1118
            re.escape(cve_id),  # 精确匹配
        ]
        
        for pattern in cve_patterns:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        
        return False
    
    def _check_commit_relevance(self, commit: CommitInfo) -> bool:
        """
        检查提交是否与漏洞相关
        
        Args:
            commit: 提交信息
            
        Returns:
            是否为相关提交
        """
        # 如果没有文件变更信息，基于提交消息判断
        if not commit.files_changed:
            return self._is_security_related_message(commit.message)
        
        # 检查是否包含C/C++文件
        has_c_cpp_files = False
        has_irrelevant_files = False
        
        for file_path in commit.files_changed:
            # 检查是否为C/C++文件
            if any(file_path.endswith(ext) for ext in self.c_cpp_extensions):
                has_c_cpp_files = True
            
            # 检查是否为无关文件
            if any(re.search(pattern, file_path, re.IGNORECASE) for pattern in self.irrelevant_patterns):
                has_irrelevant_files = True
        
        # 必须包含C/C++文件，且不能只修改无关文件
        if not has_c_cpp_files:
            return False
        
        # 如果只修改了无关文件，则过滤掉
        if has_irrelevant_files and not has_c_cpp_files:
            return False
        
        return True
    
    def _is_security_related_message(self, message: str) -> bool:
        """
        基于提交消息判断是否与安全相关
        
        Args:
            message: 提交消息
            
        Returns:
            是否与安全相关
        """
        security_keywords = [
            'fix', 'patch', 'security', 'vulnerability', 'exploit',
            'buffer overflow', 'memory leak', 'use after free',
            'double free', 'null pointer', 'bounds check',
            'sanitize', 'validate', 'check', 'prevent',
            'cve', 'bug', 'issue', 'flaw', 'hole'
        ]
        
        message_lower = message.lower()
        
        for keyword in security_keywords:
            if keyword in message_lower:
                return True
        
        return False
    
    def filter_c_cpp_projects(self, commits: List[CommitInfo]) -> List[CommitInfo]:
        """
        过滤出C/C++项目的提交
        
        Args:
            commits: 提交列表
            
        Returns:
            C/C++项目的提交列表
        """
        c_cpp_commits = []
        
        for commit in commits:
            if self._is_c_cpp_project(commit):
                c_cpp_commits.append(commit)
        
        logger.info(f"C/C++项目过滤: {len(commits)} -> {len(c_cpp_commits)}")
        return c_cpp_commits
    
    def _is_c_cpp_project(self, commit: CommitInfo) -> bool:
        """
        判断提交是否来自C/C++项目
        
        Args:
            commit: 提交信息
            
        Returns:
            是否为C/C++项目
        """
        # 基于仓库URL判断
        repo_url = commit.repo_url.lower()
        c_cpp_indicators = [
            'linux', 'kernel', 'gcc', 'glibc', 'openssl',
            'nginx', 'apache', 'curl', 'sqlite', 'redis',
            'postgresql', 'mysql', 'mariadb', 'mongodb'
        ]
        
        for indicator in c_cpp_indicators:
            if indicator in repo_url:
                return True
        
        # 基于文件扩展名判断
        if commit.files_changed:
            c_cpp_file_count = sum(
                1 for file_path in commit.files_changed
                if any(file_path.endswith(ext) for ext in self.c_cpp_extensions)
            )
            
            # 如果C/C++文件占比超过50%，认为是C/C++项目
            if c_cpp_file_count > len(commit.files_changed) * 0.5:
                return True
        
        return False
