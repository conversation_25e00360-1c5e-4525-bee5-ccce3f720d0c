#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub补丁搜索器

根据CVE ID搜索相关的补丁提交
"""

import requests
import time
import logging
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from models.patch_pair import CommitInfo

logger = logging.getLogger(__name__)


class GitHubSearcher:
    """GitHub补丁搜索器"""
    
    def __init__(self, github_token: Optional[str] = None):
        """
        初始化GitHub搜索器
        
        Args:
            github_token: GitHub API令牌
        """
        self.github_token = github_token
        self.session = requests.Session()
        
        # 设置请求头
        headers = {
            'User-Agent': 'NVD-PatchPair-Collector/1.0',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        if self.github_token:
            headers['Authorization'] = f'token {self.github_token}'
            logger.info("使用GitHub API令牌")
        else:
            logger.warning("未提供GitHub API令牌，将受到严格的速率限制")
        
        self.session.headers.update(headers)
        
        # 速率限制设置
        self.rate_limit_delay = 1.0 if not github_token else 0.1
    
    def search_commits_by_cve(self, cve_id: str, max_results: int = 100) -> List[CommitInfo]:
        """
        根据CVE ID搜索相关提交
        
        Args:
            cve_id: CVE标识符
            max_results: 最大结果数
            
        Returns:
            提交信息列表
        """
        logger.info(f"搜索CVE {cve_id} 相关的提交")
        
        commits = []
        
        # 搜索策略1: 直接搜索CVE ID
        commits.extend(self._search_commits_by_query(cve_id, max_results // 2))
        
        # 搜索策略2: 搜索CVE ID的变体
        cve_variants = self._generate_cve_variants(cve_id)
        for variant in cve_variants:
            if len(commits) >= max_results:
                break
            commits.extend(self._search_commits_by_query(variant, 10))
        
        # 去重
        unique_commits = self._deduplicate_commits(commits)
        
        logger.info(f"找到 {len(unique_commits)} 个相关提交")
        return unique_commits[:max_results]
    
    def _search_commits_by_query(self, query: str, max_results: int = 50) -> List[CommitInfo]:
        """
        使用查询字符串搜索提交
        
        Args:
            query: 搜索查询
            max_results: 最大结果数
            
        Returns:
            提交信息列表
        """
        commits = []
        
        # GitHub commit search API
        search_url = "https://api.github.com/search/commits"
        
        params = {
            'q': query,
            'sort': 'committer-date',
            'order': 'desc',
            'per_page': min(100, max_results)
        }
        
        try:
            response = self.session.get(search_url, params=params, timeout=30)
            
            if response.status_code == 403:
                logger.warning("GitHub API速率限制，等待重试")
                time.sleep(60)
                return commits
            
            response.raise_for_status()
            data = response.json()
            
            items = data.get('items', [])
            logger.info(f"查询 '{query}' 找到 {len(items)} 个提交")
            
            for item in items:
                try:
                    commit_info = self._parse_commit_item(item)
                    if commit_info:
                        commits.append(commit_info)
                except Exception as e:
                    logger.error(f"解析提交数据失败: {e}")
                    continue
            
            time.sleep(self.rate_limit_delay)
            
        except requests.exceptions.RequestException as e:
            logger.error(f"搜索提交失败: {e}")
        
        return commits
    
    def _parse_commit_item(self, item: Dict[str, Any]) -> Optional[CommitInfo]:
        """
        解析搜索结果中的提交项
        
        Args:
            item: GitHub API返回的提交项
            
        Returns:
            提交信息对象
        """
        try:
            sha = item.get('sha')
            if not sha:
                return None
            
            commit = item.get('commit', {})
            repository = item.get('repository', {})
            
            # 提取仓库信息
            repo_full_name = repository.get('full_name', '')
            repo_parts = repo_full_name.split('/')
            repo_owner = repo_parts[0] if len(repo_parts) > 0 else 'unknown'
            repo_name = repo_parts[1] if len(repo_parts) > 1 else 'unknown'
            repo_url = repository.get('html_url', '')
            
            # 提取提交信息
            message = commit.get('message', '')
            author_info = commit.get('author', {})
            author = author_info.get('name', 'unknown')
            date_str = author_info.get('date', '')
            
            # 解析日期
            commit_date = None
            if date_str:
                try:
                    commit_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                except:
                    pass
            
            return CommitInfo(
                sha=sha,
                message=message,
                author=author,
                date=commit_date or datetime.now(),
                repo_owner=repo_owner,
                repo_name=repo_name,
                repo_url=repo_url
            )
            
        except Exception as e:
            logger.error(f"解析提交项失败: {e}")
            return None
    
    def get_commit_details(self, owner: str, repo: str, sha: str) -> Optional[CommitInfo]:
        """
        获取提交的详细信息
        
        Args:
            owner: 仓库所有者
            repo: 仓库名称
            sha: 提交哈希
            
        Returns:
            详细的提交信息
        """
        url = f"https://api.github.com/repos/{owner}/{repo}/commits/{sha}"
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # 提取文件变更信息
            files = data.get('files', [])
            files_changed = [f.get('filename', '') for f in files]
            additions = sum(f.get('additions', 0) for f in files)
            deletions = sum(f.get('deletions', 0) for f in files)
            
            # 基本信息
            commit = data.get('commit', {})
            message = commit.get('message', '')
            author_info = commit.get('author', {})
            author = author_info.get('name', 'unknown')
            date_str = author_info.get('date', '')
            
            # 解析日期
            commit_date = None
            if date_str:
                try:
                    commit_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                except:
                    pass
            
            return CommitInfo(
                sha=sha,
                message=message,
                author=author,
                date=commit_date or datetime.now(),
                repo_owner=owner,
                repo_name=repo,
                repo_url=f"https://github.com/{owner}/{repo}",
                files_changed=files_changed,
                additions=additions,
                deletions=deletions
            )
            
            time.sleep(self.rate_limit_delay)
            
        except Exception as e:
            logger.error(f"获取提交详情失败 {owner}/{repo}@{sha}: {e}")
            return None
    
    def _generate_cve_variants(self, cve_id: str) -> List[str]:
        """
        生成CVE ID的搜索变体
        
        Args:
            cve_id: CVE标识符
            
        Returns:
            CVE变体列表
        """
        variants = []
        
        # 基本变体
        variants.append(cve_id.replace('-', ''))  # CVE20181118
        variants.append(cve_id.lower())  # cve-2018-1118
        variants.append(cve_id.upper())  # CVE-2018-1118
        
        # 提取年份和编号
        match = re.match(r'CVE-(\d{4})-(\d+)', cve_id, re.IGNORECASE)
        if match:
            year, number = match.groups()
            variants.append(f"CVE {year} {number}")
            variants.append(f"CVE-{year}-{number.zfill(4)}")  # 补零
        
        return list(set(variants))  # 去重
    
    def _deduplicate_commits(self, commits: List[CommitInfo]) -> List[CommitInfo]:
        """
        去除重复的提交
        
        Args:
            commits: 提交列表
            
        Returns:
            去重后的提交列表
        """
        seen_shas = set()
        unique_commits = []
        
        for commit in commits:
            if commit.sha not in seen_shas:
                seen_shas.add(commit.sha)
                unique_commits.append(commit)
        
        return unique_commits
