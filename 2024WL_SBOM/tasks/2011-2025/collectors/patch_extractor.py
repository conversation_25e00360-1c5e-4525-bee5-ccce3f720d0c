#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
补丁提取器

从NVD的CVE references中提取补丁信息
"""

import re
import requests
import logging
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse
from models.patch_pair import CommitInfo
from datetime import datetime

logger = logging.getLogger(__name__)


class PatchExtractor:
    """补丁提取器 - 从CVE references中提取补丁信息"""
    
    def __init__(self, github_token: Optional[str] = None):
        """
        初始化补丁提取器
        
        Args:
            github_token: GitHub API令牌，用于获取提交详情
        """
        self.github_token = github_token
        self.session = requests.Session()
        
        if github_token:
            self.session.headers.update({
                'Authorization': f'token {github_token}',
                'Accept': 'application/vnd.github.v3+json'
            })
    
    def extract_patches_from_references(self, references: List[str], cve_id: str) -> List[CommitInfo]:
        """
        从CVE references中提取补丁信息
        
        Args:
            references: CVE参考链接列表
            cve_id: CVE标识符
            
        Returns:
            提交信息列表
        """
        patches = []
        
        for ref_url in references:
            try:
                # 解析GitHub提交链接
                github_commit = self._parse_github_commit_url(ref_url)
                if github_commit:
                    owner, repo, sha = github_commit
                    commit_info = self._get_github_commit_details(owner, repo, sha)
                    if commit_info:
                        patches.append(commit_info)
                        continue
                
                # 解析其他Git平台的提交链接
                git_commit = self._parse_git_commit_url(ref_url)
                if git_commit:
                    commit_info = self._create_commit_info_from_url(ref_url, git_commit, cve_id)
                    if commit_info:
                        patches.append(commit_info)
                
            except Exception as e:
                logger.debug(f"解析参考链接失败 {ref_url}: {e}")
                continue
        
        logger.info(f"从CVE {cve_id} 的references中提取到 {len(patches)} 个补丁")
        return patches
    
    def _parse_github_commit_url(self, url: str) -> Optional[Tuple[str, str, str]]:
        """
        解析GitHub提交URL
        
        Args:
            url: GitHub URL
            
        Returns:
            (owner, repo, sha) 元组，如果不是GitHub提交URL则返回None
        """
        # GitHub提交URL模式
        patterns = [
            r'github\.com/([^/]+)/([^/]+)/commit/([a-f0-9]{7,40})',
            r'github\.com/([^/]+)/([^/]+)/commits/([a-f0-9]{7,40})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                owner, repo, sha = match.groups()
                return owner, repo, sha
        
        return None
    
    def _parse_git_commit_url(self, url: str) -> Optional[str]:
        """
        解析其他Git平台的提交URL

        Args:
            url: Git提交URL

        Returns:
            提交SHA，如果不是Git提交URL则返回None
        """
        # 通用Git提交SHA模式
        sha_patterns = [
            r'/commit/([a-f0-9]{7,40})',
            r'/commits/([a-f0-9]{7,40})',
            r'commit=([a-f0-9]{7,40})',
            r'id=([a-f0-9]{7,40})',
            r'h=([a-f0-9]{7,40})',
            r'commitdiff/([a-f0-9]{7,40})',  # cgit格式
            r'commit/\?id=([a-f0-9]{7,40})',  # 另一种cgit格式
        ]

        for pattern in sha_patterns:
            match = re.search(pattern, url, re.IGNORECASE)
            if match:
                return match.group(1)

        return None
    
    def _get_github_commit_details(self, owner: str, repo: str, sha: str) -> Optional[CommitInfo]:
        """
        获取GitHub提交的详细信息
        
        Args:
            owner: 仓库所有者
            repo: 仓库名称
            sha: 提交SHA
            
        Returns:
            提交信息对象
        """
        if not self.github_token:
            # 没有token时创建基本的提交信息
            return CommitInfo(
                sha=sha,
                message="",
                author="unknown",
                date=datetime.now(),
                repo_owner=owner,
                repo_name=repo,
                repo_url=f"https://github.com/{owner}/{repo}"
            )
        
        url = f"https://api.github.com/repos/{owner}/{repo}/commits/{sha}"
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # 提取提交信息
            commit = data.get('commit', {})
            message = commit.get('message', '')
            author_info = commit.get('author', {})
            author = author_info.get('name', 'unknown')
            date_str = author_info.get('date', '')
            
            # 解析日期
            commit_date = datetime.now()
            if date_str:
                try:
                    commit_date = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                except:
                    pass
            
            # 提取文件变更信息
            files = data.get('files', [])
            files_changed = [f.get('filename', '') for f in files]
            additions = sum(f.get('additions', 0) for f in files)
            deletions = sum(f.get('deletions', 0) for f in files)
            
            return CommitInfo(
                sha=sha,
                message=message,
                author=author,
                date=commit_date,
                repo_owner=owner,
                repo_name=repo,
                repo_url=f"https://github.com/{owner}/{repo}",
                files_changed=files_changed,
                additions=additions,
                deletions=deletions
            )
            
        except Exception as e:
            logger.debug(f"获取GitHub提交详情失败 {owner}/{repo}@{sha}: {e}")
            # 返回基本信息
            return CommitInfo(
                sha=sha,
                message="",
                author="unknown",
                date=datetime.now(),
                repo_owner=owner,
                repo_name=repo,
                repo_url=f"https://github.com/{owner}/{repo}"
            )
    
    def _create_commit_info_from_url(self, url: str, sha: str, cve_id: str) -> Optional[CommitInfo]:
        """
        从URL创建提交信息对象
        
        Args:
            url: 提交URL
            sha: 提交SHA
            cve_id: CVE标识符
            
        Returns:
            提交信息对象
        """
        try:
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            # 尝试从URL路径中提取仓库信息
            path_parts = parsed_url.path.strip('/').split('/')
            
            repo_owner = "unknown"
            repo_name = "unknown"
            
            if len(path_parts) >= 2:
                repo_owner = path_parts[0]
                repo_name = path_parts[1]
            
            repo_url = f"{parsed_url.scheme}://{domain}/{repo_owner}/{repo_name}"
            
            return CommitInfo(
                sha=sha,
                message=f"Patch for {cve_id}",
                author="unknown",
                date=datetime.now(),
                repo_owner=repo_owner,
                repo_name=repo_name,
                repo_url=repo_url
            )
            
        except Exception as e:
            logger.debug(f"创建提交信息失败 {url}: {e}")
            return None
    
    def is_patch_reference(self, url: str) -> bool:
        """
        判断URL是否为补丁引用
        
        Args:
            url: 参考URL
            
        Returns:
            是否为补丁引用
        """
        # 补丁相关的关键词
        patch_indicators = [
            'commit', 'patch', 'fix', 'diff', 'changeset',
            'commits', 'patches', 'fixes', 'diffs'
        ]
        
        url_lower = url.lower()
        
        # 检查URL中是否包含补丁相关关键词
        for indicator in patch_indicators:
            if indicator in url_lower:
                return True
        
        # 检查是否包含提交SHA模式
        if re.search(r'[a-f0-9]{7,40}', url):
            return True
        
        return False
