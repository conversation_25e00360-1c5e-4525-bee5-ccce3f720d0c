#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD数据收集器

从NVD API获取CVE数据，包括CWE分类、发布日期等信息
"""

import requests
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from models.patch_pair import CVEInfo

logger = logging.getLogger(__name__)


class NVDCollector:
    """NVD数据收集器"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化NVD收集器
        
        Args:
            api_key: NVD API密钥，可选
        """
        self.base_url = "https://services.nvd.nist.gov/rest/json/cves/2.0"
        self.api_key = api_key
        self.session = requests.Session()
        
        # 设置请求头
        headers = {
            'User-Agent': 'NVD-PatchPair-Collector/1.0',
            'Accept': 'application/json'
        }
        
        if self.api_key:
            headers['apiKey'] = self.api_key
            logger.info("使用NVD API密钥，提高速率限制")
        else:
            logger.warning("未提供NVD API密钥，将受到速率限制")
        
        self.session.headers.update(headers)
        
        # 速率限制设置
        self.rate_limit_delay = 0.6 if not api_key else 0.1  # 无密钥时每分钟100次，有密钥时每分钟1000次
    
    def fetch_cves_by_year(self, year: int) -> List[CVEInfo]:
        """
        获取指定年份的所有CVE数据，按月分批收集

        Args:
            year: 年份

        Returns:
            CVE信息列表
        """
        logger.info(f"开始收集{year}年的CVE数据")

        all_cves = []

        # 按月收集数据，避免大时间范围的API问题
        for month in range(1, 13):
            try:
                # 计算月份的开始和结束日期
                if month == 12:
                    start_date = datetime(year, month, 1)
                    end_date = datetime(year, month, 31, 23, 59, 59)
                else:
                    start_date = datetime(year, month, 1)
                    # 下个月的第一天减1秒
                    next_month = datetime(year, month + 1, 1)
                    end_date = next_month - timedelta(seconds=1)

                logger.info(f"收集{year}年{month}月的数据")
                month_cves = self.fetch_cves_by_date_range(start_date, end_date)
                all_cves.extend(month_cves)

                logger.info(f"{year}年{month}月: 获取到 {len(month_cves)} 个CVE")

                # 月份间稍作休息
                time.sleep(1)

            except Exception as e:
                logger.error(f"收集{year}年{month}月数据失败: {e}")
                continue

        logger.info(f"{year}年收集完成，共获取 {len(all_cves)} 个CVE")
        return all_cves
    
    def fetch_cves_by_date_range(self, start_date: datetime, end_date: datetime) -> List[CVEInfo]:
        """
        获取指定日期范围内的CVE数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            CVE信息列表
        """
        all_cves = []
        start_index = 0
        results_per_page = 2000
        
        while True:
            params = {
                'pubStartDate': start_date.strftime('%Y-%m-%dT%H:%M:%S.000'),
                'pubEndDate': end_date.strftime('%Y-%m-%dT%H:%M:%S.999'),
                'resultsPerPage': results_per_page,
                'startIndex': start_index
            }
            
            logger.info(f"请求CVE数据: startIndex={start_index}, resultsPerPage={results_per_page}")
            
            try:
                response = self.session.get(self.base_url, params=params, timeout=30)

                # 处理404错误（可能是早期年份数据不可用）
                if response.status_code == 404:
                    logger.warning(f"年份 {start_date.year} 的数据不可用 (404)，跳过此时间段")
                    break

                response.raise_for_status()

                data = response.json()
                vulnerabilities = data.get('vulnerabilities', [])
                total_results = data.get('totalResults', 0)

                logger.info(f"获取到 {len(vulnerabilities)} 条CVE数据，总计 {total_results} 条")

                if not vulnerabilities:
                    break
                
                # 解析CVE数据
                for vuln_data in vulnerabilities:
                    try:
                        cve_info = self.parse_cve_data(vuln_data)
                        if cve_info:
                            all_cves.append(cve_info)
                    except Exception as e:
                        logger.error(f"解析CVE数据失败: {e}")
                        continue
                
                # 检查是否还有更多数据
                if start_index + len(vulnerabilities) >= total_results:
                    break
                
                start_index += len(vulnerabilities)
                
                # 速率限制
                time.sleep(self.rate_limit_delay)
                
            except requests.exceptions.RequestException as e:
                logger.error(f"请求NVD API失败: {e}")
                time.sleep(5)  # 错误时等待更长时间
                continue
            except Exception as e:
                logger.error(f"处理NVD响应失败: {e}")
                break
        
        logger.info(f"完成CVE数据收集，共获取 {len(all_cves)} 条有效数据")
        return all_cves
    
    def parse_cve_data(self, vuln_data: Dict[str, Any]) -> Optional[CVEInfo]:
        """
        解析单个CVE数据
        
        Args:
            vuln_data: NVD API返回的漏洞数据
            
        Returns:
            CVE信息对象
        """
        try:
            cve = vuln_data.get('cve', {})
            cve_id = cve.get('id')
            
            if not cve_id:
                return None
            
            # 基本信息
            published_date = datetime.fromisoformat(cve.get('published', '').replace('Z', '+00:00'))
            last_modified_date = datetime.fromisoformat(cve.get('lastModified', '').replace('Z', '+00:00'))
            
            # 描述信息
            descriptions = cve.get('descriptions', [])
            description = ""
            for desc in descriptions:
                if desc.get('lang') == 'en':
                    description = desc.get('value', '')
                    break
            
            # CVSS评分
            cvss_score = None
            cvss_severity = None
            metrics = cve.get('metrics', {})
            
            # 优先使用CVSSv3
            cvss_v3 = metrics.get('cvssMetricV31', []) or metrics.get('cvssMetricV30', [])
            if cvss_v3:
                cvss_data = cvss_v3[0].get('cvssData', {})
                cvss_score = cvss_data.get('baseScore')
                cvss_severity = cvss_data.get('baseSeverity')
            else:
                # 使用CVSSv2
                cvss_v2 = metrics.get('cvssMetricV2', [])
                if cvss_v2:
                    cvss_data = cvss_v2[0].get('cvssData', {})
                    cvss_score = cvss_data.get('baseScore')
                    cvss_severity = cvss_v2[0].get('baseSeverity')
            
            # CWE信息
            cwe_ids = []
            weaknesses = cve.get('weaknesses', [])
            for weakness in weaknesses:
                for desc in weakness.get('description', []):
                    if desc.get('lang') == 'en':
                        cwe_ids.append(desc.get('value', ''))
            
            # 参考链接
            references = []
            for ref in cve.get('references', []):
                references.append(ref.get('url', ''))
            
            return CVEInfo(
                cve_id=cve_id,
                published_date=published_date,
                last_modified_date=last_modified_date,
                description=description,
                cvss_score=cvss_score,
                cvss_severity=cvss_severity,
                cwe_ids=cwe_ids,
                references=references
            )
            
        except Exception as e:
            logger.error(f"解析CVE数据失败 {vuln_data.get('cve', {}).get('id', 'unknown')}: {e}")
            return None
    
    def get_cve_by_id(self, cve_id: str) -> Optional[CVEInfo]:
        """
        根据CVE ID获取单个CVE信息
        
        Args:
            cve_id: CVE标识符
            
        Returns:
            CVE信息对象
        """
        params = {'cveId': cve_id}
        
        try:
            response = self.session.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            vulnerabilities = data.get('vulnerabilities', [])
            
            if vulnerabilities:
                return self.parse_cve_data(vulnerabilities[0])
            
        except Exception as e:
            logger.error(f"获取CVE {cve_id} 失败: {e}")
        
        return None
