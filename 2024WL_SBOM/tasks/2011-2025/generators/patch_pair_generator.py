#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
补丁对生成器

实现CVE补丁对的生成逻辑，包括时间排序、最早最新提交选择、补丁类型分类等
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from models.patch_pair import (
    PatchPair, CommitInfo, CVEInfo, ProjectInfo, VersionInfo, ProjectVersions,
    determine_patch_type, determine_patch_pair_category, extract_project_name_from_repo
)

logger = logging.getLogger(__name__)


class PatchPairGenerator:
    """补丁对生成器"""
    
    def __init__(self):
        """初始化生成器"""
        pass
    
    def generate_patch_pairs(self, cve_info: CVEInfo, commits: List[CommitInfo]) -> List[PatchPair]:
        """
        为CVE生成补丁对
        
        Args:
            cve_info: CVE信息
            commits: 筛选后的提交列表
            
        Returns:
            补丁对列表
        """
        if len(commits) < 2:
            logger.debug(f"CVE {cve_info.cve_id} 只有 {len(commits)} 个提交，无法生成补丁对")
            return []
        
        logger.info(f"为CVE {cve_info.cve_id} 生成补丁对，共 {len(commits)} 个提交")
        
        # 按时间戳排序提交
        sorted_commits = sorted(commits, key=lambda c: c.date)
        
        # 选择最早和最新的提交
        earliest_commit = sorted_commits[0]
        latest_commit = sorted_commits[-1]
        
        logger.info(f"最早提交: {earliest_commit.sha[:8]} ({earliest_commit.date})")
        logger.info(f"最新提交: {latest_commit.sha[:8]} ({latest_commit.date})")
        
        # 生成补丁对
        patch_pairs = []
        
        # 如果最早和最新是同一个提交，尝试其他组合
        if earliest_commit.sha == latest_commit.sha:
            logger.debug(f"CVE {cve_info.cve_id} 最早和最新提交相同，跳过")
            return []
        
        # 生成主要的补丁对（最早作为target，最新作为source）
        patch_pair = self._create_patch_pair(
            cve_info=cve_info,
            source_commit=latest_commit,  # source patch commit (最新)
            target_commit=earliest_commit  # target patch commit (最早)
        )
        
        if patch_pair:
            patch_pairs.append(patch_pair)
        
        # 如果有多个提交，可以生成更多的补丁对组合
        if len(sorted_commits) > 2:
            # 生成其他可能的补丁对
            additional_pairs = self._generate_additional_pairs(cve_info, sorted_commits)
            patch_pairs.extend(additional_pairs)
        
        logger.info(f"为CVE {cve_info.cve_id} 生成了 {len(patch_pairs)} 个补丁对")
        return patch_pairs
    
    def _create_patch_pair(self, cve_info: CVEInfo, source_commit: CommitInfo, 
                          target_commit: CommitInfo) -> Optional[PatchPair]:
        """
        创建单个补丁对
        
        Args:
            cve_info: CVE信息
            source_commit: 源提交（最新）
            target_commit: 目标提交（最早）
            
        Returns:
            补丁对对象
        """
        try:
            # 确定补丁类型
            patch_type = determine_patch_type(source_commit.repo_url, target_commit.repo_url)
            
            # 创建项目信息
            source_project = ProjectInfo(
                name=extract_project_name_from_repo(source_commit.repo_url),
                repo=source_commit.repo_url,
                language="C"  # 根据需求，必须是C/C++项目
            )
            
            target_project = ProjectInfo(
                name=extract_project_name_from_repo(target_commit.repo_url),
                repo=target_commit.repo_url,
                language="C"
            )
            
            # 创建版本信息
            source_versions = ProjectVersions(
                vulnerable=VersionInfo(version="unknown", commit="unknown"),
                patched=VersionInfo(version="unknown", commit=source_commit.sha)
            )
            
            target_versions = ProjectVersions(
                vulnerable=VersionInfo(version="unknown", commit="unknown"),
                patched=VersionInfo(version="unknown", commit=target_commit.sha)
            )
            
            # 创建元数据
            metadata = {
                "cwe_categories": cve_info.cwe_ids,
                "published_date": cve_info.published_date.isoformat(),
                "patch_pair_category": determine_patch_pair_category(
                    source_commit.repo_url, target_commit.repo_url,
                    "unknown", "unknown"
                ),
                "source_commit_date": source_commit.date.isoformat(),
                "target_commit_date": target_commit.date.isoformat(),
                "source_commit_message": source_commit.message,
                "target_commit_message": target_commit.message,
                "cvss_score": cve_info.cvss_score,
                "cvss_severity": cve_info.cvss_severity,
                "description": cve_info.description
            }
            
            # 创建补丁对
            patch_pair = PatchPair(
                unified_id="",  # 将在__post_init__中生成
                cve_id=cve_info.cve_id,
                patch_type=patch_type,
                projects={
                    "source": source_project,
                    "target": target_project
                },
                versions={
                    "source": source_versions,
                    "target": target_versions
                },
                source_dataset="NVD-PatchPair",
                metadata=metadata
            )
            
            return patch_pair
            
        except Exception as e:
            logger.error(f"创建补丁对失败: {e}")
            return None
    
    def _generate_additional_pairs(self, cve_info: CVEInfo, 
                                 sorted_commits: List[CommitInfo]) -> List[PatchPair]:
        """
        生成额外的补丁对组合
        
        Args:
            cve_info: CVE信息
            sorted_commits: 按时间排序的提交列表
            
        Returns:
            额外的补丁对列表
        """
        additional_pairs = []
        
        # 限制额外补丁对的数量，避免组合爆炸
        max_additional_pairs = 3
        
        # 生成一些中间的补丁对
        if len(sorted_commits) >= 3:
            middle_commits = sorted_commits[1:-1]
            
            for i, middle_commit in enumerate(middle_commits):
                if len(additional_pairs) >= max_additional_pairs:
                    break
                
                # 最早 -> 中间
                pair1 = self._create_patch_pair(cve_info, middle_commit, sorted_commits[0])
                if pair1:
                    additional_pairs.append(pair1)
                
                # 中间 -> 最新
                if len(additional_pairs) < max_additional_pairs:
                    pair2 = self._create_patch_pair(cve_info, sorted_commits[-1], middle_commit)
                    if pair2:
                        additional_pairs.append(pair2)
        
        return additional_pairs
    
    def filter_duplicate_pairs(self, patch_pairs: List[PatchPair]) -> List[PatchPair]:
        """
        过滤重复的补丁对
        
        Args:
            patch_pairs: 补丁对列表
            
        Returns:
            去重后的补丁对列表
        """
        seen_ids = set()
        unique_pairs = []
        
        for pair in patch_pairs:
            if pair.unified_id not in seen_ids:
                seen_ids.add(pair.unified_id)
                unique_pairs.append(pair)
        
        logger.info(f"去重: {len(patch_pairs)} -> {len(unique_pairs)} 个补丁对")
        return unique_pairs
    
    def group_pairs_by_repo(self, patch_pairs: List[PatchPair]) -> Dict[str, List[PatchPair]]:
        """
        按仓库分组补丁对
        
        Args:
            patch_pairs: 补丁对列表
            
        Returns:
            按仓库分组的补丁对字典
        """
        repo_groups = {}
        
        for pair in patch_pairs:
            source_repo = pair.projects['source'].repo
            target_repo = pair.projects['target'].repo
            
            # 使用源仓库作为分组键
            if source_repo not in repo_groups:
                repo_groups[source_repo] = []
            
            repo_groups[source_repo].append(pair)
        
        return repo_groups
    
    def get_statistics(self, patch_pairs: List[PatchPair]) -> Dict[str, Any]:
        """
        获取补丁对统计信息
        
        Args:
            patch_pairs: 补丁对列表
            
        Returns:
            统计信息字典
        """
        if not patch_pairs:
            return {}
        
        # 统计补丁类型
        patch_type_counts = {}
        for pair in patch_pairs:
            patch_type = pair.patch_type
            patch_type_counts[patch_type] = patch_type_counts.get(patch_type, 0) + 1
        
        # 统计仓库数量
        repos = set()
        for pair in patch_pairs:
            repos.add(pair.projects['source'].repo)
            repos.add(pair.projects['target'].repo)
        
        # 统计CVE数量
        cves = set(pair.cve_id for pair in patch_pairs)
        
        return {
            "total_pairs": len(patch_pairs),
            "patch_type_distribution": patch_type_counts,
            "unique_repositories": len(repos),
            "unique_cves": len(cves),
            "repositories": list(repos)
        }
