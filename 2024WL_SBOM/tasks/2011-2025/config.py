#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置文件

包含NVD Patch Pair收集器的所有配置参数
"""

import os
from datetime import datetime


class Config:
    """配置类"""
    
    # API配置
    NVD_API_KEY = os.getenv('NVD_API_KEY', '')
    GITHUB_TOKEN = os.getenv('GITHUB_TOKEN', '')
    
    # 时间范围配置
    START_YEAR = 2019  # 从2019年开始，因为更早的年份可能有API限制
    END_YEAR = 2025
    END_DATE = datetime(2025, 8, 4)  # 2025年8月4日
    
    # 输出配置
    OUTPUT_DIR = "output"
    MERGED_DATASET_PATH = "/home/<USER>/tasks/2011-2025-nvd-patchpair/merged_dataset.json"
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE = "nvd_patch_collection.log"
    
    # 收集配置
    MAX_COMMITS_PER_CVE = 50
    BATCH_SIZE = 100  # 每批处理的CVE数量
    
    # 速率限制配置
    NVD_RATE_LIMIT_DELAY = 0.6  # NVD API请求间隔（秒）
    GITHUB_RATE_LIMIT_DELAY = 1.0  # GitHub API请求间隔（秒）
    
    # 过滤配置
    ENABLE_MYSTIQUE_FILTER = True
    REQUIRE_C_CPP_PROJECT = True
    MIN_COMMITS_FOR_PAIR = 2
    
    # 时间分割点
    TIME_CUTOFFS = {
        "2022-07-01": datetime(2022, 7, 1),
        "2023-08-23": datetime(2023, 8, 23)
    }
    
    @classmethod
    def validate(cls):
        """验证配置"""
        if not cls.NVD_API_KEY:
            print("警告: 未设置NVD_API_KEY环境变量，将受到API速率限制")
        
        if not cls.GITHUB_TOKEN:
            print("警告: 未设置GITHUB_TOKEN环境变量，将受到严格的API速率限制")
        
        # 创建输出目录
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        
        return True


# 验证配置
Config.validate()
