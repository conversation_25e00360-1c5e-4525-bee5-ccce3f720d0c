#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
样本收集脚本

收集一年的数据作为示例，验证完整流程
"""

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from main import NVDPatchPairCollector


def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("sample_collection.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """运行样本收集"""
    setup_logging()
    
    print("🚀 开始样本NVD Patch Pair收集")
    print("收集范围: 2019年1月-2月（限制数据量）")
    print("=" * 50)
    
    try:
        collector = NVDPatchPairCollector()
        
        # 收集2019年1-2月的数据作为样本
        start_date = datetime(2019, 1, 1)
        end_date = datetime(2019, 2, 28, 23, 59, 59)
        
        print(f"收集时间范围: {start_date} 到 {end_date}")
        
        # 获取CVE数据
        cve_list = collector.nvd_collector.fetch_cves_by_date_range(start_date, end_date)
        print(f"找到 {len(cve_list)} 个CVE")
        
        if not cve_list:
            print("没有找到CVE数据")
            return
        
        # 限制处理数量，避免测试时间过长
        max_cves = 50
        if len(cve_list) > max_cves:
            cve_list = cve_list[:max_cves]
            print(f"限制处理数量为 {max_cves} 个CVE")
        
        # 过滤已存在的CVE
        new_cves = [cve for cve in cve_list if cve.cve_id not in collector.existing_cves]
        print(f"过滤已存在CVE后剩余 {len(new_cves)} 个")
        
        # 处理CVE数据
        all_patch_pairs = []
        processed_count = 0
        
        for cve_info in new_cves:
            try:
                print(f"\n处理CVE {cve_info.cve_id} ({processed_count+1}/{len(new_cves)})")
                patch_pairs = collector.process_single_cve(cve_info)
                
                if patch_pairs:
                    all_patch_pairs.extend(patch_pairs)
                    print(f"  ✅ 生成 {len(patch_pairs)} 个补丁对")
                else:
                    print(f"  ❌ 未生成补丁对")
                
                processed_count += 1
                
                # 每处理10个CVE显示进度
                if processed_count % 10 == 0:
                    print(f"\n进度: {processed_count}/{len(new_cves)}, 已生成 {len(all_patch_pairs)} 个补丁对")
                
            except Exception as e:
                print(f"  ❌ 处理失败: {e}")
                continue
        
        print(f"\n样本收集完成！")
        print(f"处理了 {processed_count} 个CVE")
        print(f"生成了 {len(all_patch_pairs)} 个补丁对")
        
        if all_patch_pairs:
            # 保存结果
            collection_date = datetime.now().strftime("%Y%m%d_%H%M%S")
            saved_files = collector.data_storage.save_patch_pairs(all_patch_pairs, f"sample_{collection_date}")
            
            print(f"\n保存的文件:")
            for category, filepath in saved_files.items():
                print(f"  {category}: {filepath}")
        else:
            print("没有生成补丁对数据")
        
    except KeyboardInterrupt:
        print("\n收集被用户中断")
    except Exception as e:
        print(f"\n❌ 收集过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
