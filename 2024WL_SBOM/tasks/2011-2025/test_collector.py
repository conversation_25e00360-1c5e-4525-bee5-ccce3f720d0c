#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试脚本

测试NVD Patch Pair收集器的各个组件
"""

import sys
import os
import logging
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from collectors.nvd_collector import NVDCollector
from collectors.patch_extractor import PatchExtractor
from filters.mystique_filter import MystiqueFilter
from generators.patch_pair_generator import PatchPairGenerator
from storage.data_storage import DataStorage
from models.patch_pair import CVEInfo, CommitInfo


def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)]
    )


def test_nvd_collector():
    """测试NVD收集器"""
    print("\n=== 测试NVD收集器 ===")
    
    collector = NVDCollector(Config.NVD_API_KEY)
    
    # 测试获取单个CVE (尝试一个可能包含GitHub链接的CVE)
    test_cve = collector.get_cve_by_id("CVE-2021-44228")  # Log4j漏洞，应该有GitHub链接
    if test_cve:
        print(f"✅ 成功获取CVE: {test_cve.cve_id}")
        print(f"   发布日期: {test_cve.published_date}")
        print(f"   CWE类别: {test_cve.cwe_ids}")
        print(f"   参考链接数: {len(test_cve.references)}")
        print(f"   参考链接示例:")
        for i, ref in enumerate(test_cve.references[:5]):
            print(f"     {i+1}. {ref}")
    else:
        print("❌ 获取CVE失败")
    
    # 测试获取年份数据（限制数量）
    print("\n测试获取2019年CVE数据（前5个）...")
    start_date = datetime(2019, 1, 1)
    end_date = datetime(2019, 1, 31)  # 只测试1月份
    
    cves = collector.fetch_cves_by_date_range(start_date, end_date)
    print(f"✅ 获取到 {len(cves)} 个CVE")
    
    if cves:
        sample_cve = cves[0]
        print(f"   示例CVE: {sample_cve.cve_id}")
        print(f"   描述: {sample_cve.description[:100]}...")
    
    return test_cve


def test_patch_extractor(cve_info):
    """测试补丁提取器"""
    print("\n=== 测试补丁提取器 ===")
    
    if not cve_info:
        print("❌ 没有CVE数据，跳过测试")
        return []
    
    extractor = PatchExtractor(Config.GITHUB_TOKEN)
    
    # 提取补丁
    commits = extractor.extract_patches_from_references(cve_info.references, cve_info.cve_id)
    
    print(f"✅ 从 {len(cve_info.references)} 个引用中提取到 {len(commits)} 个补丁")
    
    for i, commit in enumerate(commits[:3]):  # 只显示前3个
        print(f"   补丁{i+1}: {commit.repo_owner}/{commit.repo_name}@{commit.sha[:8]}")
        print(f"          消息: {commit.message[:50]}...")
    
    return commits


def test_mystique_filter(commits, cve_info):
    """测试Mystique过滤器"""
    print("\n=== 测试Mystique过滤器 ===")
    
    if not commits or not cve_info:
        print("❌ 没有提交数据，跳过测试")
        return []
    
    filter_obj = MystiqueFilter()
    
    # 测试C/C++项目过滤
    c_cpp_commits = filter_obj.filter_c_cpp_projects(commits)
    print(f"✅ C/C++项目过滤: {len(commits)} -> {len(c_cpp_commits)}")
    
    # 测试Mystique标准
    filtered_commits = filter_obj.apply_mystique_criteria(c_cpp_commits, cve_info)
    print(f"✅ Mystique标准过滤: {len(c_cpp_commits)} -> {len(filtered_commits)}")
    
    return filtered_commits


def test_patch_pair_generator(cve_info, commits):
    """测试补丁对生成器"""
    print("\n=== 测试补丁对生成器 ===")
    
    if not commits or not cve_info:
        print("❌ 没有提交数据，跳过测试")
        return []
    
    generator = PatchPairGenerator()
    
    # 生成补丁对
    patch_pairs = generator.generate_patch_pairs(cve_info, commits)
    print(f"✅ 生成补丁对: {len(patch_pairs)} 个")
    
    for i, pair in enumerate(patch_pairs[:2]):  # 只显示前2个
        print(f"   补丁对{i+1}: {pair.unified_id}")
        print(f"           类型: {pair.patch_type}")
        print(f"           源项目: {pair.projects['source'].name}")
        print(f"           目标项目: {pair.projects['target'].name}")
    
    return patch_pairs


def test_data_storage(patch_pairs):
    """测试数据存储"""
    print("\n=== 测试数据存储 ===")
    
    if not patch_pairs:
        print("❌ 没有补丁对数据，跳过测试")
        return
    
    storage = DataStorage("test_output")
    
    # 保存数据
    saved_files = storage.save_patch_pairs(patch_pairs, "test")
    
    print(f"✅ 保存文件:")
    for category, filepath in saved_files.items():
        print(f"   {category}: {filepath}")


def create_test_data():
    """创建测试数据"""
    print("\n=== 创建测试数据 ===")
    
    # 创建测试CVE
    test_cve = CVEInfo(
        cve_id="CVE-2018-1118",
        published_date=datetime(2018, 5, 1),
        last_modified_date=datetime(2018, 5, 2),
        description="Test vulnerability for buffer overflow",
        cvss_score=7.5,
        cvss_severity="HIGH",
        cwe_ids=["CWE-119", "CWE-120"],
        references=[
            "https://github.com/torvalds/linux/commit/670ae9caaca467ea1bfd325cb2a5c98ba87f94ad",
            "https://github.com/torvalds/linux/commit/a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99"
        ]
    )
    
    # 创建测试提交
    test_commits = [
        CommitInfo(
            sha="670ae9caaca467ea1bfd325cb2a5c98ba87f94ad",
            message="Fix CVE-2018-1118 buffer overflow in vhost driver",
            author="Test Author",
            date=datetime(2018, 4, 15),
            repo_owner="torvalds",
            repo_name="linux",
            repo_url="https://github.com/torvalds/linux",
            files_changed=["drivers/vhost/vhost.c", "drivers/vhost/vhost.h"],
            additions=10,
            deletions=5
        ),
        CommitInfo(
            sha="a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99",
            message="Backport fix for CVE-2018-1118 to stable branch",
            author="Test Author 2",
            date=datetime(2018, 4, 20),
            repo_owner="torvalds",
            repo_name="linux",
            repo_url="https://github.com/torvalds/linux",
            files_changed=["drivers/vhost/vhost.c"],
            additions=8,
            deletions=3
        )
    ]
    
    return test_cve, test_commits


def run_full_test():
    """运行完整测试"""
    print("🚀 开始NVD Patch Pair收集器测试")

    # 测试NVD收集器
    cve_info = test_nvd_collector()

    # 测试补丁提取器
    commits = []
    if cve_info:
        commits = test_patch_extractor(cve_info)

    # 如果没有提取到补丁，使用测试数据继续测试
    if not commits:
        print("\n没有从NVD references中提取到补丁，使用测试数据继续测试...")
        cve_info, test_commits = create_test_data()
        commits = test_commits

    # 测试Mystique过滤器
    filtered_commits = test_mystique_filter(commits, cve_info)

    # 测试补丁对生成器
    patch_pairs = test_patch_pair_generator(cve_info, filtered_commits)

    # 测试数据存储
    test_data_storage(patch_pairs)

    print("\n✅ 所有测试完成！")


def main():
    """主函数"""
    setup_test_logging()
    
    print("NVD Patch Pair收集器测试工具")
    print("=" * 50)
    
    try:
        run_full_test()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
