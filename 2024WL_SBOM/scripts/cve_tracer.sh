#!/bin/bash

# CVE追踪配置脚本
# 此脚本包含与CVE追踪相关的各种命令配置

# 描述: 追踪单个CVE
# 使用方法: bash cve_tracer.sh single CVE-2021-44225
trace_single_cve() {
    local cve_id="$1"
    echo "正在追踪CVE: $cve_id"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id"
}

# 描述: 追踪时间范围内的CVE
# 使用方法: bash cve_tracer.sh timerange 2022-01-01 2022-12-31
trace_timerange() {
    local start_date="$1"
    local end_date="$2"
    echo "正在追踪时间范围 $start_date 到 $end_date 内的CVE"
    python3 -m src.data_collection.services.patch_tracer.tracer --timerange --start_date "$start_date" --end_date "$end_date"
}

# 描述: 带调试信息的CVE追踪
# 使用方法: bash cve_tracer.sh debug CVE-2021-44225
trace_with_debug() {
    local cve_id="$1"
    echo "正在以调试模式追踪CVE: $cve_id"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --debug
}

# 描述: 使用特定语言过滤追踪CVE
# 使用方法: bash cve_tracer.sh lang CVE-2021-44225 python,java
trace_with_language() {
    local cve_id="$1"
    local languages="$2"
    echo "正在追踪CVE: $cve_id (仅限语言: $languages)"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --languages "$languages"
}

# 主函数
main() {
    case "$1" in
        single)
            if [ -z "$2" ]; then
                echo "错误: 需要提供CVE ID"
                echo "示例: $0 single CVE-2021-44225"
                exit 1
            fi
            trace_single_cve "$2"
            ;;
        timerange)
            if [ -z "$2" ] || [ -z "$3" ]; then
                echo "错误: 需要提供开始和结束日期"
                echo "示例: $0 timerange 2022-01-01 2022-12-31"
                exit 1
            fi
            trace_timerange "$2" "$3"
            ;;
        debug)
            if [ -z "$2" ]; then
                echo "错误: 需要提供CVE ID"
                echo "示例: $0 debug CVE-2021-44225"
                exit 1
            fi
            trace_with_debug "$2"
            ;;
        lang)
            if [ -z "$2" ] || [ -z "$3" ]; then
                echo "错误: 需要提供CVE ID和语言"
                echo "示例: $0 lang CVE-2021-44225 python,java"
                exit 1
            fi
            trace_with_language "$2" "$3"
            ;;
        *)
            echo "用法: $0 {single|timerange|debug|lang} [参数]"
            echo "示例:"
            echo "  $0 single CVE-2021-44225    # 追踪单个CVE"
            echo "  $0 timerange 2022-01-01 2022-12-31   # 追踪时间范围内的CVE"
            echo "  $0 debug CVE-2021-44225     # 带调试信息的追踪"
            echo "  $0 lang CVE-2021-44225 python,java   # 使用语言过滤的追踪"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 