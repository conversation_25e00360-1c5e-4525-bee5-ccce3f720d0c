#!/bin/bash

# 帮助脚本
# 提供关于所有配置脚本及其用法的信息

# 彩色输出设置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 打印标题
print_title() {
    echo -e "\n${GREEN}$1${NC}"
    echo -e "${GREEN}$(printf '=%.0s' $(seq 1 ${#1}))${NC}\n"
}

# 打印章节
print_section() {
    echo -e "${YELLOW}$1${NC}"
    echo -e "${YELLOW}$(printf '-%.0s' $(seq 1 ${#1}))${NC}\n"
}

# 打印命令示例
print_example() {
    echo -e "${BLUE}$1${NC}"
}

# 显示语言过滤配置
show_language_filter() {
    print_section "语言过滤配置 (language_filter.sh)"
    echo "用于按编程语言类型过滤补丁追踪结果的命令。"
    echo "示例用法:"
    print_example "  bash language_filter.sh python     # 仅处理Python文件"
    print_example "  bash language_filter.sh java       # 仅处理Java文件"
    print_example "  bash language_filter.sh c          # 仅处理C语言文件"
    print_example "  bash language_filter.sh cpp        # 仅处理C++文件"
    print_example "  bash language_filter.sh py_java    # 处理Python和Java文件"
    echo ""
}

# 显示CVE追踪配置
show_cve_tracer() {
    print_section "CVE追踪配置 (cve_tracer.sh)"
    echo "用于追踪单个CVE或时间范围内CVE的命令。"
    echo "示例用法:"
    print_example "  bash cve_tracer.sh single CVE-2021-44225                 # 追踪单个CVE"
    print_example "  bash cve_tracer.sh timerange 2022-01-01 2022-12-31       # 追踪时间范围内的CVE"
    print_example "  bash cve_tracer.sh debug CVE-2021-44225                  # 带调试信息的追踪"
    print_example "  bash cve_tracer.sh lang CVE-2021-44225 python,java       # 使用语言过滤的追踪"
    echo ""
}

# 显示输出配置
show_output_config() {
    print_section "输出配置 (output_config.sh)"
    echo "用于控制追踪结果的输出格式和目标的命令。"
    echo "示例用法:"
    print_example "  bash output_config.sh file CVE-2021-44225 output.json    # 输出到特定文件"
    print_example "  bash output_config.sh verbose CVE-2021-44225             # 以详细模式输出"
    print_example "  bash output_config.sh json CVE-2021-44225                # 输出JSON格式"
    print_example "  bash output_config.sh detailed CVE-2021-44225            # 输出包含详细补丁信息"
    echo ""
}

# 显示综合配置
show_combined_config() {
    print_section "综合配置 (combined_config.sh)"
    echo "整合了各种常用参数组合的配置命令。"
    echo "示例用法:"
    print_example "  bash combined_config.sh standard CVE-2021-44225                          # 标准追踪"
    print_example "  bash combined_config.sh full CVE-2021-44225 output.json                  # 完全追踪"
    print_example "  bash combined_config.sh light CVE-2021-44225                             # 轻量追踪"
    print_example "  bash combined_config.sh py_java_detailed CVE-2021-44225 output.json      # Python和Java详细追踪"
    print_example "  bash combined_config.sh timerange_multi 2022-01-01 2022-12-31 python,java,c output.json  # 时间范围内多语言追踪"
    print_example "  bash combined_config.sh dev_test CVE-2021-44225                          # 开发测试配置"
    echo ""
}

# 显示常见任务的快速指南
show_quick_guide() {
    print_section "常见任务快速指南"
    echo "1. 追踪单个CVE (最简单方式):"
    print_example "   bash combined_config.sh standard CVE-2021-44225"
    echo ""
    echo "2. 按语言过滤追踪结果:"
    print_example "   bash language_filter.sh python"
    echo ""
    echo "3. 获取详细调试信息:"
    print_example "   bash cve_tracer.sh debug CVE-2021-44225"
    echo ""
    echo "4. 将结果输出到文件:"
    print_example "   bash output_config.sh file CVE-2021-44225 my_output.json"
    echo ""
    echo "5. 追踪时间范围内的CVE:"
    print_example "   bash cve_tracer.sh timerange 2022-01-01 2022-12-31"
    echo ""
    echo "6. 完整追踪 (详细信息，输出到文件):"
    print_example "   bash combined_config.sh full CVE-2021-44225 detailed_output.json"
    echo ""
}

# 主函数
main() {
    print_title "补丁追踪系统配置脚本帮助"
    echo "此帮助文档提供了所有可用配置脚本的信息及其用法。"
    echo ""
    
    if [ "$1" == "" ]; then
        # 显示所有配置的概述
        show_language_filter
        show_cve_tracer
        show_output_config
        show_combined_config
        show_quick_guide
        
        echo -e "${GREEN}要查看特定配置脚本的详细信息，请使用:${NC}"
        echo "  bash help.sh language   # 查看语言过滤配置详情"
        echo "  bash help.sh cve        # 查看CVE追踪配置详情"
        echo "  bash help.sh output     # 查看输出配置详情"
        echo "  bash help.sh combined   # 查看综合配置详情"
        echo "  bash help.sh quick      # 查看常见任务快速指南"
    else
        # 显示特定配置的详情
        case "$1" in
            language)
                show_language_filter
                ;;
            cve)
                show_cve_tracer
                ;;
            output)
                show_output_config
                ;;
            combined)
                show_combined_config
                ;;
            quick)
                show_quick_guide
                ;;
            *)
                echo -e "${RED}错误:${NC} 未知的配置类型 '$1'"
                echo "可用的配置类型: language, cve, output, combined, quick"
                exit 1
                ;;
        esac
    fi
}

# 执行主函数
main "$@" 