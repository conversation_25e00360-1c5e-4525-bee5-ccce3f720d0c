#!/bin/bash

# 输出配置脚本
# 此脚本包含与输出格式和目标相关的各种命令配置

# 描述: 将追踪结果输出到特定文件
# 使用方法: bash output_config.sh file CVE-2021-44225 output.json
output_to_file() {
    local cve_id="$1"
    local output_file="$2"
    echo "正在追踪CVE: $cve_id 并输出到文件: $output_file"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --output_file "$output_file"
}

# 描述: 以详细模式输出到控制台
# 使用方法: bash output_config.sh verbose CVE-2021-44225
output_verbose() {
    local cve_id="$1"
    echo "正在以详细模式追踪CVE: $cve_id"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --verbose
}

# 描述: 输出JSON格式
# 使用方法: bash output_config.sh json CVE-2021-44225
output_json() {
    local cve_id="$1"
    echo "正在追踪CVE: $cve_id 并输出为JSON格式"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --format json
}

# 描述: 输出包含详细补丁信息
# 使用方法: bash output_config.sh detailed CVE-2021-44225
output_detailed() {
    local cve_id="$1"
    echo "正在追踪CVE: $cve_id 并包含详细补丁信息"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --include_patch_details
}

# 主函数
main() {
    case "$1" in
        file)
            if [ -z "$2" ] || [ -z "$3" ]; then
                echo "错误: 需要提供CVE ID和输出文件名"
                echo "示例: $0 file CVE-2021-44225 output.json"
                exit 1
            fi
            output_to_file "$2" "$3"
            ;;
        verbose)
            if [ -z "$2" ]; then
                echo "错误: 需要提供CVE ID"
                echo "示例: $0 verbose CVE-2021-44225"
                exit 1
            fi
            output_verbose "$2"
            ;;
        json)
            if [ -z "$2" ]; then
                echo "错误: 需要提供CVE ID"
                echo "示例: $0 json CVE-2021-44225"
                exit 1
            fi
            output_json "$2"
            ;;
        detailed)
            if [ -z "$2" ]; then
                echo "错误: 需要提供CVE ID"
                echo "示例: $0 detailed CVE-2021-44225"
                exit 1
            fi
            output_detailed "$2"
            ;;
        *)
            echo "用法: $0 {file|verbose|json|detailed} [参数]"
            echo "示例:"
            echo "  $0 file CVE-2021-44225 output.json  # 输出到特定文件"
            echo "  $0 verbose CVE-2021-44225           # 以详细模式输出"
            echo "  $0 json CVE-2021-44225              # 输出JSON格式"
            echo "  $0 detailed CVE-2021-44225          # 输出包含详细补丁信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 