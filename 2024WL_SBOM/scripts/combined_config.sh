#!/bin/bash

# 综合配置脚本
# 此脚本整合了各种常用的参数组合配置

# 描述: 针对单个CVE的标准跟踪 (默认设置)
# 使用方法: bash combined_config.sh standard CVE-2021-44225
standard_trace() {
    local cve_id="$1"
    echo "正在执行标准追踪，CVE: $cve_id"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id"
}

# 描述: 完全追踪 (包含详细日志、详细补丁信息，输出到文件)
# 使用方法: bash combined_config.sh full CVE-2021-44225 output.json
full_trace() {
    local cve_id="$1"
    local output_file="$2"
    echo "正在执行完全追踪，CVE: $cve_id，输出到: $output_file"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --verbose --debug --include_patch_details --output_file "$output_file"
}

# 描述: 轻量追踪 (最小化输出，适用于快速检查)
# 使用方法: bash combined_config.sh light CVE-2021-44225
light_trace() {
    local cve_id="$1"
    echo "正在执行轻量追踪，CVE: $cve_id"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --no_details
}

# 描述: 针对Python和Java的详细追踪
# 使用方法: bash combined_config.sh py_java_detailed CVE-2021-44225 output.json
py_java_detailed() {
    local cve_id="$1"
    local output_file="$2"
    echo "正在执行针对Python和Java的详细追踪，CVE: $cve_id，输出到: $output_file"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --languages python,java --verbose --include_patch_details --output_file "$output_file"
}

# 描述: 针对时间范围的多语言追踪
# 使用方法: bash combined_config.sh timerange_multi 2022-01-01 2022-12-31 python,java,c output.json
timerange_multi() {
    local start_date="$1"
    local end_date="$2"
    local languages="$3"
    local output_file="$4"
    echo "正在执行时间范围内多语言追踪，时间: $start_date 到 $end_date，语言: $languages，输出到: $output_file"
    python3 -m src.data_collection.services.patch_tracer.tracer --timerange --start_date "$start_date" --end_date "$end_date" --languages "$languages" --output_file "$output_file"
}

# 描述: 开发测试配置 (带详细调试信息，仅Python，小范围)
# 使用方法: bash combined_config.sh dev_test CVE-2021-44225
dev_test() {
    local cve_id="$1"
    echo "正在执行开发测试配置，CVE: $cve_id"
    python3 -m src.data_collection.services.patch_tracer.tracer --cve_id "$cve_id" --languages python --debug --verbose --limit 5
}

# 主函数
main() {
    case "$1" in
        standard)
            if [ -z "$2" ]; then
                echo "错误: 需要提供CVE ID"
                echo "示例: $0 standard CVE-2021-44225"
                exit 1
            fi
            standard_trace "$2"
            ;;
        full)
            if [ -z "$2" ] || [ -z "$3" ]; then
                echo "错误: 需要提供CVE ID和输出文件"
                echo "示例: $0 full CVE-2021-44225 output.json"
                exit 1
            fi
            full_trace "$2" "$3"
            ;;
        light)
            if [ -z "$2" ]; then
                echo "错误: 需要提供CVE ID"
                echo "示例: $0 light CVE-2021-44225"
                exit 1
            fi
            light_trace "$2"
            ;;
        py_java_detailed)
            if [ -z "$2" ] || [ -z "$3" ]; then
                echo "错误: 需要提供CVE ID和输出文件"
                echo "示例: $0 py_java_detailed CVE-2021-44225 output.json"
                exit 1
            fi
            py_java_detailed "$2" "$3"
            ;;
        timerange_multi)
            if [ -z "$2" ] || [ -z "$3" ] || [ -z "$4" ] || [ -z "$5" ]; then
                echo "错误: 需要提供开始日期、结束日期、语言列表和输出文件"
                echo "示例: $0 timerange_multi 2022-01-01 2022-12-31 python,java,c output.json"
                exit 1
            fi
            timerange_multi "$2" "$3" "$4" "$5"
            ;;
        dev_test)
            if [ -z "$2" ]; then
                echo "错误: 需要提供CVE ID"
                echo "示例: $0 dev_test CVE-2021-44225"
                exit 1
            fi
            dev_test "$2"
            ;;
        *)
            echo "用法: $0 {standard|full|light|py_java_detailed|timerange_multi|dev_test} [参数]"
            echo "示例:"
            echo "  $0 standard CVE-2021-44225                         # 标准追踪"
            echo "  $0 full CVE-2021-44225 output.json                 # 完全追踪"
            echo "  $0 light CVE-2021-44225                            # 轻量追踪"
            echo "  $0 py_java_detailed CVE-2021-44225 output.json     # Python和Java详细追踪"
            echo "  $0 timerange_multi 2022-01-01 2022-12-31 python,java,c output.json  # 时间范围内多语言追踪"
            echo "  $0 dev_test CVE-2021-44225                         # 开发测试配置"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 