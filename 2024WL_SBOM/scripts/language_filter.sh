#!/bin/bash

# 语言过滤配置脚本
# 此脚本包含与语言过滤相关的各种命令配置

# 描述: 仅处理Python文件的补丁跟踪
# 使用方法: bash language_filter.sh python
PYTHON_ONLY="python3 -m src.data_collection.services.patch_tracer.tracer --cve_id CVE-2021-44225 --languages python"

# 描述: 仅处理Java文件的补丁跟踪
# 使用方法: bash language_filter.sh java
JAVA_ONLY="python3 -m src.data_collection.services.patch_tracer.tracer --cve_id CVE-2021-44225 --languages java"

# 描述: 仅处理C语言文件的补丁跟踪
# 使用方法: bash language_filter.sh c
C_ONLY="python3 -m src.data_collection.services.patch_tracer.tracer --cve_id CVE-2021-44225 --languages c"

# 描述: 仅处理C++文件的补丁跟踪
# 使用方法: bash language_filter.sh cpp
CPP_ONLY="python3 -m src.data_collection.services.patch_tracer.tracer --cve_id CVE-2021-44225 --languages cpp"

# 描述: 处理多种语言文件的补丁跟踪(Python和Java)
# 使用方法: bash language_filter.sh py_java
PY_JAVA="python3 -m src.data_collection.services.patch_tracer.tracer --cve_id CVE-2021-44225 --languages python,java"

# 主函数
main() {
    case "$1" in
        python)
            echo "执行Python语言过滤命令..."
            eval "$PYTHON_ONLY"
            ;;
        java)
            echo "执行Java语言过滤命令..."
            eval "$JAVA_ONLY"
            ;;
        c)
            echo "执行C语言过滤命令..."
            eval "$C_ONLY"
            ;;
        cpp)
            echo "执行C++语言过滤命令..."
            eval "$CPP_ONLY"
            ;;
        py_java)
            echo "执行Python和Java语言过滤命令..."
            eval "$PY_JAVA"
            ;;
        *)
            echo "用法: $0 {python|java|c|cpp|py_java}"
            echo "示例: $0 python  # 仅处理Python文件"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 