# CVE等价补丁收集与比较实验

本目录包含用于从 cve-c-patch.json 中提取CVE列表，并利用等价补丁搜集工具收集补丁，最后进行对比分析的脚本。

## 文件说明

- `cve-c-patch.json`: 包含CVE编号及其补丁信息的JSON文件
- `extract_cves.py`: 从JSON文件中提取CVE编号列表
- `collect_equivalent_patches.py`: 根据CVE列表收集等价补丁（使用main.py的patch-trace功能）
- `compare_patches.py`: 比较cve-c-patch.json与我们收集的等价补丁

## 使用流程

### 步骤1：提取CVE列表

从cve-c-patch.json中提取所有CVE编号并保存到文本文件中。

```bash
python3 extract_cves.py
```

输出：
- `cve_list.txt`: 包含所有从cve-c-patch.json中提取的CVE编号

### 步骤2：收集等价补丁

基于CVE列表，使用系统中的等价补丁收集功能收集各个CVE的等价补丁。

```bash
python3 collect_equivalent_patches.py [--cve_list cve_list.txt] [--output_dir equivalent_patch_results] [--limit 10] [--confidence_threshold 0.7] [--max_depth 3] [--skip_url_check] [--no-verify-ssl] [--no-priority] [--github_token YOUR_TOKEN] [--retry_failed]
```

参数说明：
- `--cve_list`: 包含CVE编号的文本文件路径，默认为`cve_list.txt`
- `--output_dir`: 保存结果的目录，默认为`equivalent_patch_results`
- `--limit`: 处理的CVE数量限制，用于测试，默认处理所有CVE
- `--confidence_threshold`: 补丁置信度阈值，默认为0.7
- `--max_depth`: 引用分析的最大深度，默认为3
- `--skip_url_check`: 跳过URL可访问性检查
- `--no-verify-ssl`: 禁用SSL证书验证
- `--no-priority`: 禁用引用优先级排序
- `--github_token`: 提供GitHub API令牌以增加API请求限额
- `--retry_failed`: 是否重试之前失败的CVE
- `--create_empty_results`: 如果找不到结果文件，是否创建空的等价补丁结果

输出：
- `equivalent_patch_results/`: 包含所有收集到的等价补丁数据
- `equivalent_patch_collection.log`: 日志文件
- `equivalent_patch_results/processed_cves.json`: 记录已处理的CVE
- `equivalent_patch_results/collection_report.txt`: 收集结果统计报告
```
cd /home/<USER>/with\ mystique/collect_equivalent_patches.py --cve_list compare/with\ mystique/data/cve_list.txt --limit 3 --no-verify-ssl --no-priority --skip_url_check --max_depth 5
```

### 步骤3：比较分析

比较cve-c-patch.json中的补丁数据与我们收集的等价补丁数据。

```bash
python3 compare_patches.py [--cve_c_patch cve-c-patch.json] [--our_results_dir equivalent_patch_results] [--output_dir comparison_results]
```

参数说明：
- `--cve_c_patch`: cve-c-patch.json文件路径，默认为`cve-c-patch.json`
- `--our_results_dir`: 我们的等价补丁结果目录，默认为`equivalent_patch_results`
- `--output_dir`: 比较结果输出目录，默认为`comparison_results`

输出：
- `comparison_results/`: 包含所有比较分析结果
- `comparison_results/repo_coverage.txt`: 仓库覆盖率分析
- `comparison_results/patch_matching.txt`: 补丁匹配率分析
- `comparison_results/patch_details.csv`: 详细的补丁匹配情况
- `comparison_results/patch_matching_charts.png`: 补丁匹配率可视化图表
- `comparison_results/repo_coverage_chart.png`: 仓库覆盖率对比图
- `comparison_results/comparison_results.json`: 完整的比较结果数据

```
cd /home/<USER>
python3 compare/with\ mystique/compare_patches.py --cve_c_patch compare/with\ mystique/data/cve-c-patch.json --our_results_dir data/equivalent_patch_results/ --output_dir data/comparison_results
```

## 需要的Python包

运行这些脚本需要以下Python包：
```
pandas
matplotlib
tqdm
```

可以通过以下命令安装：
```bash
pip install pandas matplotlib tqdm
```

## 实现说明

1. 本实现直接集成了`main.py`中的patch-trace功能，不再使用外部的patch_tracer.py脚本
2. 等价补丁收集过程可能会比较耗时，建议先使用`--limit`参数测试少量CVE
3. 如果收集过程中断，可以使用`--retry_failed`参数继续处理失败的CVE
4. 比较分析需要matplotlib包生成图表，如果缺少该包，会跳过图表生成但继续生成文本报告
5. 为了提高爬取成功率，`--no-verify-ssl`和`--no-priority`参数默认开启，分别用于禁用SSL证书验证和禁用引用优先级排序 