#!/usr/bin/env python3
import json
import os
import argparse
import logging
from tqdm import tqdm
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("patch_comparison.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("patch_comparator")

def setup_argparser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(description='比较两种不同方法收集的等价补丁')
    parser.add_argument('--cve_c_patch', type=str, default='cve-c-patch.json',
                        help='cve-c-patch.json文件路径')
    parser.add_argument('--our_results_dir', type=str, default='equivalent_patch_results',
                        help='我们的等价补丁结果目录')
    parser.add_argument('--output_dir', type=str, default='comparison_results',
                        help='比较结果输出目录')
    return parser

def load_cve_c_patch(file_path):
    """加载cve-c-patch.json数据"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        logger.info(f"从 {file_path} 中加载了 {len(data)} 个CVE的补丁数据")
        return data
    except Exception as e:
        logger.error(f"加载cve-c-patch.json时出错: {e}")
        return {}

def load_our_patch_results(results_dir, cve_list):
    """加载我们的补丁结果数据"""
    results = {}
    for cve_id in tqdm(cve_list, desc="加载我们的补丁数据"):
        result_file = os.path.join(results_dir, cve_id, f"{cve_id}_equivalent_patches.json")
        if os.path.exists(result_file):
            try:
                with open(result_file, 'r') as f:
                    data = json.load(f)
                if data:  # 确保数据不为空
                    results[cve_id] = data
            except Exception as e:
                logger.warning(f"加载 {cve_id} 的补丁数据时出错: {e}")
    
    logger.info(f"从 {results_dir} 中加载了 {len(results)} 个CVE的补丁数据")
    return results

def extract_repos_from_cve_c_patch(cve_c_patch_data):
    """从cve-c-patch数据中提取仓库信息"""
    repos = {}
    for cve_id, patch_info in cve_c_patch_data.items():
        owner = patch_info.get('owner', '')
        repo = patch_info.get('repo', '')
        if owner and repo:
            repo_key = f"{owner}/{repo}"
            if repo_key not in repos:
                repos[repo_key] = []
            repos[repo_key].append(cve_id)
    
    return repos

def extract_repos_from_our_results(our_results):
    """从我们的结果中提取仓库信息"""
    repos = {}
    
    for cve_id, patch_data in our_results.items():
        # 跳过处理非字典类型的数据
        if not isinstance(patch_data, dict):
            continue
            
        # 处理equivalent_patches结构
        if 'equivalent_patches' in patch_data:
            patch_urls = patch_data['equivalent_patches'].keys()
        else:
            patch_urls = patch_data.keys()
        
        for patch_url in patch_urls:
            # 提取仓库信息，支持多种URL格式
            repo_key = extract_repo_from_url(patch_url)
            if repo_key:
                if repo_key not in repos:
                    repos[repo_key] = []
                if cve_id not in repos[repo_key]:
                    repos[repo_key].append(cve_id)
    
    return repos

def extract_repo_from_url(url):
    """从URL中提取仓库信息"""
    # 处理GitHub URL格式
    if "github.com" in url:
        try:
            parts = url.split('github.com/')[1].split('/')
            if len(parts) >= 2:
                owner = parts[0]
                repo = parts[1]
                return f"{owner}/{repo}"
        except:
            pass
    
    # 处理其他git托管网站URL格式
    try:
        if "git" in url or "gitlab" in url or "gitweb" in url:
            # 从URL中提取域名作为owner
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            # 提取仓库名
            path = parsed_url.path
            query = parsed_url.query
            
            # 尝试从路径或查询参数中提取仓库名
            repo_name = None
            
            # 查找p=xxx.git模式
            if 'p=' in query:
                parts = query.split('p=')[1].split(';')[0].split('/')
                if parts:
                    repo_name = parts[-1].replace('.git', '')
            
            # 尝试从路径中提取
            elif path and len(path.split('/')) > 1:
                parts = [p for p in path.split('/') if p]
                if parts:
                    repo_name = parts[-1].replace('.git', '')
            
            if domain and repo_name:
                return f"{domain}/{repo_name}"
    except:
        pass
        
    # 如果无法提取仓库信息，返回整个URL作为唯一标识
    return url

def compare_repo_coverage(cve_c_patch_repos, our_repos):
    """比较两种方法覆盖的仓库差异"""
    cve_c_patch_repo_set = set(cve_c_patch_repos.keys())
    our_repo_set = set(our_repos.keys())
    
    common_repos = cve_c_patch_repo_set.intersection(our_repo_set)
    only_in_cve_c_patch = cve_c_patch_repo_set - our_repo_set
    only_in_our_results = our_repo_set - cve_c_patch_repo_set
    
    # 计算每个仓库包含的CVE数量
    common_repo_stats = []
    for repo in common_repos:
        cve_c_patch_count = len(cve_c_patch_repos[repo])
        our_count = len(our_repos[repo])
        common_repo_stats.append({
            'repo': repo,
            'cve_c_patch_count': cve_c_patch_count,
            'our_count': our_count,
            'difference': our_count - cve_c_patch_count
        })
    
    # 按照差异降序排序
    common_repo_stats.sort(key=lambda x: abs(x['difference']), reverse=True)
    
    return {
        'common_repos': common_repos,
        'only_in_cve_c_patch': only_in_cve_c_patch,
        'only_in_our_results': only_in_our_results,
        'common_repo_stats': common_repo_stats
    }

def compare_patch_details(cve_id, cve_c_patch_data, our_results):
    """比较特定CVE的补丁细节差异"""
    if cve_id not in cve_c_patch_data or cve_id not in our_results:
        return None
    
    cve_c_patch = cve_c_patch_data[cve_id]
    our_patches = our_results[cve_id]
    
    # 提取cve-c-patch中的origin和target
    c_patch_origin = cve_c_patch.get('origin', '')
    c_patch_target = cve_c_patch.get('target', '')
    c_patch_owner = cve_c_patch.get('owner', '')
    c_patch_repo = cve_c_patch.get('repo', '')
    c_patch_repo_full = f"{c_patch_owner}/{c_patch_repo}" if c_patch_owner and c_patch_repo else ""
    
    # 在我们的结果中查找相同的SHA
    origin_matched = False
    target_matched = False
    matched_urls = []
    
    # 检查our_patches的类型和结构
    if 'equivalent_patches' in our_patches:
        # 处理新格式
        for patch_url, patch_info in our_patches['equivalent_patches'].items():
            # 检查是否包含相同的仓库 - 使用更灵活的匹配逻辑
            url_repo_key = extract_repo_from_url(patch_url)
            
            # 如果cve-c-patch中有repo信息，尝试匹配
            if c_patch_repo_full:
                # 直接匹配仓库名
                if c_patch_repo in url_repo_key or c_patch_owner in url_repo_key:
                    matched_urls.append(patch_url)
                # 完整匹配
                elif c_patch_repo_full.lower() in url_repo_key.lower():
                    matched_urls.append(patch_url)
            
            # 检查原始补丁
            if isinstance(patch_info, dict):
                original_patch = patch_info.get('original_patch', {})
                if original_patch:
                    original_sha = original_patch.get('sha', '')
                    if original_sha and c_patch_origin and original_sha.startswith(c_patch_origin):
                        origin_matched = True
                
                # 检查目标补丁
                target_patch = patch_info.get('target_patch', {})
                if target_patch:
                    target_sha = target_patch.get('sha', '')
                    if target_sha and c_patch_target and target_sha.startswith(c_patch_target):
                        target_matched = True
    else:
        # 尝试处理其他可能的结构
        logger.warning(f"CVE {cve_id} 的补丁数据结构不符合预期")
        
        # 尝试处理JSON字符串或其他结构
        try:
            # 如果patch_info是字符串，尝试在URL中查找匹配
            for key, value in our_patches.items():
                # 尝试匹配仓库
                url_repo_key = extract_repo_from_url(key)
                if c_patch_repo_full:
                    if c_patch_repo in url_repo_key or c_patch_owner in url_repo_key:
                        matched_urls.append(key)
                    elif c_patch_repo_full.lower() in url_repo_key.lower():
                        matched_urls.append(key)
                
                # 如果value是字典类型，尝试查找sha
                if isinstance(value, dict):
                    # 尝试查找原始补丁
                    if 'sha' in value and value['sha'] and c_patch_origin and value['sha'].startswith(c_patch_origin):
                        origin_matched = True
                    
                    # 尝试在其他可能的位置查找sha
                    for subkey, subvalue in value.items():
                        if isinstance(subvalue, dict) and 'sha' in subvalue:
                            if subvalue['sha'] and c_patch_origin and subvalue['sha'].startswith(c_patch_origin):
                                origin_matched = True
                            if subvalue['sha'] and c_patch_target and subvalue['sha'].startswith(c_patch_target):
                                target_matched = True
                
                # 如果值是字符串，尝试直接匹配
                elif isinstance(value, str) and (
                    (c_patch_origin and c_patch_origin in value) or 
                    (c_patch_target and c_patch_target in value)
                ):
                    if c_patch_origin and c_patch_origin in value:
                        origin_matched = True
                    if c_patch_target and c_patch_target in value:
                        target_matched = True
        except Exception as e:
            logger.error(f"处理CVE {cve_id} 的补丁数据时出错: {e}")
    
    return {
        'cve_id': cve_id,
        'c_patch_repo': c_patch_repo_full,
        'c_patch_origin': c_patch_origin,
        'c_patch_target': c_patch_target,
        'origin_matched': origin_matched,
        'target_matched': target_matched,
        'matched_urls': matched_urls,
        'our_patch_count': len(our_patches.get('equivalent_patches', {})) if isinstance(our_patches, dict) and 'equivalent_patches' in our_patches else (len(our_patches) if isinstance(our_patches, dict) else 1)
    }

def generate_comparison_report(comparison_results, output_dir):
    """生成比较报告"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成仓库覆盖率比较报告
    repo_coverage = comparison_results['repo_coverage']
    
    # 1. 仓库统计
    with open(os.path.join(output_dir, 'repo_coverage.txt'), 'w') as f:
        f.write(f"共同覆盖的仓库数: {len(repo_coverage['common_repos'])}\n")
        f.write(f"仅在cve-c-patch中的仓库数: {len(repo_coverage['only_in_cve_c_patch'])}\n")
        f.write(f"仅在我们结果中的仓库数: {len(repo_coverage['only_in_our_results'])}\n\n")
        
        f.write("仅在cve-c-patch中的顶级仓库:\n")
        top_only_in_cve_c = sorted([(repo, len(comparison_results['cve_c_patch_repos'][repo])) 
                                    for repo in repo_coverage['only_in_cve_c_patch']], 
                                   key=lambda x: x[1], reverse=True)[:20]
        for repo, count in top_only_in_cve_c:
            f.write(f"{repo}: {count} CVEs\n")
        
        f.write("\n仅在我们结果中的顶级仓库:\n")
        top_only_in_ours = sorted([(repo, len(comparison_results['our_repos'][repo])) 
                                  for repo in repo_coverage['only_in_our_results']], 
                                 key=lambda x: x[1], reverse=True)[:20]
        for repo, count in top_only_in_ours:
            f.write(f"{repo}: {count} CVEs\n")
        
        f.write("\n共同覆盖的仓库中CVE数量差异最大的:\n")
        for stat in repo_coverage['common_repo_stats'][:20]:
            f.write(f"{stat['repo']}: cve-c-patch有{stat['cve_c_patch_count']}个CVE, 我们有{stat['our_count']}个CVE, 差异{stat['difference']}\n")
    
    # 2. 补丁匹配率统计
    patch_details = comparison_results['patch_details']
    matched_origin = sum(1 for detail in patch_details if detail['origin_matched'])
    matched_target = sum(1 for detail in patch_details if detail['target_matched'])
    matched_repo = sum(1 for detail in patch_details if detail['matched_urls'])
    
    with open(os.path.join(output_dir, 'patch_matching.txt'), 'w') as f:
        f.write(f"总比较的CVE数: {len(patch_details)}\n")
        f.write(f"原始补丁匹配数: {matched_origin} ({matched_origin/len(patch_details)*100:.2f}%)\n")
        f.write(f"目标补丁匹配数: {matched_target} ({matched_target/len(patch_details)*100:.2f}%)\n")
        f.write(f"仓库匹配数: {matched_repo} ({matched_repo/len(patch_details)*100:.2f}%)\n")
        
        # 统计我们找到的补丁数量分布
        patch_counts = [detail['our_patch_count'] for detail in patch_details]
        patch_count_avg = sum(patch_counts) / len(patch_counts)
        f.write(f"\n我们找到的平均补丁数: {patch_count_avg:.2f}\n")
        
        counter = Counter(patch_counts)
        f.write("\n补丁数量分布:\n")
        for count, freq in sorted(counter.items()):
            f.write(f"{count}个补丁: {freq}个CVE ({freq/len(patch_details)*100:.2f}%)\n")
    
    # 3. 生成详细的匹配情况CSV
    df = pd.DataFrame(patch_details)
    df.to_csv(os.path.join(output_dir, 'patch_details.csv'), index=False)
    
    # 4. 生成可视化图表
    try:
        # 饼图：补丁匹配情况
        plt.figure(figsize=(12, 8))
        plt.subplot(221)
        plt.pie([matched_origin, len(patch_details)-matched_origin], 
                labels=['Matched', 'Not Matched'], 
                autopct='%1.1f%%')
        plt.title('Original Patch Match Rate')
        
        plt.subplot(222)
        plt.pie([matched_target, len(patch_details)-matched_target], 
                labels=['Matched', 'Not Matched'], 
                autopct='%1.1f%%')
        plt.title('Target Patch Match Rate')
        
        plt.subplot(223)
        plt.pie([matched_repo, len(patch_details)-matched_repo], 
                labels=['Matched', 'Not Matched'], 
                autopct='%1.1f%%')
        plt.title('Repository Match Rate')
        
        # 我们找到的补丁数量分布
        plt.subplot(224)
        plt.hist(patch_counts, bins=10)
        plt.title('Patch Count Distribution per CVE')
        plt.xlabel('Number of Patches')
        plt.ylabel('Number of CVEs')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'patch_matching_charts.png'))
        plt.close()
        
        # 仓库覆盖率对比条形图
        plt.figure(figsize=(10, 6))
        categories = ['Common Coverage', 'Only in cve-c-patch', 'Only in Our Method']
        values = [len(repo_coverage['common_repos']), 
                  len(repo_coverage['only_in_cve_c_patch']), 
                  len(repo_coverage['only_in_our_results'])]
        
        plt.bar(categories, values)
        plt.title('Repository Coverage Comparison')
        plt.ylabel('Number of Repositories')
        plt.savefig(os.path.join(output_dir, 'repo_coverage_chart.png'))
        plt.close()
        
    except Exception as e:
        logger.warning(f"生成图表时出错: {e}")
    
    logger.info(f"比较报告已生成到 {output_dir} 目录")

def main():
    args = setup_argparser().parse_args()
    
    # 加载cve-c-patch.json数据
    cve_c_patch_data = load_cve_c_patch(args.cve_c_patch)
    if not cve_c_patch_data:
        logger.error("无法加载cve-c-patch.json数据，退出")
        return
    
    # 获取CVE列表
    cve_list = list(cve_c_patch_data.keys())
    
    # 加载我们的补丁结果
    our_results = load_our_patch_results(args.our_results_dir, cve_list)
    
    # 提取仓库信息
    cve_c_patch_repos = extract_repos_from_cve_c_patch(cve_c_patch_data)
    our_repos = extract_repos_from_our_results(our_results)
    
    # 比较仓库覆盖
    repo_coverage = compare_repo_coverage(cve_c_patch_repos, our_repos)
    
    # 详细比较相同CVE的补丁
    common_cves = set(cve_c_patch_data.keys()).intersection(set(our_results.keys()))
    patch_details = []
    
    for cve_id in tqdm(common_cves, desc="比较补丁细节"):
        detail = compare_patch_details(cve_id, cve_c_patch_data, our_results)
        if detail:
            patch_details.append(detail)
    
    # 组合所有比较结果
    comparison_results = {
        'cve_c_patch_total': len(cve_c_patch_data),
        'our_results_total': len(our_results),
        'common_cves': len(common_cves),
        'cve_c_patch_repos': cve_c_patch_repos,
        'our_repos': our_repos,
        'repo_coverage': repo_coverage,
        'patch_details': patch_details
    }
    
    # 生成比较报告
    generate_comparison_report(comparison_results, args.output_dir)
    
    # 保存原始比较结果
    with open(os.path.join(args.output_dir, 'comparison_results.json'), 'w') as f:
        # 将比较结果转换为可序列化的格式
        serializable_results = {
            'cve_c_patch_total': comparison_results['cve_c_patch_total'],
            'our_results_total': comparison_results['our_results_total'],
            'common_cves': comparison_results['common_cves'],
            'repo_coverage': {
                'common_repos_count': len(repo_coverage['common_repos']),
                'only_in_cve_c_patch_count': len(repo_coverage['only_in_cve_c_patch']),
                'only_in_our_results_count': len(repo_coverage['only_in_our_results']),
                'common_repo_stats': repo_coverage['common_repo_stats']
            },
            'patch_details': patch_details
        }
        json.dump(serializable_results, f, indent=2)
    
    logger.info("比较分析完成")

if __name__ == "__main__":
    main() 