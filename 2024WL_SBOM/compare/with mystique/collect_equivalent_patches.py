#!/usr/bin/env python3
import os
import json
import subprocess
import argparse
from tqdm import tqdm
import logging
import sys
import glob
import shutil
import re

# 将项目根目录添加到sys.path，以便导入src模块
project_root = "/home/<USER>"
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("equivalent_patch_collection.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("equivalent_patch_collector")

def setup_argparser():
    """设置命令行参数解析器"""
    parser = argparse.ArgumentParser(description='根据CVE列表收集等价补丁')
    parser.add_argument('--cve_list', type=str, default='cve_list.txt',
                        help='包含CVE编号的文本文件路径')
    parser.add_argument('--output_dir', type=str, default='data/equivalent_patch_results',
                        help='保存结果的目录')
    parser.add_argument('--limit', type=int, default=None,
                        help='处理的CVE数量限制（用于测试）')
    parser.add_argument('--use_main_py', action='store_true', default=True,
                        help='使用main.py中的patch-trace功能（默认启用）')
    parser.add_argument('--confidence_threshold', type=float, default=0.7,
                        help='补丁置信度阈值')
    parser.add_argument('--main_py_path', type=str, 
                        default=os.path.join(project_root, 'src/data_collection/main.py'),
                        help='main.py的路径')
    parser.add_argument('--retry_failed', action='store_true',
                        help='是否重试失败的CVE')
    parser.add_argument('--max_depth', type=int, default=3,
                       help='引用最大深度，默认为3')
    parser.add_argument('--skip_url_check', action='store_true',
                       help='跳过URL可访问性检查')
    parser.add_argument('--github_token', type=str, default=None,
                       help='GitHub API令牌')
    parser.add_argument('--create_empty_results', action='store_true', default=False,
                       help='如果找不到结果文件，是否创建空的等价补丁结果')
    # 添加新的命令行参数
    parser.add_argument('--no-verify-ssl', action='store_true',
                       help='禁用SSL证书验证')
    parser.add_argument('--no-priority', action='store_true',
                       help='禁用引用优先级排序')
    return parser

def load_cve_list(file_path):
    """从文件中加载CVE列表"""
    try:
        with open(file_path, 'r') as f:
            cve_list = [line.strip() for line in f if line.strip()]
        logger.info(f"从 {file_path} 中加载了 {len(cve_list)} 个CVE编号")
        return cve_list
    except Exception as e:
        logger.error(f"加载CVE列表时出错: {e}")
        return []

def collect_equivalent_patches_via_main(cve_id, output_dir, confidence_threshold, max_depth, skip_url_check, github_token, create_empty_results=False, no_verify_ssl=False, no_priority=False):
    """通过调用main.py的patch-trace功能收集等价补丁"""
    logger.info(f"开始处理 {cve_id} (使用main.py)")
    
    # 确保输出目录存在
    cve_output_dir = os.path.join(output_dir, cve_id)
    os.makedirs(cve_output_dir, exist_ok=True)
    
    # 构建命令 - 不再指定data-dir，使用main.py的默认路径
    cmd = [
        'python3', os.path.join(project_root, 'src/data_collection/main.py'),
        'patch-trace',
        '--cve', cve_id,
        '--confidence', str(confidence_threshold),
        '--depth', str(max_depth)
    ]
    
    # 添加可选参数
    if skip_url_check:
        cmd.append('--skip-url-check')
    
    if github_token:
        cmd.extend(['--github-token', github_token])
        
    # 添加新参数
    if no_verify_ssl:
        cmd.append('--no-verify-ssl')
        
    if no_priority:
        cmd.append('--no-priority')
    
    try:
        # 执行命令
        logger.info(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            text=True,
            timeout=900  # 15分钟超时
        )
        
        # 记录结果
        log_file = os.path.join(cve_output_dir, 'execution_log.txt')
        with open(log_file, 'w') as f:
            f.write(f"STDOUT:\n{result.stdout}\n\nSTDERR:\n{result.stderr}")
        
        # 检查命令是否成功
        if result.returncode != 0:
            logger.error(f"{cve_id} 处理失败，返回码: {result.returncode}")
            return False
        
        # 从执行日志中找到结果保存位置
        result_dir = None
        save_pattern = re.compile(r'结果保存在: ([^\n]+)')
        save_matches = save_pattern.findall(result.stdout)
        if save_matches:
            result_dir = save_matches[-1].strip()
            logger.info(f"从日志中找到结果保存位置: {result_dir}")
        
        # 定义所有可能的默认结果文件路径
        default_data_dirs = [
            # main.py的默认输出路径
            os.path.join(project_root, 'src/data_collection/data'),
            os.path.join(project_root, 'data'),
            # 从日志中提取的路径
            result_dir
        ]
        
        # 在每个可能的默认路径下查找CVE相关的目录
        possible_dirs = []
        for data_dir in default_data_dirs:
            if data_dir and os.path.exists(data_dir):
                # CVE编号的不同格式
                cve_dir_formats = [
                    cve_id.replace('-', '_'),  # 下划线格式
                    cve_id,                   # 短横线格式
                    cve_id.lower().replace('-', '_')  # 小写下划线格式
                ]
                
                for cve_format in cve_dir_formats:
                    possible_path = os.path.join(data_dir, cve_format)
                    if os.path.exists(possible_path):
                        possible_dirs.append(possible_path)
                
                # 也可能在data_dir下直接存在结果文件
                possible_dirs.append(data_dir)
                
                # 搜索任何包含CVE编号的子目录
                for subdir in glob.glob(os.path.join(data_dir, '*')):
                    if os.path.isdir(subdir) and (cve_id in subdir or cve_id.replace('-', '_') in subdir):
                        possible_dirs.append(subdir)
        
        # 移除None和重复项
        possible_dirs = [d for d in possible_dirs if d]
        possible_dirs = list(set(possible_dirs))
        
        # 所有可能的文件名
        possible_files = [
            'trace_result.json', 
            f'{cve_id}.json', 
            f"{cve_id.replace('-', '_')}.json",
            'tracer.json'
        ]
        
        # 尝试所有可能的路径组合
        trace_result_path = None
        for dir_path in possible_dirs:
            if os.path.exists(dir_path):
                for file_name in possible_files:
                    file_path = os.path.join(dir_path, file_name)
                    if os.path.exists(file_path):
                        trace_result_path = file_path
                        logger.info(f"找到结果文件: {trace_result_path}")
                        break
            if trace_result_path:
                break
                
        # 如果没找到，尝试查找任何JSON文件
        if not trace_result_path:
            logger.warning(f"在预定义位置找不到结果文件")
            
            # 查找所有json文件
            all_json_files = []
            for dir_path in possible_dirs:
                if os.path.exists(dir_path):
                    all_json_files.extend(glob.glob(os.path.join(dir_path, '*.json')))
            
            if all_json_files:
                trace_result_path = all_json_files[0]
                logger.info(f"找到JSON文件: {trace_result_path}")
            else:
                logger.warning("未找到任何JSON文件")
                
                # 如果我们有高置信度补丁信息，可以从日志中提取
                high_confidence_patches = []
                patch_pattern = re.compile(r'\[置信度: ([\d\.]+)\] (https://[^\n]+)')
                patch_matches = patch_pattern.findall(result.stdout)
                
                if patch_matches:
                    logger.info(f"从日志中提取到 {len(patch_matches)} 个高置信度补丁")
                    
                    # 构建等价补丁结果
                    patches_result = {}
                    for confidence, url in patch_matches:
                        patches_result[url] = {
                            "equivalent_patches": [
                                {
                                    "url": url,
                                    "confidence": float(confidence),
                                    "reason": "extracted_from_log"
                                }
                            ],
                            "original_patch": {
                                "url": url,
                                "confidence": float(confidence),
                                "reason": "extracted_from_log"
                            }
                        }
                    
                    # 保存提取的补丁信息
                    target_file = os.path.join(cve_output_dir, f"{cve_id}_equivalent_patches.json")
                    with open(target_file, 'w') as f:
                        json.dump(patches_result, f, indent=2)
                    logger.info(f"从日志中提取并保存了 {len(patches_result)} 个补丁到: {target_file}")
                    return True
                elif create_empty_results:
                    # 创建空结果
                    logger.warning(f"未找到任何补丁信息，创建空结果文件")
                    target_file = os.path.join(cve_output_dir, f"{cve_id}_equivalent_patches.json")
                    with open(target_file, 'w') as f:
                        json.dump({}, f, indent=2)
                    return False
                else:
                    logger.error(f"{cve_id} 处理完成，但未找到结果文件或补丁信息")
                    return False
        
        # 直接将找到的文件复制到标准位置
        target_file = os.path.join(cve_output_dir, f"{cve_id}_equivalent_patches.json")
        logger.info(f"将 {trace_result_path} 复制到 {target_file}")
        
        try:
            # 复制文件
            shutil.copy2(trace_result_path, target_file)
            logger.info(f"文件复制成功")
            
            # 简单验证结果文件是否包含补丁信息
            with open(target_file, 'r') as f:
                data = json.load(f)
                if data and ('equivalent_patches' in data or 'high_confidence_patches' in data):
                    logger.info(f"{cve_id}成功生成等价补丁结果文件")
                    # 如果文件有补丁信息，返回成功
                    return True
                else:
                    logger.warning(f"{cve_id}的结果文件不包含补丁信息")
                    return False
                
        except Exception as e:
            logger.error(f"复制或验证文件时出错: {e}")
            return False
        
    except subprocess.TimeoutExpired:
        logger.error(f"{cve_id} 处理超时")
        return False
    except Exception as e:
        logger.error(f"{cve_id} 处理时出现异常: {e}")
        return False

def main():
    """主函数"""
    args = setup_argparser().parse_args()
    
    # 加载CVE列表
    cve_list = load_cve_list(args.cve_list)
    if not cve_list:
        logger.error("CVE列表为空，退出")
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 如果设置了限制，只处理指定数量的CVE
    if args.limit:
        cve_list = cve_list[:args.limit]
        logger.info(f"设置了CVE限制，将仅处理 {args.limit} 个CVE")
    
    # 记录成功和失败的CVE
    successful_cves = []
    failed_cves = []
    
    # 加载之前已经处理过的CVE
    processed_cves_file = os.path.join(args.output_dir, 'processed_cves.json')
    if os.path.exists(processed_cves_file):
        with open(processed_cves_file, 'r') as f:
            processed_data = json.load(f)
            if not args.retry_failed:
                # 排除已处理的CVE
                cve_list = [cve for cve in cve_list if cve not in processed_data.get('successful', []) 
                                                  and cve not in processed_data.get('failed', [])]
            else:
                # 只重试失败的CVE
                successful_cves = processed_data.get('successful', [])
                cve_list = [cve for cve in cve_list if cve not in successful_cves]
    
    # 处理每个CVE
    for cve_id in tqdm(cve_list, desc="处理CVE"):
        # 使用main.py的patch-trace功能
        if args.use_main_py:
            result = collect_equivalent_patches_via_main(
                cve_id, 
                args.output_dir,
                args.confidence_threshold,
                args.max_depth,
                args.skip_url_check,
                args.github_token,
                args.create_empty_results,
                getattr(args, 'no_verify_ssl', True),  # 传递新参数
                getattr(args, 'no_priority', True)     # 传递新参数
            )
        
        if result:
            successful_cves.append(cve_id)
        else:
            failed_cves.append(cve_id)
        
        # 定期保存处理结果
        if (len(successful_cves) + len(failed_cves)) % 10 == 0:
            with open(processed_cves_file, 'w') as f:
                json.dump({
                    'successful': successful_cves,
                    'failed': failed_cves
                }, f, indent=2)
    
    # 保存最终处理结果
    with open(processed_cves_file, 'w') as f:
        json.dump({
            'successful': successful_cves,
            'failed': failed_cves
        }, f, indent=2)
    
    logger.info(f"处理完成。成功: {len(successful_cves)}，失败: {len(failed_cves)}")
    
    # 生成简单的统计报告
    with open(os.path.join(args.output_dir, 'collection_report.txt'), 'w') as f:
        f.write(f"总处理CVE数: {len(successful_cves) + len(failed_cves)}\n")
        f.write(f"成功数: {len(successful_cves)}\n")
        f.write(f"失败数: {len(failed_cves)}\n")
        
        if len(successful_cves) + len(failed_cves) > 0:
            f.write(f"成功率: {len(successful_cves)/(len(successful_cves) + len(failed_cves)) * 100:.2f}%\n")
        else:
            f.write("成功率: 0%\n")
        
        f.write("\n\n成功的CVE列表:\n")
        for cve in successful_cves:
            f.write(f"{cve}\n")
            
        f.write("\n\n失败的CVE列表:\n")
        for cve in failed_cves:
            f.write(f"{cve}\n")

if __name__ == "__main__":
    main() 