#!/usr/bin/env python3
import json
import os

def extract_cves(json_file_path, output_file_path):
    """从JSON文件中提取所有CVE编号并保存到文本文件中"""
    try:
        # 读取JSON文件
        with open(json_file_path, 'r') as f:
            data = json.load(f)
        
        # 提取所有CVE编号
        cve_list = list(data.keys())
        
        # 按字母数字顺序排序
        cve_list.sort()
        
        # 保存到文本文件
        with open(output_file_path, 'w') as f:
            for cve in cve_list:
                f.write(f"{cve}\n")
        
        print(f"成功提取了 {len(cve_list)} 个CVE编号，并保存到 {output_file_path}")
        return cve_list
    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        return []

if __name__ == "__main__":
    # 设置文件路径 - 直接使用相对路径，因为脚本已在目标目录中
    json_file_path = "cve-c-patch.json"
    output_file_path = "cve_list.txt"
    
    # 确保输入文件存在
    if not os.path.exists(json_file_path):
        print(f"错误: 文件 {json_file_path} 不存在")
    else:
        # 提取CVE编号
        cve_list = extract_cves(json_file_path, output_file_path)
        
        # 输出一些统计信息
        if cve_list:
            print(f"总共提取了 {len(cve_list)} 个CVE编号")
            print(f"前10个CVE编号: {', '.join(cve_list[:10])}") 